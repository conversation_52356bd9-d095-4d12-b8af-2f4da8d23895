<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>org.jeecgframework.boot</groupId>
		<artifactId>jeecg-boot-parent</artifactId>
		<version>2.4.6</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>jeecg-boot-module-system</artifactId>

	<repositories>
		<repository>
			<id>aliyun</id>
			<name>aliyun Repository</name>
			<url>https://maven.aliyun.com/repository/public</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>jeecg</id>
			<name>jeecg Repository</name>
			<url>https://maven.jeecg.org/nexus/content/repositories/jeecg</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>

	<dependencies>
		<dependency>
			<groupId>org.jeecgframework.boot</groupId>
			<artifactId>jeecg-system-local-api</artifactId>
		</dependency>
		<!-- jeewx api -->
		<dependency>
			<groupId>org.jeecgframework</groupId>
			<artifactId>jeewx-api</artifactId>
			<version>1.4.6</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-beanutils</artifactId>
					<groupId>commons-beanutils</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 积木报表 -->
		<dependency>
			<groupId>org.jeecgframework.jimureport</groupId>
			<artifactId>jimureport-spring-boot-starter</artifactId>
			<version>1.3.78</version>
		</dependency>


		<!-- DEMO 示例模块【微服务启动请注释掉】 -->
		<!--
		<dependency>
			<groupId>org.jeecgframework.boot</groupId>
			<artifactId>jeecg-boot-module-demo</artifactId>
			<version>2.4.6</version>
		</dependency>
		-->

		<!-- ZXing 二维码生成 -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.5.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>3.5.1</version>
		</dependency>

		<!-- 敏感词检测库 -->
		<dependency>
			<groupId>com.github.houbb</groupId>
			<artifactId>sensitive-word</artifactId>
			<version>0.26.0</version>
		</dependency>

		<!-- OkHttp 强力HTTP客户端 -->
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>4.12.0</version>
		</dependency>

		<!-- 🔥 火山引擎TOS对象存储SDK -->
		<dependency>
			<groupId>com.volcengine</groupId>
			<artifactId>ve-tos-java-sdk</artifactId>
			<version>2.8.5</version>
		</dependency>

		<!-- 🔐 微信开发工具包 - 暂时注释避免编译错误 -->
		<!--
		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-mp</artifactId>
			<version>4.1.0</version>
		</dependency>

		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-common</artifactId>
			<version>4.1.0</version>
		</dependency>
		-->

		<!-- 💰 支付宝SDK -->
		<dependency>
			<groupId>com.alipay.sdk</groupId>
			<artifactId>alipay-sdk-java</artifactId>
			<version>4.38.200.ALL</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<!--微服务模式下修改为true,跳过此打包插件，否则微服务模块无法引用-->
					<skip>false</skip>
					<mainClass>org.jeecg.JeecgSystemApplication</mainClass>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>