package org.jeecg.modules.system.service.impl;

import org.jeecg.modules.system.service.IInviteCodeGeneratorService;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.jeecg.common.util.oConvertUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Random;

/**
 * @Description: 邀请码生成服务实现类
 * @Author: jeecg-boot
 * @Date: 2025-01-27
 * @Version: V1.0
 */
@Slf4j
@Service
public class InviteCodeGeneratorServiceImpl implements IInviteCodeGeneratorService {

    @Autowired
    private IAicgUserProfileService userProfileService;

    /**
     * 邀请码格式常量
     */
    private static final String ZJ_FORMAT = "ZJ_FORMAT";
    private static final String INV_FORMAT = "INV_FORMAT";
    private static final String RANDOM_FORMAT = "RANDOM_FORMAT";
    private static final String INVALID_FORMAT = "INVALID";

    /**
     * ZJ格式邀请码前缀
     */
    private static final String ZJ_PREFIX = "ZJ";

    /**
     * 最大重试次数
     */
    private static final int MAX_ATTEMPTS = 10;

    @Override
    public String generateOrGetInviteCode(String userId) {
        if (oConvertUtils.isEmpty(userId)) {
            log.error("用户ID不能为空");
            throw new IllegalArgumentException("用户ID不能为空");
        }

        try {
            // 检查用户是否已有邀请码
            AicgUserProfile profile = userProfileService.getByUserId(userId);
            if (profile != null && oConvertUtils.isNotEmpty(profile.getMyInviteCode())) {
                log.info("用户 {} 已有邀请码: {}", userId, profile.getMyInviteCode());
                return profile.getMyInviteCode();
            }

            // 如果用户存在但没有邀请码，生成新的ZJ格式邀请码并保存
            if (profile != null) {
                String newInviteCode = generateZJFormatInviteCode(userId);
                profile.setMyInviteCode(newInviteCode);
                userProfileService.updateById(profile);
                log.info("为用户 {} 生成并保存新的ZJ格式邀请码: {}", userId, newInviteCode);
                return newInviteCode;
            } else {
                // 用户不存在的情况（理论上不应该发生）
                log.error("用户 {} 不存在，无法生成邀请码", userId);
                throw new RuntimeException("用户不存在");
            }

        } catch (Exception e) {
            log.error("为用户 {} 生成邀请码失败: {}", userId, e.getMessage(), e);
            throw new RuntimeException("生成邀请码失败", e);
        }
    }

    @Override
    public String generateZJFormatInviteCode(String userId) {
        for (int attempt = 0; attempt < MAX_ATTEMPTS; attempt++) {
            try {
                // 生成3位序号（基于时间戳确保相对唯一性）
                long timestamp = System.currentTimeMillis() % 1000;
                String sequence = String.format("%03d", timestamp);

                // 生成3位随机校验码
                String checkCode = generateRandomCheckCode(3);

                // 组合成ZJ格式邀请码
                String inviteCode = ZJ_PREFIX + sequence + checkCode;

                // 检查唯一性
                if (!isInviteCodeExists(inviteCode)) {
                    log.info("成功生成ZJ格式邀请码: {} (尝试次数: {})", inviteCode, attempt + 1);
                    return inviteCode;
                }

                log.debug("邀请码 {} 已存在，重新生成 (尝试次数: {})", inviteCode, attempt + 1);

            } catch (Exception e) {
                log.warn("第 {} 次生成邀请码失败: {}", attempt + 1, e.getMessage());
            }
        }

        // 如果随机生成失败，使用用户ID生成备用邀请码
        String fallbackCode = generateFallbackInviteCode(userId);
        log.warn("邀请码生成使用备用方案，用户: {}, 邀请码: {}", userId, fallbackCode);
        return fallbackCode;
    }

    @Override
    public String validateInviteCodeFormat(String inviteCode) {
        if (oConvertUtils.isEmpty(inviteCode)) {
            return INVALID_FORMAT;
        }

        if (inviteCode.startsWith(ZJ_PREFIX) && inviteCode.length() == 8) {
            return ZJ_FORMAT;
        }

        if (inviteCode.startsWith("INV")) {
            return INV_FORMAT;
        }

        if (inviteCode.matches("^[A-Z0-9]+$") && inviteCode.length() >= 6 && inviteCode.length() <= 10) {
            return RANDOM_FORMAT;
        }

        return INVALID_FORMAT;
    }

    @Override
    public boolean isInviteCodeExists(String inviteCode) {
        try {
            AicgUserProfile profile = userProfileService.getByInviteCode(inviteCode);
            return profile != null;
        } catch (Exception e) {
            log.error("检查邀请码 {} 是否存在时发生错误: {}", inviteCode, e.getMessage());
            return true; // 出错时返回true，避免生成重复邀请码
        }
    }

    @Override
    public String generateRandomCheckCode(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder code = new StringBuilder();

        for (int i = 0; i < length; i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }

        return code.toString();
    }

    /**
     * 生成备用邀请码
     * 当随机生成失败时使用
     */
    private String generateFallbackInviteCode(String userId) {
        String userSuffix = userId.length() >= 3 ? 
            userId.substring(userId.length() - 3) : userId;
        String randomCode = generateRandomCheckCode(3);
        return ZJ_PREFIX + userSuffix + randomCode;
    }
}
