<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0"><dict><key>CFBundleDisplayName</key><string>剪映小助手-智界</string><key>CFBundleExecutable</key><string>剪映小助手-智界</string><key>CFBundleIconFile</key><string>icon.icns</string><key>CFBundleIdentifier</key><string>com.aigcview.jianying-assistant</string><key>CFBundleInfoDictionaryVersion</key><string>6.0</string><key>CFBundleName</key><string>剪映小助手-智界</string><key>CFBundlePackageType</key><string>APPL</string><key>CFBundleShortVersionString</key><string>1.0.3</string><key>CFBundleVersion</key><string>1.0.3</string><key>DTCompiler</key><string>com.apple.compilers.llvm.clang.1_0</string><key>DTSDKBuild</key><string>21E226</string><key>DTSDKName</key><string>macosx12.3</string><key>DTXcode</key><string>1341</string><key>DTXcodeBuild</key><string>13F100</string><key>ElectronAsarIntegrity</key><dict><key>Resources/app.asar</key><dict><key>algorithm</key><string>SHA256</string><key>hash</key><string>9804be499b8544a6cf99f84d9be1613ec718cde2523bc616a8d02976f42b32d0</string></dict></dict><key>LSApplicationCategoryType</key><string>public.app-category.developer-tools</string><key>LSEnvironment</key><dict><key>MallocNanoZone</key><string>0</string></dict><key>LSMinimumSystemVersion</key><string>10.13</string><key>NSAppTransportSecurity</key><dict><key>NSAllowsArbitraryLoads</key><true/><key>NSAllowsLocalNetworking</key><true/><key>NSExceptionDomains</key><dict><key>127.0.0.1</key><dict><key>NSIncludesSubdomains</key><false/><key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key><true/><key>NSTemporaryExceptionAllowsInsecureHTTPSLoads</key><false/><key>NSTemporaryExceptionMinimumTLSVersion</key><string>1.0</string><key>NSTemporaryExceptionRequiresForwardSecrecy</key><false/></dict><key>localhost</key><dict><key>NSIncludesSubdomains</key><false/><key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key><true/><key>NSTemporaryExceptionAllowsInsecureHTTPSLoads</key><false/><key>NSTemporaryExceptionMinimumTLSVersion</key><string>1.0</string><key>NSTemporaryExceptionRequiresForwardSecrecy</key><false/></dict></dict></dict><key>NSBluetoothAlwaysUsageDescription</key><string>This app needs access to Bluetooth</string><key>NSBluetoothPeripheralUsageDescription</key><string>This app needs access to Bluetooth</string><key>NSCameraUsageDescription</key><string>This app needs access to the camera</string><key>NSHighResolutionCapable</key><true/><key>NSHumanReadableCopyright</key><string>Copyright © 2025 智界AigcView</string><key>NSMainNibFile</key><string>MainMenu</string><key>NSMicrophoneUsageDescription</key><string>This app needs access to the microphone</string><key>NSPrincipalClass</key><string>AtomApplication</string><key>NSQuitAlwaysKeepsWindows</key><false/><key>NSRequiresAquaSystemAppearance</key><false/><key>NSSupportsAutomaticGraphicsSwitching</key><true/></dict></plist>