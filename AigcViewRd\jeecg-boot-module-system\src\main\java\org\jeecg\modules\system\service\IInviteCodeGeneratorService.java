package org.jeecg.modules.system.service;

/**
 * @Description: 邀请码生成服务接口
 * @Author: jeecg-boot
 * @Date: 2025-01-27
 * @Version: V1.0
 */
public interface IInviteCodeGeneratorService {

    /**
     * 为用户生成或获取邀请码
     * 如果用户已有邀请码，直接返回现有的
     * 如果用户没有邀请码，生成新的ZJ格式邀请码
     * 
     * @param userId 用户ID
     * @return 邀请码
     */
    String generateOrGetInviteCode(String userId);

    /**
     * 生成新的ZJ格式邀请码
     * 格式：ZJ + 3位序号 + 3位随机码
     * 
     * @param userId 用户ID
     * @return ZJ格式邀请码
     */
    String generateZJFormatInviteCode(String userId);

    /**
     * 验证邀请码格式
     * 
     * @param inviteCode 邀请码
     * @return 邀请码格式类型：ZJ_FORMAT, INV_FORMAT, RANDOM_FORMAT, INVALID
     */
    String validateInviteCodeFormat(String inviteCode);

    /**
     * 检查邀请码是否已存在
     * 
     * @param inviteCode 邀请码
     * @return 是否存在
     */
    boolean isInviteCodeExists(String inviteCode);

    /**
     * 生成随机校验码
     * 
     * @param length 长度
     * @return 随机校验码
     */
    String generateRandomCheckCode(int length);
}
