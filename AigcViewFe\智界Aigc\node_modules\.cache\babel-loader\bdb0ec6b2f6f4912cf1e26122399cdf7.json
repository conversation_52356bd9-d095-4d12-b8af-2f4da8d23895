{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue", "mtime": 1753663019793}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WebsitePage from '@/components/website/WebsitePage.vue';\nimport { getReferralStats, generateReferralLink } from '@/api/usercenter';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport Vue from 'vue';\nexport default {\n  name: 'Affiliate',\n  components: {\n    WebsitePage: WebsitePage\n  },\n  data: function data() {\n    return {\n      loading: true,\n      qrLoading: false,\n      // 收益数据\n      totalEarnings: 0,\n      totalReferrals: 0,\n      memberReferrals: 0,\n      conversionRate: 0,\n      // 推广链接\n      affiliateLink: '',\n      // 佣金等级\n      userRole: 'NORMAL',\n      // NORMAL, VIP, SVIP\n      currentCommissionRate: 30,\n      commissionLevelText: '新手推广员',\n      levelProgress: 0,\n      nextLevelRequirement: 10,\n      nextLevelText: '高级推广员',\n      nextLevelRate: 40,\n      progressColor: '#1890ff',\n      // 二维码\n      showQRModal: false,\n      qrCodeUrl: '',\n      // 用户信息\n      userInfo: null\n    };\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return this.checkLoginAndLoadData();\n\n            case 2:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  methods: {\n    // 检查登录状态并加载数据\n    checkLoginAndLoadData: function () {\n      var _checkLoginAndLoadData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var token;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                token = Vue.ls.get(ACCESS_TOKEN);\n\n                if (token) {\n                  _context2.next = 4;\n                  break;\n                }\n\n                this.$router.push({\n                  path: '/login',\n                  query: {\n                    redirect: this.$route.fullPath\n                  }\n                });\n                return _context2.abrupt(\"return\");\n\n              case 4:\n                _context2.prev = 4;\n                _context2.next = 7;\n                return Promise.all([this.loadReferralData(), this.loadReferralLink(), this.loadUserRole()]);\n\n              case 7:\n                // 计算佣金等级\n                this.calculateCommissionLevel();\n                _context2.next = 14;\n                break;\n\n              case 10:\n                _context2.prev = 10;\n                _context2.t0 = _context2[\"catch\"](4);\n                console.error('加载分销数据失败:', _context2.t0);\n                this.$notification.error({\n                  message: '加载失败',\n                  description: '获取分销数据失败，请稍后重试',\n                  placement: 'topRight'\n                });\n\n              case 14:\n                _context2.prev = 14;\n                this.loading = false;\n                return _context2.finish(14);\n\n              case 17:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[4, 10, 14, 17]]);\n      }));\n\n      function checkLoginAndLoadData() {\n        return _checkLoginAndLoadData.apply(this, arguments);\n      }\n\n      return checkLoginAndLoadData;\n    }(),\n    // 加载推荐统计数据\n    loadReferralData: function () {\n      var _loadReferralData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var response, data;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                _context3.next = 3;\n                return getReferralStats();\n\n              case 3:\n                response = _context3.sent;\n\n                if (response.success) {\n                  data = response.result;\n                  this.totalEarnings = data.total_reward_amount || 0;\n                  this.totalReferrals = data.total_referrals || 0;\n                  this.memberReferrals = data.member_referrals || 0; // 计算会员转化率：会员转化数 / 总注册数 * 100\n\n                  this.conversionRate = this.totalReferrals > 0 ? (this.memberReferrals / this.totalReferrals * 100).toFixed(1) : 0;\n                }\n\n                _context3.next = 11;\n                break;\n\n              case 7:\n                _context3.prev = 7;\n                _context3.t0 = _context3[\"catch\"](0);\n                console.error('获取推荐统计失败:', _context3.t0);\n                throw _context3.t0;\n\n              case 11:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 7]]);\n      }));\n\n      function loadReferralData() {\n        return _loadReferralData.apply(this, arguments);\n      }\n\n      return loadReferralData;\n    }(),\n    // 加载推荐链接\n    loadReferralLink: function () {\n      var _loadReferralLink = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n                _context4.next = 3;\n                return generateReferralLink({});\n\n              case 3:\n                response = _context4.sent;\n\n                if (response.success) {\n                  this.affiliateLink = response.result || '';\n                }\n\n                _context4.next = 11;\n                break;\n\n              case 7:\n                _context4.prev = 7;\n                _context4.t0 = _context4[\"catch\"](0);\n                console.error('获取推荐链接失败:', _context4.t0); // 如果获取失败，使用默认链接格式\n\n                this.affiliateLink = \"\".concat(window.location.origin, \"?ref=loading...\");\n\n              case 11:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[0, 7]]);\n      }));\n\n      function loadReferralLink() {\n        return _loadReferralLink.apply(this, arguments);\n      }\n\n      return loadReferralLink;\n    }(),\n    // 加载用户角色信息\n    loadUserRole: function () {\n      var _loadUserRole = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                try {\n                  // TODO: 从用户信息API获取用户角色\n                  // 暂时使用默认值\n                  this.userRole = 'NORMAL';\n                } catch (error) {\n                  console.error('获取用户角色失败:', error);\n                  this.userRole = 'NORMAL';\n                }\n\n              case 1:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this);\n      }));\n\n      function loadUserRole() {\n        return _loadUserRole.apply(this, arguments);\n      }\n\n      return loadUserRole;\n    }(),\n    // 计算佣金等级和进度\n    calculateCommissionLevel: function calculateCommissionLevel() {\n      var memberCount = this.memberReferrals;\n\n      if (this.userRole === 'SVIP') {\n        this.currentCommissionRate = 50;\n        this.commissionLevelText = 'SVIP推广员';\n        this.levelProgress = 100;\n        this.nextLevelRequirement = 0;\n        this.nextLevelText = '已达最高等级';\n        this.nextLevelRate = 50;\n        this.progressColor = '#722ed1';\n      } else if (this.userRole === 'VIP') {\n        if (memberCount >= 30) {\n          this.currentCommissionRate = 50;\n          this.commissionLevelText = 'VIP顶级推广员';\n          this.levelProgress = 100;\n          this.nextLevelRequirement = 0;\n          this.nextLevelText = '已达最高等级';\n          this.nextLevelRate = 50;\n          this.progressColor = '#722ed1';\n        } else if (memberCount >= 10) {\n          this.currentCommissionRate = 45;\n          this.commissionLevelText = 'VIP高级推广员';\n          this.levelProgress = memberCount / 30 * 100;\n          this.nextLevelRequirement = 30;\n          this.nextLevelText = 'VIP顶级推广员';\n          this.nextLevelRate = 50;\n          this.progressColor = '#13c2c2';\n        } else {\n          this.currentCommissionRate = 35;\n          this.commissionLevelText = 'VIP推广员';\n          this.levelProgress = memberCount / 10 * 100;\n          this.nextLevelRequirement = 10;\n          this.nextLevelText = 'VIP高级推广员';\n          this.nextLevelRate = 45;\n          this.progressColor = '#1890ff';\n        }\n      } else {\n        // NORMAL用户\n        if (memberCount >= 30) {\n          this.currentCommissionRate = 50;\n          this.commissionLevelText = '顶级推广员';\n          this.levelProgress = 100;\n          this.nextLevelRequirement = 0;\n          this.nextLevelText = '已达最高等级';\n          this.nextLevelRate = 50;\n          this.progressColor = '#722ed1';\n        } else if (memberCount >= 10) {\n          this.currentCommissionRate = 40;\n          this.commissionLevelText = '高级推广员';\n          this.levelProgress = memberCount / 30 * 100;\n          this.nextLevelRequirement = 30;\n          this.nextLevelText = '顶级推广员';\n          this.nextLevelRate = 50;\n          this.progressColor = '#13c2c2';\n        } else {\n          this.currentCommissionRate = 30;\n          this.commissionLevelText = '新手推广员';\n          this.levelProgress = memberCount / 10 * 100;\n          this.nextLevelRequirement = 10;\n          this.nextLevelText = '高级推广员';\n          this.nextLevelRate = 40;\n          this.progressColor = '#1890ff';\n        }\n      }\n    },\n    // 复制推广链接\n    copyLink: function copyLink() {\n      var _this = this;\n\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '推广链接未生成',\n          description: '推广链接正在生成中，请稍后再试',\n          placement: 'topRight'\n        });\n        return;\n      }\n\n      navigator.clipboard.writeText(this.affiliateLink).then(function () {\n        _this.$notification.success({\n          message: '推广链接已复制',\n          description: '推广链接已成功复制到剪贴板，快去分享给好友吧！',\n          placement: 'topRight',\n          duration: 3,\n          style: {\n            width: '380px',\n            marginTop: '101px',\n            borderRadius: '8px',\n            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n          }\n        });\n      }).catch(function () {\n        _this.$notification.error({\n          message: '复制失败',\n          description: '复制推广链接失败，请手动复制',\n          placement: 'topRight'\n        });\n      });\n    },\n    // 生成推广二维码\n    generateQRCode: function () {\n      var _generateQRCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6() {\n        var qrApiUrl;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                if (this.affiliateLink) {\n                  _context6.next = 3;\n                  break;\n                }\n\n                this.$notification.warning({\n                  message: '推广链接未生成',\n                  description: '请等待推广链接生成完成后再生成二维码',\n                  placement: 'topRight'\n                });\n                return _context6.abrupt(\"return\");\n\n              case 3:\n                try {\n                  this.qrLoading = true; // 使用第三方二维码生成服务\n\n                  qrApiUrl = \"https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=\".concat(encodeURIComponent(this.affiliateLink));\n                  this.qrCodeUrl = qrApiUrl;\n                  this.showQRModal = true;\n                  this.$notification.success({\n                    message: '二维码生成成功',\n                    description: '推广二维码已生成，可以下载保存',\n                    placement: 'topRight'\n                  });\n                } catch (error) {\n                  console.error('生成二维码失败:', error);\n                  this.$notification.error({\n                    message: '生成失败',\n                    description: '二维码生成失败，请稍后重试',\n                    placement: 'topRight'\n                  });\n                } finally {\n                  this.qrLoading = false;\n                }\n\n              case 4:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this);\n      }));\n\n      function generateQRCode() {\n        return _generateQRCode.apply(this, arguments);\n      }\n\n      return generateQRCode;\n    }(),\n    // 下载二维码\n    downloadQRCode: function downloadQRCode() {\n      if (!this.qrCodeUrl) return;\n      var link = document.createElement('a');\n      link.href = this.qrCodeUrl;\n      link.download = '智界AIGC推广二维码.png';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.$notification.success({\n        message: '下载成功',\n        description: '二维码已保存到本地',\n        placement: 'topRight'\n      });\n    },\n    // 格式化数字显示\n    formatNumber: function formatNumber(num) {\n      if (num === null || num === undefined) return '0';\n      var number = parseFloat(num);\n      if (isNaN(number)) return '0'; // 如果是金额，保留两位小数\n\n      if (num === this.totalEarnings) {\n        return number.toLocaleString('zh-CN', {\n          minimumFractionDigits: 2,\n          maximumFractionDigits: 2\n        });\n      } // 其他数字不保留小数\n\n\n      return number.toLocaleString('zh-CN');\n    }\n  }\n};", {"version": 3, "sources": ["Affiliate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgMA,OAAA,WAAA,MAAA,sCAAA;AACA,SAAA,gBAAA,EAAA,oBAAA,QAAA,kBAAA;AACA,SAAA,YAAA,QAAA,wBAAA;AACA,OAAA,GAAA,MAAA,KAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,WADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,WAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,IADA;AAEA,MAAA,SAAA,EAAA,KAFA;AAIA;AACA,MAAA,aAAA,EAAA,CALA;AAMA,MAAA,cAAA,EAAA,CANA;AAOA,MAAA,eAAA,EAAA,CAPA;AAQA,MAAA,cAAA,EAAA,CARA;AAUA;AACA,MAAA,aAAA,EAAA,EAXA;AAaA;AACA,MAAA,QAAA,EAAA,QAdA;AAcA;AACA,MAAA,qBAAA,EAAA,EAfA;AAgBA,MAAA,mBAAA,EAAA,OAhBA;AAiBA,MAAA,aAAA,EAAA,CAjBA;AAkBA,MAAA,oBAAA,EAAA,EAlBA;AAmBA,MAAA,aAAA,EAAA,OAnBA;AAoBA,MAAA,aAAA,EAAA,EApBA;AAqBA,MAAA,aAAA,EAAA,SArBA;AAuBA;AACA,MAAA,WAAA,EAAA,KAxBA;AAyBA,MAAA,SAAA,EAAA,EAzBA;AA2BA;AACA,MAAA,QAAA,EAAA;AA5BA,KAAA;AA8BA,GApCA;AAqCA,EAAA,OArCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAsCA,KAAA,qBAAA,EAtCA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAwCA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,qBAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,gBAAA,KAHA,GAGA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,YAAA,CAHA;;AAAA,oBAIA,KAJA;AAAA;AAAA;AAAA;;AAKA,qBAAA,OAAA,CAAA,IAAA,CAAA;AAAA,kBAAA,IAAA,EAAA,QAAA;AAAA,kBAAA,KAAA,EAAA;AAAA,oBAAA,QAAA,EAAA,KAAA,MAAA,CAAA;AAAA;AAAA,iBAAA;AALA;;AAAA;AAAA;AAAA;AAAA,uBAUA,OAAA,CAAA,GAAA,CAAA,CACA,KAAA,gBAAA,EADA,EAEA,KAAA,gBAAA,EAFA,EAGA,KAAA,YAAA,EAHA,CAAA,CAVA;;AAAA;AAgBA;AACA,qBAAA,wBAAA;AAjBA;AAAA;;AAAA;AAAA;AAAA;AAmBA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,aAAA,CAAA,KAAA,CAAA;AACA,kBAAA,OAAA,EAAA,MADA;AAEA,kBAAA,WAAA,EAAA,gBAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;;AApBA;AAAA;AA0BA,qBAAA,OAAA,GAAA,KAAA;AA1BA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA8BA;AACA,IAAA,gBA/BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAiCA,gBAAA,EAjCA;;AAAA;AAiCA,gBAAA,QAjCA;;AAkCA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,kBAAA,IADA,GACA,QAAA,CAAA,MADA;AAEA,uBAAA,aAAA,GAAA,IAAA,CAAA,mBAAA,IAAA,CAAA;AACA,uBAAA,cAAA,GAAA,IAAA,CAAA,eAAA,IAAA,CAAA;AACA,uBAAA,eAAA,GAAA,IAAA,CAAA,gBAAA,IAAA,CAAA,CAJA,CAMA;;AACA,uBAAA,cAAA,GAAA,KAAA,cAAA,GAAA,CAAA,GACA,CAAA,KAAA,eAAA,GAAA,KAAA,cAAA,GAAA,GAAA,EAAA,OAAA,CAAA,CAAA,CADA,GACA,CADA;AAEA;;AA3CA;AAAA;;AAAA;AAAA;AAAA;AA6CA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AA7CA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAkDA;AACA,IAAA,gBAnDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAqDA,oBAAA,CAAA,EAAA,CArDA;;AAAA;AAqDA,gBAAA,QArDA;;AAsDA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,uBAAA,aAAA,GAAA,QAAA,CAAA,MAAA,IAAA,EAAA;AACA;;AAxDA;AAAA;;AAAA;AAAA;AAAA;AA0DA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,gBA1DA,CA2DA;;AACA,qBAAA,aAAA,aAAA,MAAA,CAAA,QAAA,CAAA,MAAA;;AA5DA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAgEA;AACA,IAAA,YAjEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkEA,oBAAA;AACA;AACA;AACA,uBAAA,QAAA,GAAA,QAAA;AACA,iBAJA,CAIA,OAAA,KAAA,EAAA;AACA,kBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA;AACA,uBAAA,QAAA,GAAA,QAAA;AACA;;AAzEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA4EA;AACA,IAAA,wBA7EA,sCA6EA;AACA,UAAA,WAAA,GAAA,KAAA,eAAA;;AAEA,UAAA,KAAA,QAAA,KAAA,MAAA,EAAA;AACA,aAAA,qBAAA,GAAA,EAAA;AACA,aAAA,mBAAA,GAAA,SAAA;AACA,aAAA,aAAA,GAAA,GAAA;AACA,aAAA,oBAAA,GAAA,CAAA;AACA,aAAA,aAAA,GAAA,QAAA;AACA,aAAA,aAAA,GAAA,EAAA;AACA,aAAA,aAAA,GAAA,SAAA;AACA,OARA,MAQA,IAAA,KAAA,QAAA,KAAA,KAAA,EAAA;AACA,YAAA,WAAA,IAAA,EAAA,EAAA;AACA,eAAA,qBAAA,GAAA,EAAA;AACA,eAAA,mBAAA,GAAA,UAAA;AACA,eAAA,aAAA,GAAA,GAAA;AACA,eAAA,oBAAA,GAAA,CAAA;AACA,eAAA,aAAA,GAAA,QAAA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,SAAA;AACA,SARA,MAQA,IAAA,WAAA,IAAA,EAAA,EAAA;AACA,eAAA,qBAAA,GAAA,EAAA;AACA,eAAA,mBAAA,GAAA,UAAA;AACA,eAAA,aAAA,GAAA,WAAA,GAAA,EAAA,GAAA,GAAA;AACA,eAAA,oBAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,UAAA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,SAAA;AACA,SARA,MAQA;AACA,eAAA,qBAAA,GAAA,EAAA;AACA,eAAA,mBAAA,GAAA,QAAA;AACA,eAAA,aAAA,GAAA,WAAA,GAAA,EAAA,GAAA,GAAA;AACA,eAAA,oBAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,UAAA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,SAAA;AACA;AACA,OA1BA,MA0BA;AACA;AACA,YAAA,WAAA,IAAA,EAAA,EAAA;AACA,eAAA,qBAAA,GAAA,EAAA;AACA,eAAA,mBAAA,GAAA,OAAA;AACA,eAAA,aAAA,GAAA,GAAA;AACA,eAAA,oBAAA,GAAA,CAAA;AACA,eAAA,aAAA,GAAA,QAAA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,SAAA;AACA,SARA,MAQA,IAAA,WAAA,IAAA,EAAA,EAAA;AACA,eAAA,qBAAA,GAAA,EAAA;AACA,eAAA,mBAAA,GAAA,OAAA;AACA,eAAA,aAAA,GAAA,WAAA,GAAA,EAAA,GAAA,GAAA;AACA,eAAA,oBAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,OAAA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,SAAA;AACA,SARA,MAQA;AACA,eAAA,qBAAA,GAAA,EAAA;AACA,eAAA,mBAAA,GAAA,OAAA;AACA,eAAA,aAAA,GAAA,WAAA,GAAA,EAAA,GAAA,GAAA;AACA,eAAA,oBAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,OAAA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,SAAA;AACA;AACA;AACA,KA9IA;AAgJA;AACA,IAAA,QAjJA,sBAiJA;AAAA;;AACA,UAAA,CAAA,KAAA,aAAA,EAAA;AACA,aAAA,aAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,EAAA,SADA;AAEA,UAAA,WAAA,EAAA,iBAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA;AACA;;AAEA,MAAA,SAAA,CAAA,SAAA,CAAA,SAAA,CAAA,KAAA,aAAA,EAAA,IAAA,CAAA,YAAA;AACA,QAAA,KAAA,CAAA,aAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,EAAA,SADA;AAEA,UAAA,WAAA,EAAA,yBAFA;AAGA,UAAA,SAAA,EAAA,UAHA;AAIA,UAAA,QAAA,EAAA,CAJA;AAKA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA,OADA;AAEA,YAAA,SAAA,EAAA,OAFA;AAGA,YAAA,YAAA,EAAA,KAHA;AAIA,YAAA,SAAA,EAAA;AAJA;AALA,SAAA;AAYA,OAbA,EAaA,KAbA,CAaA,YAAA;AACA,QAAA,KAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,UAAA,OAAA,EAAA,MADA;AAEA,UAAA,WAAA,EAAA,gBAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA,OAnBA;AAoBA,KA/KA;AAiLA;AACA,IAAA,cAlLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAmLA,KAAA,aAnLA;AAAA;AAAA;AAAA;;AAoLA,qBAAA,aAAA,CAAA,OAAA,CAAA;AACA,kBAAA,OAAA,EAAA,SADA;AAEA,kBAAA,WAAA,EAAA,oBAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;AApLA;;AAAA;AA4LA,oBAAA;AACA,uBAAA,SAAA,GAAA,IAAA,CADA,CAGA;;AACA,kBAAA,QAJA,2EAIA,kBAAA,CAAA,KAAA,aAAA,CAJA;AAKA,uBAAA,SAAA,GAAA,QAAA;AACA,uBAAA,WAAA,GAAA,IAAA;AAEA,uBAAA,aAAA,CAAA,OAAA,CAAA;AACA,oBAAA,OAAA,EAAA,SADA;AAEA,oBAAA,WAAA,EAAA,iBAFA;AAGA,oBAAA,SAAA,EAAA;AAHA,mBAAA;AAKA,iBAbA,CAaA,OAAA,KAAA,EAAA;AACA,kBAAA,OAAA,CAAA,KAAA,CAAA,UAAA,EAAA,KAAA;AACA,uBAAA,aAAA,CAAA,KAAA,CAAA;AACA,oBAAA,OAAA,EAAA,MADA;AAEA,oBAAA,WAAA,EAAA,eAFA;AAGA,oBAAA,SAAA,EAAA;AAHA,mBAAA;AAKA,iBApBA,SAoBA;AACA,uBAAA,SAAA,GAAA,KAAA;AACA;;AAlNA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAqNA;AACA,IAAA,cAtNA,4BAsNA;AACA,UAAA,CAAA,KAAA,SAAA,EAAA;AAEA,UAAA,IAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA;AACA,MAAA,IAAA,CAAA,IAAA,GAAA,KAAA,SAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,iBAAA;AACA,MAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AACA,MAAA,IAAA,CAAA,KAAA;AACA,MAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AAEA,WAAA,aAAA,CAAA,OAAA,CAAA;AACA,QAAA,OAAA,EAAA,MADA;AAEA,QAAA,WAAA,EAAA,WAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OAAA;AAKA,KArOA;AAuOA;AACA,IAAA,YAxOA,wBAwOA,GAxOA,EAwOA;AACA,UAAA,GAAA,KAAA,IAAA,IAAA,GAAA,KAAA,SAAA,EAAA,OAAA,GAAA;AACA,UAAA,MAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,UAAA,KAAA,CAAA,MAAA,CAAA,EAAA,OAAA,GAAA,CAHA,CAKA;;AACA,UAAA,GAAA,KAAA,KAAA,aAAA,EAAA;AACA,eAAA,MAAA,CAAA,cAAA,CAAA,OAAA,EAAA;AACA,UAAA,qBAAA,EAAA,CADA;AAEA,UAAA,qBAAA,EAAA;AAFA,SAAA,CAAA;AAIA,OAXA,CAaA;;;AACA,aAAA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA;AACA;AAvPA;AAxCA,CAAA", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"affiliate-container\">\n      <!-- 页面标题 -->\n      <div class=\"page-header\">\n        <div class=\"header-content\">\n          <h1 class=\"page-title\">分销推广中心</h1>\n          <p class=\"page-subtitle\">推广智界AIGC会员订阅，享受分层佣金奖励</p>\n          <div class=\"commission-badge\">\n            <span class=\"badge-text\">当前佣金率：{{ currentCommissionRate }}%</span>\n            <span class=\"badge-level\">{{ commissionLevelText }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 分销内容区域 -->\n      <section class=\"affiliate-section\">\n        <div class=\"container\">\n          <!-- 收益展示 -->\n          <div class=\"earnings-dashboard\">\n            <h2 class=\"section-title\">收益概览</h2>\n            <div class=\"earnings-grid\">\n              <div class=\"earning-card primary\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"dollar\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">¥{{ formatNumber(totalEarnings) }}</div>\n                    <div class=\"earning-label\">累计收益</div>\n                  </a-spin>\n                </div>\n              </div>\n\n              <div class=\"earning-card success\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"team\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">{{ formatNumber(totalReferrals) }}</div>\n                    <div class=\"earning-label\">推荐注册</div>\n                  </a-spin>\n                </div>\n              </div>\n\n              <div class=\"earning-card warning\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"crown\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">{{ formatNumber(memberReferrals) }}</div>\n                    <div class=\"earning-label\">会员转化</div>\n                  </a-spin>\n                </div>\n              </div>\n\n              <div class=\"earning-card info\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"percentage\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">{{ conversionRate }}%</div>\n                    <div class=\"earning-label\">会员转化率</div>\n                  </a-spin>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 佣金等级进度 -->\n          <div class=\"commission-progress\">\n            <h2 class=\"section-title\">佣金等级进度</h2>\n            <div class=\"progress-card\">\n              <div class=\"current-level\">\n                <div class=\"level-info\">\n                  <span class=\"level-name\">{{ commissionLevelText }}</span>\n                  <span class=\"level-rate\">{{ currentCommissionRate }}%佣金</span>\n                </div>\n                <div class=\"level-progress\">\n                  <a-progress\n                    :percent=\"levelProgress\"\n                    :stroke-color=\"progressColor\"\n                    :show-info=\"false\"\n                  />\n                  <div class=\"progress-text\">\n                    {{ memberReferrals }}/{{ nextLevelRequirement }} 会员转化\n                  </div>\n                </div>\n              </div>\n              <div class=\"next-level\" v-if=\"nextLevelRequirement > 0\">\n                <div class=\"next-info\">\n                  <span class=\"next-text\">下一等级：{{ nextLevelText }}</span>\n                  <span class=\"next-rate\">{{ nextLevelRate }}%佣金</span>\n                </div>\n                <div class=\"remaining\">\n                  还需 {{ nextLevelRequirement - memberReferrals }} 个会员转化\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 推广工具 -->\n          <div class=\"tools-section\">\n            <h2 class=\"section-title\">推广工具</h2>\n            <div class=\"tools-grid\">\n              <div class=\"tool-card\">\n                <div class=\"tool-header\">\n                  <div class=\"tool-icon\">\n                    <a-icon type=\"link\" />\n                  </div>\n                  <div class=\"tool-info\">\n                    <h3>专属推广链接</h3>\n                    <p>分享链接，好友订阅会员即可获得佣金</p>\n                  </div>\n                </div>\n                <div class=\"tool-content\">\n                  <div class=\"link-input\">\n                    <a-input\n                      :value=\"affiliateLink || '正在生成推广链接...'\"\n                      readonly\n                      :loading=\"loading\"\n                    >\n                      <template slot=\"addonAfter\">\n                        <a-button\n                          type=\"primary\"\n                          size=\"small\"\n                          :disabled=\"!affiliateLink || loading\"\n                          @click=\"copyLink\"\n                        >\n                          复制链接\n                        </a-button>\n                      </template>\n                    </a-input>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"tool-card\">\n                <div class=\"tool-header\">\n                  <div class=\"tool-icon\">\n                    <a-icon type=\"qrcode\" />\n                  </div>\n                  <div class=\"tool-info\">\n                    <h3>推广二维码</h3>\n                    <p>生成专属二维码，方便线下推广分享</p>\n                  </div>\n                </div>\n                <div class=\"tool-content\">\n                  <a-button\n                    type=\"default\"\n                    block\n                    :loading=\"qrLoading\"\n                    @click=\"generateQRCode\"\n                  >\n                    <a-icon type=\"qrcode\" />\n                    生成二维码\n                  </a-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 二维码弹窗 -->\n      <a-modal\n        v-model=\"showQRModal\"\n        title=\"推广二维码\"\n        :footer=\"null\"\n        width=\"400px\"\n        centered\n      >\n        <div class=\"qr-modal-content\">\n          <div class=\"qr-code-container\" v-if=\"qrCodeUrl\">\n            <img :src=\"qrCodeUrl\" alt=\"推广二维码\" class=\"qr-code-image\" />\n          </div>\n          <div class=\"qr-actions\">\n            <a-button type=\"primary\" block @click=\"downloadQRCode\" v-if=\"qrCodeUrl\">\n              <a-icon type=\"download\" />\n              下载二维码\n            </a-button>\n          </div>\n        </div>\n      </a-modal>\n    </div>\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport { getReferralStats, generateReferralLink } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport Vue from 'vue'\n\nexport default {\n  name: 'Affiliate',\n  components: {\n    WebsitePage\n  },\n  data() {\n    return {\n      loading: true,\n      qrLoading: false,\n\n      // 收益数据\n      totalEarnings: 0,\n      totalReferrals: 0,\n      memberReferrals: 0,\n      conversionRate: 0,\n\n      // 推广链接\n      affiliateLink: '',\n\n      // 佣金等级\n      userRole: 'NORMAL', // NORMAL, VIP, SVIP\n      currentCommissionRate: 30,\n      commissionLevelText: '新手推广员',\n      levelProgress: 0,\n      nextLevelRequirement: 10,\n      nextLevelText: '高级推广员',\n      nextLevelRate: 40,\n      progressColor: '#1890ff',\n\n      // 二维码\n      showQRModal: false,\n      qrCodeUrl: '',\n\n      // 用户信息\n      userInfo: null\n    }\n  },\n  async mounted() {\n    await this.checkLoginAndLoadData()\n  },\n  methods: {\n    // 检查登录状态并加载数据\n    async checkLoginAndLoadData() {\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      if (!token) {\n        this.$router.push({ path: '/login', query: { redirect: this.$route.fullPath } })\n        return\n      }\n\n      try {\n        await Promise.all([\n          this.loadReferralData(),\n          this.loadReferralLink(),\n          this.loadUserRole()\n        ])\n\n        // 计算佣金等级\n        this.calculateCommissionLevel()\n      } catch (error) {\n        console.error('加载分销数据失败:', error)\n        this.$notification.error({\n          message: '加载失败',\n          description: '获取分销数据失败，请稍后重试',\n          placement: 'topRight'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载推荐统计数据\n    async loadReferralData() {\n      try {\n        const response = await getReferralStats()\n        if (response.success) {\n          const data = response.result\n          this.totalEarnings = data.total_reward_amount || 0\n          this.totalReferrals = data.total_referrals || 0\n          this.memberReferrals = data.member_referrals || 0\n\n          // 计算会员转化率：会员转化数 / 总注册数 * 100\n          this.conversionRate = this.totalReferrals > 0 ?\n            ((this.memberReferrals) / this.totalReferrals * 100).toFixed(1) : 0\n        }\n      } catch (error) {\n        console.error('获取推荐统计失败:', error)\n        throw error\n      }\n    },\n\n    // 加载推荐链接\n    async loadReferralLink() {\n      try {\n        const response = await generateReferralLink({})\n        if (response.success) {\n          this.affiliateLink = response.result || ''\n        }\n      } catch (error) {\n        console.error('获取推荐链接失败:', error)\n        // 如果获取失败，使用默认链接格式\n        this.affiliateLink = `${window.location.origin}?ref=loading...`\n      }\n    },\n\n    // 加载用户角色信息\n    async loadUserRole() {\n      try {\n        // TODO: 从用户信息API获取用户角色\n        // 暂时使用默认值\n        this.userRole = 'NORMAL'\n      } catch (error) {\n        console.error('获取用户角色失败:', error)\n        this.userRole = 'NORMAL'\n      }\n    },\n\n    // 计算佣金等级和进度\n    calculateCommissionLevel() {\n      const memberCount = this.memberReferrals\n\n      if (this.userRole === 'SVIP') {\n        this.currentCommissionRate = 50\n        this.commissionLevelText = 'SVIP推广员'\n        this.levelProgress = 100\n        this.nextLevelRequirement = 0\n        this.nextLevelText = '已达最高等级'\n        this.nextLevelRate = 50\n        this.progressColor = '#722ed1'\n      } else if (this.userRole === 'VIP') {\n        if (memberCount >= 30) {\n          this.currentCommissionRate = 50\n          this.commissionLevelText = 'VIP顶级推广员'\n          this.levelProgress = 100\n          this.nextLevelRequirement = 0\n          this.nextLevelText = '已达最高等级'\n          this.nextLevelRate = 50\n          this.progressColor = '#722ed1'\n        } else if (memberCount >= 10) {\n          this.currentCommissionRate = 45\n          this.commissionLevelText = 'VIP高级推广员'\n          this.levelProgress = (memberCount / 30) * 100\n          this.nextLevelRequirement = 30\n          this.nextLevelText = 'VIP顶级推广员'\n          this.nextLevelRate = 50\n          this.progressColor = '#13c2c2'\n        } else {\n          this.currentCommissionRate = 35\n          this.commissionLevelText = 'VIP推广员'\n          this.levelProgress = (memberCount / 10) * 100\n          this.nextLevelRequirement = 10\n          this.nextLevelText = 'VIP高级推广员'\n          this.nextLevelRate = 45\n          this.progressColor = '#1890ff'\n        }\n      } else {\n        // NORMAL用户\n        if (memberCount >= 30) {\n          this.currentCommissionRate = 50\n          this.commissionLevelText = '顶级推广员'\n          this.levelProgress = 100\n          this.nextLevelRequirement = 0\n          this.nextLevelText = '已达最高等级'\n          this.nextLevelRate = 50\n          this.progressColor = '#722ed1'\n        } else if (memberCount >= 10) {\n          this.currentCommissionRate = 40\n          this.commissionLevelText = '高级推广员'\n          this.levelProgress = (memberCount / 30) * 100\n          this.nextLevelRequirement = 30\n          this.nextLevelText = '顶级推广员'\n          this.nextLevelRate = 50\n          this.progressColor = '#13c2c2'\n        } else {\n          this.currentCommissionRate = 30\n          this.commissionLevelText = '新手推广员'\n          this.levelProgress = (memberCount / 10) * 100\n          this.nextLevelRequirement = 10\n          this.nextLevelText = '高级推广员'\n          this.nextLevelRate = 40\n          this.progressColor = '#1890ff'\n        }\n      }\n    },\n\n     // 复制推广链接\n     copyLink() {\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '推广链接未生成',\n          description: '推广链接正在生成中，请稍后再试',\n          placement: 'topRight'\n        })\n        return\n      }\n      \n      navigator.clipboard.writeText(this.affiliateLink).then(() => {\n        this.$notification.success({\n          message: '推广链接已复制',\n          description: '推广链接已成功复制到剪贴板，快去分享给好友吧！',\n          placement: 'topRight',\n          duration: 3,\n          style: {\n            width: '380px',\n            marginTop: '101px',\n            borderRadius: '8px',\n            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n          }\n        })\n      }).catch(() => {\n        this.$notification.error({\n          message: '复制失败',\n          description: '复制推广链接失败，请手动复制',\n          placement: 'topRight'\n        })\n      })\n    },\n\n    // 生成推广二维码\n    async generateQRCode() {\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '推广链接未生成',\n          description: '请等待推广链接生成完成后再生成二维码',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      try {\n        this.qrLoading = true\n\n        // 使用第三方二维码生成服务\n        const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(this.affiliateLink)}`\n        this.qrCodeUrl = qrApiUrl\n        this.showQRModal = true\n\n        this.$notification.success({\n          message: '二维码生成成功',\n          description: '推广二维码已生成，可以下载保存',\n          placement: 'topRight'\n        })\n      } catch (error) {\n        console.error('生成二维码失败:', error)\n        this.$notification.error({\n          message: '生成失败',\n          description: '二维码生成失败，请稍后重试',\n          placement: 'topRight'\n        })\n      } finally {\n        this.qrLoading = false\n      }\n    },\n\n    // 下载二维码\n    downloadQRCode() {\n      if (!this.qrCodeUrl) return\n\n      const link = document.createElement('a')\n      link.href = this.qrCodeUrl\n      link.download = '智界AIGC推广二维码.png'\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n\n      this.$notification.success({\n        message: '下载成功',\n        description: '二维码已保存到本地',\n        placement: 'topRight'\n      })\n    },\n\n     // 格式化数字显示\n     formatNumber(num) {\n       if (num === null || num === undefined) return '0'\n       const number = parseFloat(num)\n       if (isNaN(number)) return '0'\n       \n       // 如果是金额，保留两位小数\n       if (num === this.totalEarnings) {\n         return number.toLocaleString('zh-CN', {\n           minimumFractionDigits: 2,\n           maximumFractionDigits: 2\n         })\n       }\n       \n       // 其他数字不保留小数\n       return number.toLocaleString('zh-CN')\n     }\n   }\n }\n</script>\n\n<style scoped>\n.affiliate-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n/* 页面头部 */\n.page-header {\n  text-align: center;\n  padding: 60px 20px 40px;\n  color: white;\n}\n\n.header-content {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.page-title {\n  font-size: 3rem;\n  font-weight: 700;\n  margin-bottom: 16px;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.page-subtitle {\n  font-size: 1.2rem;\n  opacity: 0.9;\n  margin-bottom: 24px;\n  line-height: 1.6;\n}\n\n.commission-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(10px);\n  padding: 12px 24px;\n  border-radius: 50px;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n}\n\n.badge-text {\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.badge-level {\n  background: rgba(255, 255, 255, 0.3);\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n/* 主要内容区域 */\n.affiliate-section {\n  padding: 0 20px 80px;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.section-title {\n  font-size: 1.8rem;\n  font-weight: 600;\n  margin-bottom: 24px;\n  color: #2d3748;\n  text-align: center;\n}\n\n/* 收益仪表板 */\n.earnings-dashboard {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  margin-bottom: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.earnings-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n  gap: 24px;\n}\n\n.earning-card {\n  display: flex;\n  align-items: center;\n  padding: 24px;\n  border-radius: 16px;\n  background: white;\n  border: 2px solid #f1f5f9;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.earning-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: var(--card-color);\n}\n\n.earning-card.primary {\n  --card-color: #3b82f6;\n}\n\n.earning-card.success {\n  --card-color: #10b981;\n}\n\n.earning-card.warning {\n  --card-color: #f59e0b;\n}\n\n.earning-card.info {\n  --card-color: #8b5cf6;\n}\n\n.earning-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);\n  border-color: var(--card-color);\n}\n\n.card-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  color: white;\n  font-size: 20px;\n  background: var(--card-color);\n}\n\n.card-content {\n  flex: 1;\n}\n\n.earning-number {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 4px;\n  line-height: 1;\n}\n\n.earning-label {\n  font-size: 0.9rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n/* 佣金等级进度 */\n.commission-progress {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  margin-bottom: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.progress-card {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  border-radius: 16px;\n  padding: 32px;\n  border: 2px solid #e2e8f0;\n}\n\n.current-level {\n  margin-bottom: 24px;\n}\n\n.level-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.level-name {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.level-rate {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 6px 16px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 600;\n}\n\n.level-progress {\n  margin-bottom: 8px;\n}\n\n.progress-text {\n  text-align: center;\n  font-size: 0.9rem;\n  color: #6b7280;\n  margin-top: 8px;\n}\n\n.next-level {\n  padding-top: 24px;\n  border-top: 1px solid #e5e7eb;\n}\n\n.next-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.next-text {\n  font-size: 1rem;\n  color: #374151;\n  font-weight: 500;\n}\n\n.next-rate {\n  background: #f3f4f6;\n  color: #6b7280;\n  padding: 4px 12px;\n  border-radius: 16px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.remaining {\n  font-size: 0.9rem;\n  color: #9ca3af;\n  text-align: center;\n}\n\n/* 推广工具 */\n.tools-section {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.tools-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 32px;\n}\n\n.tool-card {\n  background: #fafbfc;\n  border: 2px solid #f1f5f9;\n  border-radius: 16px;\n  padding: 32px;\n  transition: all 0.3s ease;\n}\n\n.tool-card:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.15);\n}\n\n.tool-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 24px;\n}\n\n.tool-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  color: white;\n  font-size: 20px;\n  flex-shrink: 0;\n}\n\n.tool-info h3 {\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n  color: #1f2937;\n}\n\n.tool-info p {\n  color: #6b7280;\n  line-height: 1.5;\n  margin: 0;\n}\n\n.tool-content {\n  margin-top: 16px;\n}\n\n.link-input {\n  width: 100%;\n}\n\n/* 二维码弹窗 */\n.qr-modal-content {\n  text-align: center;\n}\n\n.qr-code-container {\n  margin-bottom: 24px;\n  padding: 20px;\n  background: #f8fafc;\n  border-radius: 12px;\n  border: 2px dashed #d1d5db;\n}\n\n.qr-code-image {\n  max-width: 100%;\n  height: auto;\n  border-radius: 8px;\n}\n\n.qr-actions {\n  margin-top: 16px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .page-title {\n    font-size: 2rem;\n  }\n\n  .page-subtitle {\n    font-size: 1rem;\n  }\n\n  .commission-badge {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .earnings-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .tools-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .earnings-dashboard,\n  .commission-progress,\n  .tools-section {\n    padding: 24px;\n  }\n\n  .tool-card {\n    padding: 24px;\n  }\n\n  .progress-card {\n    padding: 24px;\n  }\n\n  .level-info,\n  .next-info {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .tool-header {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .tool-icon {\n    margin: 0 auto 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .affiliate-section {\n    padding: 0 16px 60px;\n  }\n\n  .page-header {\n    padding: 40px 16px 24px;\n  }\n\n  .earning-card {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .card-icon {\n    margin: 0 auto 12px;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/affiliate"}]}