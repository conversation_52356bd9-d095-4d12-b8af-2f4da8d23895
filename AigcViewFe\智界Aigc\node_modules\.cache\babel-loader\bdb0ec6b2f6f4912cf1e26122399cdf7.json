{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue", "mtime": 1753667703784}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WebsitePage from '@/components/website/WebsitePage.vue';\nimport { getReferralStats, generateReferralLink } from '@/api/usercenter';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport Vue from 'vue';\nexport default {\n  name: 'Affiliate',\n  components: {\n    WebsitePage: WebsitePage\n  },\n  data: function data() {\n    return {\n      loading: true,\n      qrLoading: false,\n      // 收益数据\n      totalEarnings: 0,\n      availableEarnings: 0,\n      totalReferrals: 0,\n      memberReferrals: 0,\n      // 推广链接\n      affiliateLink: '',\n      // 佣金等级\n      userRole: 'NORMAL',\n      // NORMAL, VIP, SVIP\n      currentCommissionRate: 30,\n      commissionLevelText: '新手推广员',\n      levelProgress: 0,\n      nextLevelRequirement: 10,\n      nextLevelText: '高级推广员',\n      nextLevelRate: 40,\n      progressColor: '#1890ff',\n      // 二维码\n      showQRModal: false,\n      qrCodeUrl: '',\n      qrPreGenerated: false,\n      // 是否已预生成二维码\n      // 提现\n      showWithdrawModal: false,\n      withdrawLoading: false,\n      withdrawForm: this.$form.createForm(this),\n      // 推广用户列表\n      referralUsers: [],\n      usersLoading: false,\n      userColumns: [{\n        title: '头像',\n        dataIndex: 'avatar',\n        key: 'avatar',\n        scopedSlots: {\n          customRender: 'avatar'\n        },\n        width: 80\n      }, {\n        title: '用户昵称',\n        dataIndex: 'nickname',\n        key: 'nickname'\n      }, {\n        title: '注册时间',\n        dataIndex: 'registerTime',\n        key: 'registerTime'\n      }, {\n        title: '转化状态',\n        dataIndex: 'status',\n        key: 'status',\n        scopedSlots: {\n          customRender: 'status'\n        }\n      }, {\n        title: '获得奖励',\n        dataIndex: 'reward',\n        key: 'reward',\n        scopedSlots: {\n          customRender: 'reward'\n        }\n      }],\n      // 提现记录\n      withdrawRecords: [],\n      recordsLoading: false,\n      withdrawColumns: [{\n        title: '提现金额',\n        dataIndex: 'amount',\n        key: 'amount',\n        scopedSlots: {\n          customRender: 'amount'\n        }\n      }, {\n        title: '提现方式',\n        dataIndex: 'method',\n        key: 'method'\n      }, {\n        title: '申请时间',\n        dataIndex: 'applyTime',\n        key: 'applyTime'\n      }, {\n        title: '状态',\n        dataIndex: 'status',\n        key: 'status',\n        scopedSlots: {\n          customRender: 'status'\n        }\n      }, {\n        title: '完成时间',\n        dataIndex: 'completeTime',\n        key: 'completeTime'\n      }],\n      // 用户信息\n      userInfo: null\n    };\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return this.checkLoginAndLoadData();\n\n            case 2:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  methods: {\n    // 检查登录状态并加载数据\n    checkLoginAndLoadData: function () {\n      var _checkLoginAndLoadData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var token;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                token = Vue.ls.get(ACCESS_TOKEN);\n\n                if (token) {\n                  _context2.next = 4;\n                  break;\n                }\n\n                this.$router.push({\n                  path: '/login',\n                  query: {\n                    redirect: this.$route.fullPath\n                  }\n                });\n                return _context2.abrupt(\"return\");\n\n              case 4:\n                _context2.prev = 4;\n                _context2.next = 7;\n                return Promise.all([this.loadReferralData(), this.loadReferralLink(), this.loadUserRole(), this.loadReferralUsers(), this.loadWithdrawRecords()]);\n\n              case 7:\n                // 计算佣金等级\n                this.calculateCommissionLevel(); // 自动预生成二维码\n\n                this.preGenerateQRCode();\n                _context2.next = 15;\n                break;\n\n              case 11:\n                _context2.prev = 11;\n                _context2.t0 = _context2[\"catch\"](4);\n                console.error('加载分销数据失败:', _context2.t0);\n                this.$notification.error({\n                  message: '加载失败',\n                  description: '获取分销数据失败，请稍后重试',\n                  placement: 'topRight'\n                });\n\n              case 15:\n                _context2.prev = 15;\n                this.loading = false;\n                return _context2.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[4, 11, 15, 18]]);\n      }));\n\n      function checkLoginAndLoadData() {\n        return _checkLoginAndLoadData.apply(this, arguments);\n      }\n\n      return checkLoginAndLoadData;\n    }(),\n    // 加载推荐统计数据\n    loadReferralData: function () {\n      var _loadReferralData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var response, data;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                _context3.next = 3;\n                return getReferralStats();\n\n              case 3:\n                response = _context3.sent;\n\n                if (response.success) {\n                  data = response.result;\n                  this.totalEarnings = data.total_reward_amount || 0;\n                  this.availableEarnings = data.available_rewards || 0;\n                  this.totalReferrals = data.total_referrals || 0;\n                  this.memberReferrals = data.member_referrals || 0;\n                }\n\n                _context3.next = 11;\n                break;\n\n              case 7:\n                _context3.prev = 7;\n                _context3.t0 = _context3[\"catch\"](0);\n                console.error('获取推荐统计失败:', _context3.t0);\n                throw _context3.t0;\n\n              case 11:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 7]]);\n      }));\n\n      function loadReferralData() {\n        return _loadReferralData.apply(this, arguments);\n      }\n\n      return loadReferralData;\n    }(),\n    // 加载推荐链接\n    loadReferralLink: function () {\n      var _loadReferralLink = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n                _context4.next = 3;\n                return generateReferralLink({});\n\n              case 3:\n                response = _context4.sent;\n\n                if (response.success) {\n                  this.affiliateLink = response.result || '';\n                }\n\n                _context4.next = 11;\n                break;\n\n              case 7:\n                _context4.prev = 7;\n                _context4.t0 = _context4[\"catch\"](0);\n                console.error('获取推荐链接失败:', _context4.t0); // 如果获取失败，使用默认链接格式\n\n                this.affiliateLink = \"\".concat(window.location.origin, \"?ref=loading...\");\n\n              case 11:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[0, 7]]);\n      }));\n\n      function loadReferralLink() {\n        return _loadReferralLink.apply(this, arguments);\n      }\n\n      return loadReferralLink;\n    }(),\n    // 加载用户角色信息\n    loadUserRole: function () {\n      var _loadUserRole = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                try {\n                  // TODO: 从用户信息API获取用户角色\n                  // 暂时使用默认值\n                  this.userRole = 'NORMAL';\n                } catch (error) {\n                  console.error('获取用户角色失败:', error);\n                  this.userRole = 'NORMAL';\n                }\n\n              case 1:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this);\n      }));\n\n      function loadUserRole() {\n        return _loadUserRole.apply(this, arguments);\n      }\n\n      return loadUserRole;\n    }(),\n    // 计算佣金等级和进度\n    calculateCommissionLevel: function calculateCommissionLevel() {\n      var memberCount = this.memberReferrals;\n\n      if (this.userRole === 'SVIP') {\n        this.currentCommissionRate = 50;\n        this.commissionLevelText = 'SVIP推广员';\n        this.levelProgress = 100;\n        this.nextLevelRequirement = 0;\n        this.nextLevelText = '已达最高等级';\n        this.nextLevelRate = 50;\n        this.progressColor = '#722ed1';\n      } else if (this.userRole === 'VIP') {\n        if (memberCount >= 30) {\n          this.currentCommissionRate = 50;\n          this.commissionLevelText = 'VIP顶级推广员';\n          this.levelProgress = 100;\n          this.nextLevelRequirement = 0;\n          this.nextLevelText = '已达最高等级';\n          this.nextLevelRate = 50;\n          this.progressColor = '#722ed1';\n        } else if (memberCount >= 10) {\n          this.currentCommissionRate = 45;\n          this.commissionLevelText = 'VIP高级推广员';\n          this.levelProgress = memberCount / 30 * 100;\n          this.nextLevelRequirement = 30;\n          this.nextLevelText = 'VIP顶级推广员';\n          this.nextLevelRate = 50;\n          this.progressColor = '#13c2c2';\n        } else {\n          this.currentCommissionRate = 35;\n          this.commissionLevelText = 'VIP推广员';\n          this.levelProgress = memberCount / 10 * 100;\n          this.nextLevelRequirement = 10;\n          this.nextLevelText = 'VIP高级推广员';\n          this.nextLevelRate = 45;\n          this.progressColor = '#1890ff';\n        }\n      } else {\n        // NORMAL用户\n        if (memberCount >= 30) {\n          this.currentCommissionRate = 50;\n          this.commissionLevelText = '顶级推广员';\n          this.levelProgress = 100;\n          this.nextLevelRequirement = 0;\n          this.nextLevelText = '已达最高等级';\n          this.nextLevelRate = 50;\n          this.progressColor = '#722ed1';\n        } else if (memberCount >= 10) {\n          this.currentCommissionRate = 40;\n          this.commissionLevelText = '高级推广员';\n          this.levelProgress = memberCount / 30 * 100;\n          this.nextLevelRequirement = 30;\n          this.nextLevelText = '顶级推广员';\n          this.nextLevelRate = 50;\n          this.progressColor = '#13c2c2';\n        } else {\n          this.currentCommissionRate = 30;\n          this.commissionLevelText = '新手推广员';\n          this.levelProgress = memberCount / 10 * 100;\n          this.nextLevelRequirement = 10;\n          this.nextLevelText = '高级推广员';\n          this.nextLevelRate = 40;\n          this.progressColor = '#1890ff';\n        }\n      }\n    },\n    // 复制推广链接\n    copyLink: function copyLink() {\n      var _this = this;\n\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '推广链接未生成',\n          description: '推广链接正在生成中，请稍后再试',\n          placement: 'topRight'\n        });\n        return;\n      }\n\n      navigator.clipboard.writeText(this.affiliateLink).then(function () {\n        _this.$notification.success({\n          message: '推广链接已复制',\n          description: '推广链接已成功复制到剪贴板，快去分享给好友吧！',\n          placement: 'topRight',\n          duration: 3,\n          style: {\n            width: '380px',\n            marginTop: '101px',\n            borderRadius: '8px',\n            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n          }\n        });\n      }).catch(function () {\n        _this.$notification.error({\n          message: '复制失败',\n          description: '复制推广链接失败，请手动复制',\n          placement: 'topRight'\n        });\n      });\n    },\n    // 预生成二维码（后台静默生成）\n    preGenerateQRCode: function () {\n      var _preGenerateQRCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                if (!(!this.affiliateLink || this.qrPreGenerated)) {\n                  _context6.next = 2;\n                  break;\n                }\n\n                return _context6.abrupt(\"return\");\n\n              case 2:\n                _context6.prev = 2;\n                console.log('开始预生成二维码...'); // 调用后端API生成二维码并上传到TOS\n\n                _context6.next = 6;\n                return this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n                  params: {\n                    url: this.affiliateLink\n                  }\n                });\n\n              case 6:\n                response = _context6.sent;\n\n                if (response && response.success) {\n                  // 静默保存二维码URL\n                  this.qrCodeUrl = response.result;\n                  this.qrPreGenerated = true;\n                  console.log('二维码预生成成功:', this.qrCodeUrl);\n                }\n\n                _context6.next = 13;\n                break;\n\n              case 10:\n                _context6.prev = 10;\n                _context6.t0 = _context6[\"catch\"](2);\n                console.error('预生成二维码失败:', _context6.t0); // 预生成失败不显示错误提示，用户点击时再重试\n\n              case 13:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[2, 10]]);\n      }));\n\n      function preGenerateQRCode() {\n        return _preGenerateQRCode.apply(this, arguments);\n      }\n\n      return preGenerateQRCode;\n    }(),\n    // 生成推广二维码（用户主动点击）\n    generateQRCode: function () {\n      var _generateQRCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7() {\n        var response, errorMsg;\n        return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n          while (1) {\n            switch (_context7.prev = _context7.next) {\n              case 0:\n                if (this.affiliateLink) {\n                  _context7.next = 3;\n                  break;\n                }\n\n                this.$notification.warning({\n                  message: '推广链接未生成',\n                  description: '请等待推广链接生成完成后再生成二维码',\n                  placement: 'topRight'\n                });\n                return _context7.abrupt(\"return\");\n\n              case 3:\n                if (!(this.qrPreGenerated && this.qrCodeUrl)) {\n                  _context7.next = 6;\n                  break;\n                }\n\n                this.showQRModal = true;\n                return _context7.abrupt(\"return\");\n\n              case 6:\n                _context7.prev = 6;\n                this.qrLoading = true; // 调用后端API生成二维码并上传到TOS\n\n                _context7.next = 10;\n                return this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n                  params: {\n                    url: this.affiliateLink\n                  }\n                });\n\n              case 10:\n                response = _context7.sent;\n                console.log('二维码生成响应:', response);\n\n                if (!(response && response.success)) {\n                  _context7.next = 19;\n                  break;\n                }\n\n                // 使用CDN地址\n                this.qrCodeUrl = response.result;\n                this.qrPreGenerated = true;\n                this.showQRModal = true;\n                this.$notification.success({\n                  message: '二维码生成成功',\n                  description: '推广二维码已生成并存储到CDN，可以下载保存',\n                  placement: 'topRight'\n                });\n                _context7.next = 21;\n                break;\n\n              case 19:\n                errorMsg = response && response.message || '生成失败';\n                throw new Error(errorMsg);\n\n              case 21:\n                _context7.next = 27;\n                break;\n\n              case 23:\n                _context7.prev = 23;\n                _context7.t0 = _context7[\"catch\"](6);\n                console.error('生成二维码失败:', _context7.t0);\n                this.$notification.error({\n                  message: '生成失败',\n                  description: _context7.t0.message || '二维码生成失败，请稍后重试',\n                  placement: 'topRight'\n                });\n\n              case 27:\n                _context7.prev = 27;\n                this.qrLoading = false;\n                return _context7.finish(27);\n\n              case 30:\n              case \"end\":\n                return _context7.stop();\n            }\n          }\n        }, _callee7, this, [[6, 23, 27, 30]]);\n      }));\n\n      function generateQRCode() {\n        return _generateQRCode.apply(this, arguments);\n      }\n\n      return generateQRCode;\n    }(),\n    // 下载二维码\n    downloadQRCode: function downloadQRCode() {\n      if (!this.qrCodeUrl) return;\n\n      try {\n        // 通过后端代理下载，避免CORS问题\n        var downloadUrl = \"/api/usercenter/downloadReferralQRCode?url=\".concat(encodeURIComponent(this.qrCodeUrl)); // 创建隐藏的iframe来触发下载\n\n        var iframe = document.createElement('iframe');\n        iframe.style.display = 'none';\n        iframe.src = downloadUrl;\n        document.body.appendChild(iframe); // 2秒后移除iframe\n\n        setTimeout(function () {\n          document.body.removeChild(iframe);\n        }, 2000);\n        this.$notification.success({\n          message: '下载开始',\n          description: '二维码正在下载到本地',\n          placement: 'topRight'\n        });\n      } catch (error) {\n        console.error('下载二维码失败:', error);\n        this.$notification.error({\n          message: '下载失败',\n          description: '二维码下载失败，请稍后重试',\n          placement: 'topRight'\n        });\n      }\n    },\n    // 显示提现弹窗\n    openWithdrawModal: function openWithdrawModal() {\n      if (this.availableEarnings < 100) {\n        this.$notification.warning({\n          message: '提现金额不足',\n          description: '最低提现金额为100元，请继续推广获得更多收益',\n          placement: 'topRight'\n        });\n        return;\n      }\n\n      this.showWithdrawModal = true;\n    },\n    // 处理提现申请\n    handleWithdraw: function handleWithdraw() {\n      var _this2 = this;\n\n      this.withdrawForm.validateFields(function (err, values) {\n        if (err) return;\n        _this2.withdrawLoading = true; // TODO: 调用提现API\n\n        setTimeout(function () {\n          _this2.withdrawLoading = false;\n          _this2.showWithdrawModal = false;\n\n          _this2.withdrawForm.resetFields();\n\n          _this2.$notification.success({\n            message: '提现申请成功',\n            description: '您的提现申请已提交，预计1-3个工作日到账',\n            placement: 'topRight'\n          });\n        }, 2000);\n      });\n    },\n    // 加载推广用户列表\n    loadReferralUsers: function () {\n      var _loadReferralUsers = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee8() {\n        var params, response, records;\n        return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n          while (1) {\n            switch (_context8.prev = _context8.next) {\n              case 0:\n                _context8.prev = 0;\n                this.usersLoading = true;\n                params = {\n                  current: 1,\n                  size: 10\n                };\n                _context8.next = 5;\n                return this.$http.get('/api/usercenter/referralList', {\n                  params: params\n                });\n\n              case 5:\n                response = _context8.sent;\n\n                if (response.data.success) {\n                  records = response.data.result && response.data.result.records || []; // 转换数据格式\n\n                  this.referralUsers = records.map(function (item, index) {\n                    return {\n                      key: item.id || index,\n                      nickname: item.referee_nickname || \"\\u7528\\u6237***\".concat(index + 1),\n                      avatar: item.referee_avatar || '',\n                      registerTime: item.register_time || '',\n                      status: item.has_membership ? '已转化' : '已注册',\n                      reward: item.total_reward || '0.00'\n                    };\n                  });\n                } else {\n                  this.referralUsers = [];\n                }\n\n                _context8.next = 13;\n                break;\n\n              case 9:\n                _context8.prev = 9;\n                _context8.t0 = _context8[\"catch\"](0);\n                console.error('获取推广用户列表失败:', _context8.t0);\n                this.referralUsers = [];\n\n              case 13:\n                _context8.prev = 13;\n                this.usersLoading = false;\n                return _context8.finish(13);\n\n              case 16:\n              case \"end\":\n                return _context8.stop();\n            }\n          }\n        }, _callee8, this, [[0, 9, 13, 16]]);\n      }));\n\n      function loadReferralUsers() {\n        return _loadReferralUsers.apply(this, arguments);\n      }\n\n      return loadReferralUsers;\n    }(),\n    // 加载提现记录\n    loadWithdrawRecords: function () {\n      var _loadWithdrawRecords = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee9() {\n        var _this3 = this;\n\n        var params, response, records;\n        return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n          while (1) {\n            switch (_context9.prev = _context9.next) {\n              case 0:\n                _context9.prev = 0;\n                this.recordsLoading = true;\n                params = {\n                  current: 1,\n                  size: 10\n                };\n                _context9.next = 5;\n                return this.$http.get('/api/usercenter/withdrawalHistory', {\n                  params: params\n                });\n\n              case 5:\n                response = _context9.sent;\n\n                if (response.data.success) {\n                  records = response.data.result && response.data.result.records || []; // 转换数据格式\n\n                  this.withdrawRecords = records.map(function (item, index) {\n                    return {\n                      key: item.id || index,\n                      amount: item.amount || '0.00',\n                      method: item.withdrawalMethod || '支付宝',\n                      applyTime: item.applyTime || '',\n                      status: _this3.getWithdrawStatusText(item.status),\n                      completeTime: item.completeTime || '-'\n                    };\n                  });\n                } else {\n                  this.withdrawRecords = [];\n                }\n\n                _context9.next = 13;\n                break;\n\n              case 9:\n                _context9.prev = 9;\n                _context9.t0 = _context9[\"catch\"](0);\n                console.error('获取提现记录失败:', _context9.t0);\n                this.withdrawRecords = [];\n\n              case 13:\n                _context9.prev = 13;\n                this.recordsLoading = false;\n                return _context9.finish(13);\n\n              case 16:\n              case \"end\":\n                return _context9.stop();\n            }\n          }\n        }, _callee9, this, [[0, 9, 13, 16]]);\n      }));\n\n      function loadWithdrawRecords() {\n        return _loadWithdrawRecords.apply(this, arguments);\n      }\n\n      return loadWithdrawRecords;\n    }(),\n    // 获取提现状态文本\n    getWithdrawStatusText: function getWithdrawStatusText(status) {\n      var statusMap = {\n        0: '待审核',\n        1: '处理中',\n        2: '已完成',\n        3: '已拒绝'\n      };\n      return statusMap[status] || '未知状态';\n    },\n    // 获取状态颜色\n    getStatusColor: function getStatusColor(status) {\n      var colorMap = {\n        '已完成': 'green',\n        '处理中': 'blue',\n        '已拒绝': 'red',\n        '待审核': 'orange'\n      };\n      return colorMap[status] || 'default';\n    },\n    // 格式化数字显示\n    formatNumber: function formatNumber(num) {\n      if (num === null || num === undefined) return '0';\n      var number = parseFloat(num);\n      if (isNaN(number)) return '0'; // 如果是金额，保留两位小数\n\n      if (num === this.totalEarnings) {\n        return number.toLocaleString('zh-CN', {\n          minimumFractionDigits: 2,\n          maximumFractionDigits: 2\n        });\n      } // 其他数字不保留小数\n\n\n      return number.toLocaleString('zh-CN');\n    }\n  }\n};", {"version": 3, "sources": ["Affiliate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4WA,OAAA,WAAA,MAAA,sCAAA;AACA,SAAA,gBAAA,EAAA,oBAAA,QAAA,kBAAA;AACA,SAAA,YAAA,QAAA,wBAAA;AACA,OAAA,GAAA,MAAA,KAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,WADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,WAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,IADA;AAEA,MAAA,SAAA,EAAA,KAFA;AAIA;AACA,MAAA,aAAA,EAAA,CALA;AAMA,MAAA,iBAAA,EAAA,CANA;AAOA,MAAA,cAAA,EAAA,CAPA;AAQA,MAAA,eAAA,EAAA,CARA;AAUA;AACA,MAAA,aAAA,EAAA,EAXA;AAaA;AACA,MAAA,QAAA,EAAA,QAdA;AAcA;AACA,MAAA,qBAAA,EAAA,EAfA;AAgBA,MAAA,mBAAA,EAAA,OAhBA;AAiBA,MAAA,aAAA,EAAA,CAjBA;AAkBA,MAAA,oBAAA,EAAA,EAlBA;AAmBA,MAAA,aAAA,EAAA,OAnBA;AAoBA,MAAA,aAAA,EAAA,EApBA;AAqBA,MAAA,aAAA,EAAA,SArBA;AAuBA;AACA,MAAA,WAAA,EAAA,KAxBA;AAyBA,MAAA,SAAA,EAAA,EAzBA;AA0BA,MAAA,cAAA,EAAA,KA1BA;AA0BA;AAEA;AACA,MAAA,iBAAA,EAAA,KA7BA;AA8BA,MAAA,eAAA,EAAA,KA9BA;AA+BA,MAAA,YAAA,EAAA,KAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CA/BA;AAiCA;AACA,MAAA,aAAA,EAAA,EAlCA;AAmCA,MAAA,YAAA,EAAA,KAnCA;AAoCA,MAAA,WAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA,SAJA;AAKA,QAAA,KAAA,EAAA;AALA,OADA,EAQA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,UAFA;AAGA,QAAA,GAAA,EAAA;AAHA,OARA,EAaA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,cAFA;AAGA,QAAA,GAAA,EAAA;AAHA,OAbA,EAkBA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAJA,OAlBA,EAwBA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAJA,OAxBA,CApCA;AAoEA;AACA,MAAA,eAAA,EAAA,EArEA;AAsEA,MAAA,cAAA,EAAA,KAtEA;AAuEA,MAAA,eAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAJA,OADA,EAOA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA;AAHA,OAPA,EAYA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,WAFA;AAGA,QAAA,GAAA,EAAA;AAHA,OAZA,EAiBA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAJA,OAjBA,EAuBA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,cAFA;AAGA,QAAA,GAAA,EAAA;AAHA,OAvBA,CAvEA;AAqGA;AACA,MAAA,QAAA,EAAA;AAtGA,KAAA;AAwGA,GA9GA;AA+GA,EAAA,OA/GA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAgHA,KAAA,qBAAA,EAhHA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAkHA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,qBAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,gBAAA,KAHA,GAGA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,YAAA,CAHA;;AAAA,oBAIA,KAJA;AAAA;AAAA;AAAA;;AAKA,qBAAA,OAAA,CAAA,IAAA,CAAA;AAAA,kBAAA,IAAA,EAAA,QAAA;AAAA,kBAAA,KAAA,EAAA;AAAA,oBAAA,QAAA,EAAA,KAAA,MAAA,CAAA;AAAA;AAAA,iBAAA;AALA;;AAAA;AAAA;AAAA;AAAA,uBAUA,OAAA,CAAA,GAAA,CAAA,CACA,KAAA,gBAAA,EADA,EAEA,KAAA,gBAAA,EAFA,EAGA,KAAA,YAAA,EAHA,EAIA,KAAA,iBAAA,EAJA,EAKA,KAAA,mBAAA,EALA,CAAA,CAVA;;AAAA;AAkBA;AACA,qBAAA,wBAAA,GAnBA,CAqBA;;AACA,qBAAA,iBAAA;AAtBA;AAAA;;AAAA;AAAA;AAAA;AAwBA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,aAAA,CAAA,KAAA,CAAA;AACA,kBAAA,OAAA,EAAA,MADA;AAEA,kBAAA,WAAA,EAAA,gBAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;;AAzBA;AAAA;AA+BA,qBAAA,OAAA,GAAA,KAAA;AA/BA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAmCA;AACA,IAAA,gBApCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAsCA,gBAAA,EAtCA;;AAAA;AAsCA,gBAAA,QAtCA;;AAuCA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,kBAAA,IADA,GACA,QAAA,CAAA,MADA;AAEA,uBAAA,aAAA,GAAA,IAAA,CAAA,mBAAA,IAAA,CAAA;AACA,uBAAA,iBAAA,GAAA,IAAA,CAAA,iBAAA,IAAA,CAAA;AACA,uBAAA,cAAA,GAAA,IAAA,CAAA,eAAA,IAAA,CAAA;AACA,uBAAA,eAAA,GAAA,IAAA,CAAA,gBAAA,IAAA,CAAA;AAGA;;AA/CA;AAAA;;AAAA;AAAA;AAAA;AAiDA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AAjDA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAsDA;AACA,IAAA,gBAvDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAyDA,oBAAA,CAAA,EAAA,CAzDA;;AAAA;AAyDA,gBAAA,QAzDA;;AA0DA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,uBAAA,aAAA,GAAA,QAAA,CAAA,MAAA,IAAA,EAAA;AACA;;AA5DA;AAAA;;AAAA;AAAA;AAAA;AA8DA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,gBA9DA,CA+DA;;AACA,qBAAA,aAAA,aAAA,MAAA,CAAA,QAAA,CAAA,MAAA;;AAhEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAoEA;AACA,IAAA,YArEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsEA,oBAAA;AACA;AACA;AACA,uBAAA,QAAA,GAAA,QAAA;AACA,iBAJA,CAIA,OAAA,KAAA,EAAA;AACA,kBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA;AACA,uBAAA,QAAA,GAAA,QAAA;AACA;;AA7EA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAgFA;AACA,IAAA,wBAjFA,sCAiFA;AACA,UAAA,WAAA,GAAA,KAAA,eAAA;;AAEA,UAAA,KAAA,QAAA,KAAA,MAAA,EAAA;AACA,aAAA,qBAAA,GAAA,EAAA;AACA,aAAA,mBAAA,GAAA,SAAA;AACA,aAAA,aAAA,GAAA,GAAA;AACA,aAAA,oBAAA,GAAA,CAAA;AACA,aAAA,aAAA,GAAA,QAAA;AACA,aAAA,aAAA,GAAA,EAAA;AACA,aAAA,aAAA,GAAA,SAAA;AACA,OARA,MAQA,IAAA,KAAA,QAAA,KAAA,KAAA,EAAA;AACA,YAAA,WAAA,IAAA,EAAA,EAAA;AACA,eAAA,qBAAA,GAAA,EAAA;AACA,eAAA,mBAAA,GAAA,UAAA;AACA,eAAA,aAAA,GAAA,GAAA;AACA,eAAA,oBAAA,GAAA,CAAA;AACA,eAAA,aAAA,GAAA,QAAA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,SAAA;AACA,SARA,MAQA,IAAA,WAAA,IAAA,EAAA,EAAA;AACA,eAAA,qBAAA,GAAA,EAAA;AACA,eAAA,mBAAA,GAAA,UAAA;AACA,eAAA,aAAA,GAAA,WAAA,GAAA,EAAA,GAAA,GAAA;AACA,eAAA,oBAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,UAAA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,SAAA;AACA,SARA,MAQA;AACA,eAAA,qBAAA,GAAA,EAAA;AACA,eAAA,mBAAA,GAAA,QAAA;AACA,eAAA,aAAA,GAAA,WAAA,GAAA,EAAA,GAAA,GAAA;AACA,eAAA,oBAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,UAAA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,SAAA;AACA;AACA,OA1BA,MA0BA;AACA;AACA,YAAA,WAAA,IAAA,EAAA,EAAA;AACA,eAAA,qBAAA,GAAA,EAAA;AACA,eAAA,mBAAA,GAAA,OAAA;AACA,eAAA,aAAA,GAAA,GAAA;AACA,eAAA,oBAAA,GAAA,CAAA;AACA,eAAA,aAAA,GAAA,QAAA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,SAAA;AACA,SARA,MAQA,IAAA,WAAA,IAAA,EAAA,EAAA;AACA,eAAA,qBAAA,GAAA,EAAA;AACA,eAAA,mBAAA,GAAA,OAAA;AACA,eAAA,aAAA,GAAA,WAAA,GAAA,EAAA,GAAA,GAAA;AACA,eAAA,oBAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,OAAA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,SAAA;AACA,SARA,MAQA;AACA,eAAA,qBAAA,GAAA,EAAA;AACA,eAAA,mBAAA,GAAA,OAAA;AACA,eAAA,aAAA,GAAA,WAAA,GAAA,EAAA,GAAA,GAAA;AACA,eAAA,oBAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,OAAA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,aAAA,GAAA,SAAA;AACA;AACA;AACA,KAlJA;AAoJA;AACA,IAAA,QArJA,sBAqJA;AAAA;;AACA,UAAA,CAAA,KAAA,aAAA,EAAA;AACA,aAAA,aAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,EAAA,SADA;AAEA,UAAA,WAAA,EAAA,iBAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA;AACA;;AAEA,MAAA,SAAA,CAAA,SAAA,CAAA,SAAA,CAAA,KAAA,aAAA,EAAA,IAAA,CAAA,YAAA;AACA,QAAA,KAAA,CAAA,aAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,EAAA,SADA;AAEA,UAAA,WAAA,EAAA,yBAFA;AAGA,UAAA,SAAA,EAAA,UAHA;AAIA,UAAA,QAAA,EAAA,CAJA;AAKA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA,OADA;AAEA,YAAA,SAAA,EAAA,OAFA;AAGA,YAAA,YAAA,EAAA,KAHA;AAIA,YAAA,SAAA,EAAA;AAJA;AALA,SAAA;AAYA,OAbA,EAaA,KAbA,CAaA,YAAA;AACA,QAAA,KAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,UAAA,OAAA,EAAA,MADA;AAEA,UAAA,WAAA,EAAA,gBAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA,OAnBA;AAoBA,KAnLA;AAqLA;AACA,IAAA,iBAtLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAuLA,CAAA,KAAA,aAAA,IAAA,KAAA,cAvLA;AAAA;AAAA;AAAA;;AAAA;;AAAA;AAAA;AA4LA,gBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EA5LA,CA8LA;;AA9LA;AAAA,uBA+LA,KAAA,KAAA,CAAA,IAAA,CAAA,wCAAA,EAAA,IAAA,EAAA;AACA,kBAAA,MAAA,EAAA;AACA,oBAAA,GAAA,EAAA,KAAA;AADA;AADA,iBAAA,CA/LA;;AAAA;AA+LA,gBAAA,QA/LA;;AAqMA,oBAAA,QAAA,IAAA,QAAA,CAAA,OAAA,EAAA;AACA;AACA,uBAAA,SAAA,GAAA,QAAA,CAAA,MAAA;AACA,uBAAA,cAAA,GAAA,IAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,KAAA,SAAA;AACA;;AA1MA;AAAA;;AAAA;AAAA;AAAA;AA4MA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,gBA5MA,CA6MA;;AA7MA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiNA;AACA,IAAA,cAlNA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAmNA,KAAA,aAnNA;AAAA;AAAA;AAAA;;AAoNA,qBAAA,aAAA,CAAA,OAAA,CAAA;AACA,kBAAA,OAAA,EAAA,SADA;AAEA,kBAAA,WAAA,EAAA,oBAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;AApNA;;AAAA;AAAA,sBA6NA,KAAA,cAAA,IAAA,KAAA,SA7NA;AAAA;AAAA;AAAA;;AA8NA,qBAAA,WAAA,GAAA,IAAA;AA9NA;;AAAA;AAAA;AAmOA,qBAAA,SAAA,GAAA,IAAA,CAnOA,CAqOA;;AArOA;AAAA,uBAsOA,KAAA,KAAA,CAAA,IAAA,CAAA,wCAAA,EAAA,IAAA,EAAA;AACA,kBAAA,MAAA,EAAA;AACA,oBAAA,GAAA,EAAA,KAAA;AADA;AADA,iBAAA,CAtOA;;AAAA;AAsOA,gBAAA,QAtOA;AA4OA,gBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA;;AA5OA,sBA8OA,QAAA,IAAA,QAAA,CAAA,OA9OA;AAAA;AAAA;AAAA;;AA+OA;AACA,qBAAA,SAAA,GAAA,QAAA,CAAA,MAAA;AACA,qBAAA,cAAA,GAAA,IAAA;AACA,qBAAA,WAAA,GAAA,IAAA;AAEA,qBAAA,aAAA,CAAA,OAAA,CAAA;AACA,kBAAA,OAAA,EAAA,SADA;AAEA,kBAAA,WAAA,EAAA,wBAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;AApPA;AAAA;;AAAA;AA0PA,gBAAA,QA1PA,GA0PA,QAAA,IAAA,QAAA,CAAA,OAAA,IAAA,MA1PA;AAAA,sBA2PA,IAAA,KAAA,CAAA,QAAA,CA3PA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA8PA,gBAAA,OAAA,CAAA,KAAA,CAAA,UAAA;AACA,qBAAA,aAAA,CAAA,KAAA,CAAA;AACA,kBAAA,OAAA,EAAA,MADA;AAEA,kBAAA,WAAA,EAAA,aAAA,OAAA,IAAA,eAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;;AA/PA;AAAA;AAqQA,qBAAA,SAAA,GAAA,KAAA;AArQA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAyQA;AACA,IAAA,cA1QA,4BA0QA;AACA,UAAA,CAAA,KAAA,SAAA,EAAA;;AAEA,UAAA;AACA;AACA,YAAA,WAAA,wDAAA,kBAAA,CAAA,KAAA,SAAA,CAAA,CAAA,CAFA,CAIA;;AACA,YAAA,MAAA,GAAA,QAAA,CAAA,aAAA,CAAA,QAAA,CAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,QAAA,MAAA,CAAA,GAAA,GAAA,WAAA;AACA,QAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,EARA,CAUA;;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA;AACA,SAFA,EAEA,IAFA,CAAA;AAIA,aAAA,aAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,EAAA,MADA;AAEA,UAAA,WAAA,EAAA,YAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA,OApBA,CAoBA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,UAAA,EAAA,KAAA;AACA,aAAA,aAAA,CAAA,KAAA,CAAA;AACA,UAAA,OAAA,EAAA,MADA;AAEA,UAAA,WAAA,EAAA,eAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA;AACA,KAzSA;AA2SA;AACA,IAAA,iBA5SA,+BA4SA;AACA,UAAA,KAAA,iBAAA,GAAA,GAAA,EAAA;AACA,aAAA,aAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,EAAA,QADA;AAEA,UAAA,WAAA,EAAA,yBAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA;AACA;;AACA,WAAA,iBAAA,GAAA,IAAA;AACA,KAtTA;AAwTA;AACA,IAAA,cAzTA,4BAyTA;AAAA;;AACA,WAAA,YAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA,MAAA,EAAA;AACA,YAAA,GAAA,EAAA;AAEA,QAAA,MAAA,CAAA,eAAA,GAAA,IAAA,CAHA,CAKA;;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,MAAA,CAAA,eAAA,GAAA,KAAA;AACA,UAAA,MAAA,CAAA,iBAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,YAAA,CAAA,WAAA;;AAEA,UAAA,MAAA,CAAA,aAAA,CAAA,OAAA,CAAA;AACA,YAAA,OAAA,EAAA,QADA;AAEA,YAAA,WAAA,EAAA,uBAFA;AAGA,YAAA,SAAA,EAAA;AAHA,WAAA;AAKA,SAVA,EAUA,IAVA,CAAA;AAWA,OAjBA;AAkBA,KA5UA;AAgVA;AACA,IAAA,iBAjVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmVA,qBAAA,YAAA,GAAA,IAAA;AAEA,gBAAA,MArVA,GAqVA;AACA,kBAAA,OAAA,EAAA,CADA;AAEA,kBAAA,IAAA,EAAA;AAFA,iBArVA;AAAA;AAAA,uBA0VA,KAAA,KAAA,CAAA,GAAA,CAAA,8BAAA,EAAA;AAAA,kBAAA,MAAA,EAAA;AAAA,iBAAA,CA1VA;;AAAA;AA0VA,gBAAA,QA1VA;;AA4VA,oBAAA,QAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,kBAAA,OADA,GACA,QAAA,CAAA,IAAA,CAAA,MAAA,IAAA,QAAA,CAAA,IAAA,CAAA,MAAA,CAAA,OAAA,IAAA,EADA,EAEA;;AACA,uBAAA,aAAA,GAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA;AAAA,2BAAA;AACA,sBAAA,GAAA,EAAA,IAAA,CAAA,EAAA,IAAA,KADA;AAEA,sBAAA,QAAA,EAAA,IAAA,CAAA,gBAAA,6BAAA,KAAA,GAAA,CAAA,CAFA;AAGA,sBAAA,MAAA,EAAA,IAAA,CAAA,cAAA,IAAA,EAHA;AAIA,sBAAA,YAAA,EAAA,IAAA,CAAA,aAAA,IAAA,EAJA;AAKA,sBAAA,MAAA,EAAA,IAAA,CAAA,cAAA,GAAA,KAAA,GAAA,KALA;AAMA,sBAAA,MAAA,EAAA,IAAA,CAAA,YAAA,IAAA;AANA,qBAAA;AAAA,mBAAA,CAAA;AAQA,iBAXA,MAWA;AACA,uBAAA,aAAA,GAAA,EAAA;AACA;;AAzWA;AAAA;;AAAA;AAAA;AAAA;AA2WA,gBAAA,OAAA,CAAA,KAAA,CAAA,aAAA;AACA,qBAAA,aAAA,GAAA,EAAA;;AA5WA;AAAA;AA8WA,qBAAA,YAAA,GAAA,KAAA;AA9WA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAkXA;AACA,IAAA,mBAnXA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqXA,qBAAA,cAAA,GAAA,IAAA;AAEA,gBAAA,MAvXA,GAuXA;AACA,kBAAA,OAAA,EAAA,CADA;AAEA,kBAAA,IAAA,EAAA;AAFA,iBAvXA;AAAA;AAAA,uBA4XA,KAAA,KAAA,CAAA,GAAA,CAAA,mCAAA,EAAA;AAAA,kBAAA,MAAA,EAAA;AAAA,iBAAA,CA5XA;;AAAA;AA4XA,gBAAA,QA5XA;;AA8XA,oBAAA,QAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,kBAAA,OADA,GACA,QAAA,CAAA,IAAA,CAAA,MAAA,IAAA,QAAA,CAAA,IAAA,CAAA,MAAA,CAAA,OAAA,IAAA,EADA,EAEA;;AACA,uBAAA,eAAA,GAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA;AAAA,2BAAA;AACA,sBAAA,GAAA,EAAA,IAAA,CAAA,EAAA,IAAA,KADA;AAEA,sBAAA,MAAA,EAAA,IAAA,CAAA,MAAA,IAAA,MAFA;AAGA,sBAAA,MAAA,EAAA,IAAA,CAAA,gBAAA,IAAA,KAHA;AAIA,sBAAA,SAAA,EAAA,IAAA,CAAA,SAAA,IAAA,EAJA;AAKA,sBAAA,MAAA,EAAA,MAAA,CAAA,qBAAA,CAAA,IAAA,CAAA,MAAA,CALA;AAMA,sBAAA,YAAA,EAAA,IAAA,CAAA,YAAA,IAAA;AANA,qBAAA;AAAA,mBAAA,CAAA;AAQA,iBAXA,MAWA;AACA,uBAAA,eAAA,GAAA,EAAA;AACA;;AA3YA;AAAA;;AAAA;AAAA;AAAA;AA6YA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,eAAA,GAAA,EAAA;;AA9YA;AAAA;AAgZA,qBAAA,cAAA,GAAA,KAAA;AAhZA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAoZA;AACA,IAAA,qBArZA,iCAqZA,MArZA,EAqZA;AACA,UAAA,SAAA,GAAA;AACA,WAAA,KADA;AAEA,WAAA,KAFA;AAGA,WAAA,KAHA;AAIA,WAAA;AAJA,OAAA;AAMA,aAAA,SAAA,CAAA,MAAA,CAAA,IAAA,MAAA;AACA,KA7ZA;AA+ZA;AACA,IAAA,cAhaA,0BAgaA,MAhaA,EAgaA;AACA,UAAA,QAAA,GAAA;AACA,eAAA,OADA;AAEA,eAAA,MAFA;AAGA,eAAA,KAHA;AAIA,eAAA;AAJA,OAAA;AAMA,aAAA,QAAA,CAAA,MAAA,CAAA,IAAA,SAAA;AACA,KAxaA;AA0aA;AACA,IAAA,YA3aA,wBA2aA,GA3aA,EA2aA;AACA,UAAA,GAAA,KAAA,IAAA,IAAA,GAAA,KAAA,SAAA,EAAA,OAAA,GAAA;AACA,UAAA,MAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,UAAA,KAAA,CAAA,MAAA,CAAA,EAAA,OAAA,GAAA,CAHA,CAKA;;AACA,UAAA,GAAA,KAAA,KAAA,aAAA,EAAA;AACA,eAAA,MAAA,CAAA,cAAA,CAAA,OAAA,EAAA;AACA,UAAA,qBAAA,EAAA,CADA;AAEA,UAAA,qBAAA,EAAA;AAFA,SAAA,CAAA;AAIA,OAXA,CAaA;;;AACA,aAAA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA;AACA;AA1bA;AAlHA,CAAA", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"affiliate-container\">\n      <!-- 简洁页面标题 -->\n      <div class=\"simple-header\">\n        <h1 class=\"simple-title\">分销推广</h1>\n        <p class=\"simple-subtitle\">加入分销计划，推广智界AIGC获得丰厚佣金</p>\n        <div class=\"commission-badge\">\n          <span class=\"badge-text\">当前佣金率：{{ currentCommissionRate }}%</span>\n          <span class=\"badge-level\">{{ commissionLevelText }}</span>\n        </div>\n      </div>\n\n      <!-- 分销内容区域 -->\n      <section class=\"affiliate-section\">\n        <div class=\"container\">\n          <!-- 推广链接区域 - 最显眼位置 -->\n          <div class=\"promotion-link-section\">\n            <h2 class=\"section-title\">您的专属推广链接</h2>\n            <div class=\"link-main-container\">\n              <div class=\"link-input-large\">\n                <a-input\n                  :value=\"affiliateLink || '正在生成推广链接...'\"\n                  readonly\n                  :loading=\"loading\"\n                  size=\"large\"\n                  placeholder=\"推广链接生成中...\"\n                />\n              </div>\n              <div class=\"link-actions\">\n                <a-button\n                  type=\"primary\"\n                  size=\"large\"\n                  :disabled=\"!affiliateLink || loading\"\n                  @click=\"copyLink\"\n                  class=\"copy-btn\"\n                >\n                  <a-icon type=\"copy\" />\n                  复制链接\n                </a-button>\n                <a-button\n                  size=\"large\"\n                  :loading=\"qrLoading\"\n                  @click=\"generateQRCode\"\n                  class=\"qr-btn\"\n                >\n                  <a-icon type=\"qrcode\" />\n                  推广二维码\n                </a-button>\n              </div>\n            </div>\n            <div class=\"link-tips\">\n              <a-icon type=\"info-circle\" />\n              分享此链接，好友注册并订阅会员后，您将获得 <strong>{{ currentCommissionRate }}%</strong> 的佣金奖励\n            </div>\n          </div>\n\n          <!-- 收益展示 -->\n          <div class=\"earnings-dashboard\">\n            <h2 class=\"section-title\">收益概览</h2>\n            <div class=\"earnings-grid\">\n              <div class=\"earning-card primary\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"dollar\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">¥{{ formatNumber(totalEarnings) }}</div>\n                    <div class=\"earning-label\">累计收益</div>\n                  </a-spin>\n                </div>\n              </div>\n\n              <div class=\"earning-card success\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"wallet\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">¥{{ formatNumber(availableEarnings) }}</div>\n                    <div class=\"earning-label\">可提现金额</div>\n                  </a-spin>\n                  <div class=\"card-action\">\n                    <a-button\n                      type=\"primary\"\n                      size=\"small\"\n                      :disabled=\"availableEarnings <= 0 || loading\"\n                      @click=\"openWithdrawModal\"\n                    >\n                      立即提现\n                    </a-button>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"earning-card info\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"team\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">{{ formatNumber(totalReferrals) }}</div>\n                    <div class=\"earning-label\">推荐注册</div>\n                  </a-spin>\n                </div>\n              </div>\n\n              <div class=\"earning-card warning\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"crown\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">{{ formatNumber(memberReferrals) }}</div>\n                    <div class=\"earning-label\">转化人数</div>\n                  </a-spin>\n                </div>\n              </div>\n\n\n            </div>\n          </div>\n\n          <!-- 佣金等级进度 -->\n          <div class=\"commission-progress\">\n            <h2 class=\"section-title\">佣金等级进度</h2>\n            <div class=\"progress-card\">\n              <div class=\"current-level\">\n                <div class=\"level-info\">\n                  <span class=\"level-name\">{{ commissionLevelText }}</span>\n                  <span class=\"level-rate\">{{ currentCommissionRate }}%佣金</span>\n                </div>\n                <div class=\"level-progress\">\n                  <a-progress\n                    :percent=\"levelProgress\"\n                    :stroke-color=\"progressColor\"\n                    :show-info=\"false\"\n                  />\n                  <div class=\"progress-text\">\n                    {{ memberReferrals }}/{{ nextLevelRequirement }} 转化用户\n                  </div>\n                </div>\n              </div>\n              <div class=\"next-level\" v-if=\"nextLevelRequirement > 0\">\n                <div class=\"next-info\">\n                  <span class=\"next-text\">下一等级：{{ nextLevelText }}</span>\n                  <span class=\"next-rate\">{{ nextLevelRate }}%佣金</span>\n                </div>\n                <div class=\"remaining\">\n                  还需 {{ nextLevelRequirement - memberReferrals }} 个转化用户\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 分成规则说明 -->\n          <div class=\"commission-rules\">\n            <h2 class=\"section-title\">分成规则</h2>\n            <div class=\"rules-table\">\n              <div class=\"rule-row header\">\n                <div class=\"rule-cell\">用户等级</div>\n                <div class=\"rule-cell\">推广人数要求</div>\n                <div class=\"rule-cell\">佣金比例</div>\n                <div class=\"rule-cell\">说明</div>\n              </div>\n              <div class=\"rule-row\">\n                <div class=\"rule-cell\">普通用户</div>\n                <div class=\"rule-cell\">0-9人</div>\n                <div class=\"rule-cell highlight\">30%</div>\n                <div class=\"rule-cell\">新手推广员</div>\n              </div>\n              <div class=\"rule-row\">\n                <div class=\"rule-cell\">普通用户</div>\n                <div class=\"rule-cell\">10-29人</div>\n                <div class=\"rule-cell highlight\">40%</div>\n                <div class=\"rule-cell\">高级推广员</div>\n              </div>\n              <div class=\"rule-row\">\n                <div class=\"rule-cell\">普通用户</div>\n                <div class=\"rule-cell\">30人以上</div>\n                <div class=\"rule-cell highlight\">50%</div>\n                <div class=\"rule-cell\">顶级推广员</div>\n              </div>\n              <div class=\"rule-row vip\">\n                <div class=\"rule-cell\">VIP用户</div>\n                <div class=\"rule-cell\">基础+5%</div>\n                <div class=\"rule-cell highlight\">35%-50%</div>\n                <div class=\"rule-cell\">VIP推广员</div>\n              </div>\n              <div class=\"rule-row svip\">\n                <div class=\"rule-cell\">SVIP用户</div>\n                <div class=\"rule-cell\">无要求</div>\n                <div class=\"rule-cell highlight\">50%</div>\n                <div class=\"rule-cell\">SVIP推广员</div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 推广用户列表 -->\n          <div class=\"referral-users\">\n            <h2 class=\"section-title\">我的推广用户</h2>\n            <div class=\"users-table-container\">\n              <a-table\n                :columns=\"userColumns\"\n                :data-source=\"referralUsers\"\n                :loading=\"usersLoading\"\n                :pagination=\"{ pageSize: 10, showSizeChanger: false }\"\n                size=\"middle\"\n              >\n                <template slot=\"avatar\" slot-scope=\"text, record\">\n                  <a-avatar :src=\"record.avatar\" :style=\"{ backgroundColor: '#87d068' }\">\n                    {{ record.nickname ? record.nickname.charAt(0) : 'U' }}\n                  </a-avatar>\n                </template>\n                <template slot=\"status\" slot-scope=\"text\">\n                  <a-tag :color=\"text === '已转化' ? 'green' : 'blue'\">\n                    {{ text }}\n                  </a-tag>\n                </template>\n                <template slot=\"reward\" slot-scope=\"text\">\n                  <span class=\"reward-amount\">¥{{ text || '0.00' }}</span>\n                </template>\n              </a-table>\n            </div>\n          </div>\n\n          <!-- 提现记录 -->\n          <div class=\"withdraw-records\">\n            <h2 class=\"section-title\">提现记录</h2>\n            <div class=\"records-table-container\">\n              <a-table\n                :columns=\"withdrawColumns\"\n                :data-source=\"withdrawRecords\"\n                :loading=\"recordsLoading\"\n                :pagination=\"{ pageSize: 10, showSizeChanger: false }\"\n                size=\"middle\"\n              >\n                <template slot=\"status\" slot-scope=\"text\">\n                  <a-tag :color=\"getStatusColor(text)\">\n                    {{ text }}\n                  </a-tag>\n                </template>\n                <template slot=\"amount\" slot-scope=\"text\">\n                  <span class=\"withdraw-amount\">¥{{ text }}</span>\n                </template>\n              </a-table>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 二维码弹窗 -->\n      <a-modal\n        v-model=\"showQRModal\"\n        title=\"推广二维码\"\n        :footer=\"null\"\n        width=\"400px\"\n        centered\n      >\n        <div class=\"qr-modal-content\">\n          <div class=\"qr-code-container\" v-if=\"qrCodeUrl\">\n            <img :src=\"qrCodeUrl\" alt=\"推广二维码\" class=\"qr-code-image\" />\n          </div>\n          <div class=\"qr-actions\">\n            <a-button type=\"primary\" block @click=\"downloadQRCode\" v-if=\"qrCodeUrl\">\n              <a-icon type=\"download\" />\n              下载二维码\n            </a-button>\n          </div>\n        </div>\n      </a-modal>\n\n      <!-- 提现弹窗 -->\n      <a-modal\n        v-model=\"showWithdrawModal\"\n        title=\"申请提现\"\n        :footer=\"null\"\n        width=\"500px\"\n        centered\n      >\n        <div class=\"withdraw-modal-content\">\n          <div class=\"withdraw-info\">\n            <div class=\"info-item\">\n              <span class=\"info-label\">可提现金额：</span>\n              <span class=\"info-value\">¥{{ formatNumber(availableEarnings) }}</span>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"info-label\">最低提现金额：</span>\n              <span class=\"info-value\">¥100.00</span>\n            </div>\n          </div>\n\n          <a-form :form=\"withdrawForm\" @submit=\"handleWithdraw\">\n            <a-form-item label=\"提现金额\">\n              <a-input-number\n                v-decorator=\"['amount', {\n                  rules: [\n                    { required: true, message: '请输入提现金额' },\n                    { type: 'number', min: 100, message: '最低提现金额为100元' },\n                    { type: 'number', max: availableEarnings, message: '提现金额不能超过可提现金额' }\n                  ]\n                }]\"\n                :min=\"100\"\n                :max=\"availableEarnings\"\n                :precision=\"2\"\n                style=\"width: 100%\"\n                placeholder=\"请输入提现金额\"\n              >\n                <template slot=\"addonAfter\">元</template>\n              </a-input-number>\n            </a-form-item>\n\n            <a-form-item label=\"提现方式\">\n              <a-select\n                v-decorator=\"['method', {\n                  rules: [{ required: true, message: '请选择提现方式' }],\n                  initialValue: 'alipay'\n                }]\"\n                placeholder=\"请选择提现方式\"\n              >\n                <a-select-option value=\"alipay\">支付宝</a-select-option>\n                <a-select-option value=\"wechat\">微信</a-select-option>\n                <a-select-option value=\"bank\">银行卡</a-select-option>\n              </a-select>\n            </a-form-item>\n\n            <a-form-item label=\"收款账号\">\n              <a-input\n                v-decorator=\"['account', {\n                  rules: [{ required: true, message: '请输入收款账号' }]\n                }]\"\n                placeholder=\"请输入收款账号\"\n              />\n            </a-form-item>\n\n            <a-form-item label=\"收款人姓名\">\n              <a-input\n                v-decorator=\"['name', {\n                  rules: [{ required: true, message: '请输入收款人姓名' }]\n                }]\"\n                placeholder=\"请输入收款人姓名\"\n              />\n            </a-form-item>\n          </a-form>\n\n          <div class=\"withdraw-actions\">\n            <a-button @click=\"showWithdrawModal = false\" style=\"margin-right: 8px\">\n              取消\n            </a-button>\n            <a-button\n              type=\"primary\"\n              :loading=\"withdrawLoading\"\n              @click=\"handleWithdraw\"\n            >\n              申请提现\n            </a-button>\n          </div>\n        </div>\n      </a-modal>\n    </div>\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport { getReferralStats, generateReferralLink } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport Vue from 'vue'\n\nexport default {\n  name: 'Affiliate',\n  components: {\n    WebsitePage\n  },\n  data() {\n    return {\n      loading: true,\n      qrLoading: false,\n\n      // 收益数据\n      totalEarnings: 0,\n      availableEarnings: 0,\n      totalReferrals: 0,\n      memberReferrals: 0,\n\n      // 推广链接\n      affiliateLink: '',\n\n      // 佣金等级\n      userRole: 'NORMAL', // NORMAL, VIP, SVIP\n      currentCommissionRate: 30,\n      commissionLevelText: '新手推广员',\n      levelProgress: 0,\n      nextLevelRequirement: 10,\n      nextLevelText: '高级推广员',\n      nextLevelRate: 40,\n      progressColor: '#1890ff',\n\n      // 二维码\n      showQRModal: false,\n      qrCodeUrl: '',\n      qrPreGenerated: false, // 是否已预生成二维码\n\n      // 提现\n      showWithdrawModal: false,\n      withdrawLoading: false,\n      withdrawForm: this.$form.createForm(this),\n\n      // 推广用户列表\n      referralUsers: [],\n      usersLoading: false,\n      userColumns: [\n        {\n          title: '头像',\n          dataIndex: 'avatar',\n          key: 'avatar',\n          scopedSlots: { customRender: 'avatar' },\n          width: 80\n        },\n        {\n          title: '用户昵称',\n          dataIndex: 'nickname',\n          key: 'nickname'\n        },\n        {\n          title: '注册时间',\n          dataIndex: 'registerTime',\n          key: 'registerTime'\n        },\n        {\n          title: '转化状态',\n          dataIndex: 'status',\n          key: 'status',\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '获得奖励',\n          dataIndex: 'reward',\n          key: 'reward',\n          scopedSlots: { customRender: 'reward' }\n        }\n      ],\n\n      // 提现记录\n      withdrawRecords: [],\n      recordsLoading: false,\n      withdrawColumns: [\n        {\n          title: '提现金额',\n          dataIndex: 'amount',\n          key: 'amount',\n          scopedSlots: { customRender: 'amount' }\n        },\n        {\n          title: '提现方式',\n          dataIndex: 'method',\n          key: 'method'\n        },\n        {\n          title: '申请时间',\n          dataIndex: 'applyTime',\n          key: 'applyTime'\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '完成时间',\n          dataIndex: 'completeTime',\n          key: 'completeTime'\n        }\n      ],\n\n      // 用户信息\n      userInfo: null\n    }\n  },\n  async mounted() {\n    await this.checkLoginAndLoadData()\n  },\n  methods: {\n    // 检查登录状态并加载数据\n    async checkLoginAndLoadData() {\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      if (!token) {\n        this.$router.push({ path: '/login', query: { redirect: this.$route.fullPath } })\n        return\n      }\n\n      try {\n        await Promise.all([\n          this.loadReferralData(),\n          this.loadReferralLink(),\n          this.loadUserRole(),\n          this.loadReferralUsers(),\n          this.loadWithdrawRecords()\n        ])\n\n        // 计算佣金等级\n        this.calculateCommissionLevel()\n\n        // 自动预生成二维码\n        this.preGenerateQRCode()\n      } catch (error) {\n        console.error('加载分销数据失败:', error)\n        this.$notification.error({\n          message: '加载失败',\n          description: '获取分销数据失败，请稍后重试',\n          placement: 'topRight'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载推荐统计数据\n    async loadReferralData() {\n      try {\n        const response = await getReferralStats()\n        if (response.success) {\n          const data = response.result\n          this.totalEarnings = data.total_reward_amount || 0\n          this.availableEarnings = data.available_rewards || 0\n          this.totalReferrals = data.total_referrals || 0\n          this.memberReferrals = data.member_referrals || 0\n\n\n        }\n      } catch (error) {\n        console.error('获取推荐统计失败:', error)\n        throw error\n      }\n    },\n\n    // 加载推荐链接\n    async loadReferralLink() {\n      try {\n        const response = await generateReferralLink({})\n        if (response.success) {\n          this.affiliateLink = response.result || ''\n        }\n      } catch (error) {\n        console.error('获取推荐链接失败:', error)\n        // 如果获取失败，使用默认链接格式\n        this.affiliateLink = `${window.location.origin}?ref=loading...`\n      }\n    },\n\n    // 加载用户角色信息\n    async loadUserRole() {\n      try {\n        // TODO: 从用户信息API获取用户角色\n        // 暂时使用默认值\n        this.userRole = 'NORMAL'\n      } catch (error) {\n        console.error('获取用户角色失败:', error)\n        this.userRole = 'NORMAL'\n      }\n    },\n\n    // 计算佣金等级和进度\n    calculateCommissionLevel() {\n      const memberCount = this.memberReferrals\n\n      if (this.userRole === 'SVIP') {\n        this.currentCommissionRate = 50\n        this.commissionLevelText = 'SVIP推广员'\n        this.levelProgress = 100\n        this.nextLevelRequirement = 0\n        this.nextLevelText = '已达最高等级'\n        this.nextLevelRate = 50\n        this.progressColor = '#722ed1'\n      } else if (this.userRole === 'VIP') {\n        if (memberCount >= 30) {\n          this.currentCommissionRate = 50\n          this.commissionLevelText = 'VIP顶级推广员'\n          this.levelProgress = 100\n          this.nextLevelRequirement = 0\n          this.nextLevelText = '已达最高等级'\n          this.nextLevelRate = 50\n          this.progressColor = '#722ed1'\n        } else if (memberCount >= 10) {\n          this.currentCommissionRate = 45\n          this.commissionLevelText = 'VIP高级推广员'\n          this.levelProgress = (memberCount / 30) * 100\n          this.nextLevelRequirement = 30\n          this.nextLevelText = 'VIP顶级推广员'\n          this.nextLevelRate = 50\n          this.progressColor = '#13c2c2'\n        } else {\n          this.currentCommissionRate = 35\n          this.commissionLevelText = 'VIP推广员'\n          this.levelProgress = (memberCount / 10) * 100\n          this.nextLevelRequirement = 10\n          this.nextLevelText = 'VIP高级推广员'\n          this.nextLevelRate = 45\n          this.progressColor = '#1890ff'\n        }\n      } else {\n        // NORMAL用户\n        if (memberCount >= 30) {\n          this.currentCommissionRate = 50\n          this.commissionLevelText = '顶级推广员'\n          this.levelProgress = 100\n          this.nextLevelRequirement = 0\n          this.nextLevelText = '已达最高等级'\n          this.nextLevelRate = 50\n          this.progressColor = '#722ed1'\n        } else if (memberCount >= 10) {\n          this.currentCommissionRate = 40\n          this.commissionLevelText = '高级推广员'\n          this.levelProgress = (memberCount / 30) * 100\n          this.nextLevelRequirement = 30\n          this.nextLevelText = '顶级推广员'\n          this.nextLevelRate = 50\n          this.progressColor = '#13c2c2'\n        } else {\n          this.currentCommissionRate = 30\n          this.commissionLevelText = '新手推广员'\n          this.levelProgress = (memberCount / 10) * 100\n          this.nextLevelRequirement = 10\n          this.nextLevelText = '高级推广员'\n          this.nextLevelRate = 40\n          this.progressColor = '#1890ff'\n        }\n      }\n    },\n\n     // 复制推广链接\n     copyLink() {\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '推广链接未生成',\n          description: '推广链接正在生成中，请稍后再试',\n          placement: 'topRight'\n        })\n        return\n      }\n      \n      navigator.clipboard.writeText(this.affiliateLink).then(() => {\n        this.$notification.success({\n          message: '推广链接已复制',\n          description: '推广链接已成功复制到剪贴板，快去分享给好友吧！',\n          placement: 'topRight',\n          duration: 3,\n          style: {\n            width: '380px',\n            marginTop: '101px',\n            borderRadius: '8px',\n            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n          }\n        })\n      }).catch(() => {\n        this.$notification.error({\n          message: '复制失败',\n          description: '复制推广链接失败，请手动复制',\n          placement: 'topRight'\n        })\n      })\n    },\n\n    // 预生成二维码（后台静默生成）\n    async preGenerateQRCode() {\n      if (!this.affiliateLink || this.qrPreGenerated) {\n        return\n      }\n\n      try {\n        console.log('开始预生成二维码...')\n\n        // 调用后端API生成二维码并上传到TOS\n        const response = await this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n          params: {\n            url: this.affiliateLink\n          }\n        })\n\n        if (response && response.success) {\n          // 静默保存二维码URL\n          this.qrCodeUrl = response.result\n          this.qrPreGenerated = true\n          console.log('二维码预生成成功:', this.qrCodeUrl)\n        }\n      } catch (error) {\n        console.error('预生成二维码失败:', error)\n        // 预生成失败不显示错误提示，用户点击时再重试\n      }\n    },\n\n    // 生成推广二维码（用户主动点击）\n    async generateQRCode() {\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '推广链接未生成',\n          description: '请等待推广链接生成完成后再生成二维码',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      // 如果已经预生成，直接显示\n      if (this.qrPreGenerated && this.qrCodeUrl) {\n        this.showQRModal = true\n        return\n      }\n\n      try {\n        this.qrLoading = true\n\n        // 调用后端API生成二维码并上传到TOS\n        const response = await this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n          params: {\n            url: this.affiliateLink\n          }\n        })\n\n        console.log('二维码生成响应:', response)\n\n        if (response && response.success) {\n          // 使用CDN地址\n          this.qrCodeUrl = response.result\n          this.qrPreGenerated = true\n          this.showQRModal = true\n\n          this.$notification.success({\n            message: '二维码生成成功',\n            description: '推广二维码已生成并存储到CDN，可以下载保存',\n            placement: 'topRight'\n          })\n        } else {\n          const errorMsg = (response && response.message) || '生成失败'\n          throw new Error(errorMsg)\n        }\n      } catch (error) {\n        console.error('生成二维码失败:', error)\n        this.$notification.error({\n          message: '生成失败',\n          description: error.message || '二维码生成失败，请稍后重试',\n          placement: 'topRight'\n        })\n      } finally {\n        this.qrLoading = false\n      }\n    },\n\n    // 下载二维码\n    downloadQRCode() {\n      if (!this.qrCodeUrl) return\n\n      try {\n        // 通过后端代理下载，避免CORS问题\n        const downloadUrl = `/api/usercenter/downloadReferralQRCode?url=${encodeURIComponent(this.qrCodeUrl)}`\n\n        // 创建隐藏的iframe来触发下载\n        const iframe = document.createElement('iframe')\n        iframe.style.display = 'none'\n        iframe.src = downloadUrl\n        document.body.appendChild(iframe)\n\n        // 2秒后移除iframe\n        setTimeout(() => {\n          document.body.removeChild(iframe)\n        }, 2000)\n\n        this.$notification.success({\n          message: '下载开始',\n          description: '二维码正在下载到本地',\n          placement: 'topRight'\n        })\n      } catch (error) {\n        console.error('下载二维码失败:', error)\n        this.$notification.error({\n          message: '下载失败',\n          description: '二维码下载失败，请稍后重试',\n          placement: 'topRight'\n        })\n      }\n    },\n\n    // 显示提现弹窗\n    openWithdrawModal() {\n      if (this.availableEarnings < 100) {\n        this.$notification.warning({\n          message: '提现金额不足',\n          description: '最低提现金额为100元，请继续推广获得更多收益',\n          placement: 'topRight'\n        })\n        return\n      }\n      this.showWithdrawModal = true\n    },\n\n    // 处理提现申请\n    handleWithdraw() {\n      this.withdrawForm.validateFields((err, values) => {\n        if (err) return\n\n        this.withdrawLoading = true\n\n        // TODO: 调用提现API\n        setTimeout(() => {\n          this.withdrawLoading = false\n          this.showWithdrawModal = false\n          this.withdrawForm.resetFields()\n\n          this.$notification.success({\n            message: '提现申请成功',\n            description: '您的提现申请已提交，预计1-3个工作日到账',\n            placement: 'topRight'\n          })\n        }, 2000)\n      })\n    },\n\n\n\n    // 加载推广用户列表\n    async loadReferralUsers() {\n      try {\n        this.usersLoading = true\n\n        const params = {\n          current: 1,\n          size: 10\n        }\n\n        const response = await this.$http.get('/api/usercenter/referralList', { params })\n\n        if (response.data.success) {\n          const records = (response.data.result && response.data.result.records) || []\n          // 转换数据格式\n          this.referralUsers = records.map((item, index) => ({\n            key: item.id || index,\n            nickname: item.referee_nickname || `用户***${index + 1}`,\n            avatar: item.referee_avatar || '',\n            registerTime: item.register_time || '',\n            status: item.has_membership ? '已转化' : '已注册',\n            reward: item.total_reward || '0.00'\n          }))\n        } else {\n          this.referralUsers = []\n        }\n      } catch (error) {\n        console.error('获取推广用户列表失败:', error)\n        this.referralUsers = []\n      } finally {\n        this.usersLoading = false\n      }\n    },\n\n    // 加载提现记录\n    async loadWithdrawRecords() {\n      try {\n        this.recordsLoading = true\n\n        const params = {\n          current: 1,\n          size: 10\n        }\n\n        const response = await this.$http.get('/api/usercenter/withdrawalHistory', { params })\n\n        if (response.data.success) {\n          const records = (response.data.result && response.data.result.records) || []\n          // 转换数据格式\n          this.withdrawRecords = records.map((item, index) => ({\n            key: item.id || index,\n            amount: item.amount || '0.00',\n            method: item.withdrawalMethod || '支付宝',\n            applyTime: item.applyTime || '',\n            status: this.getWithdrawStatusText(item.status),\n            completeTime: item.completeTime || '-'\n          }))\n        } else {\n          this.withdrawRecords = []\n        }\n      } catch (error) {\n        console.error('获取提现记录失败:', error)\n        this.withdrawRecords = []\n      } finally {\n        this.recordsLoading = false\n      }\n    },\n\n    // 获取提现状态文本\n    getWithdrawStatusText(status) {\n      const statusMap = {\n        0: '待审核',\n        1: '处理中',\n        2: '已完成',\n        3: '已拒绝'\n      }\n      return statusMap[status] || '未知状态'\n    },\n\n    // 获取状态颜色\n    getStatusColor(status) {\n      const colorMap = {\n        '已完成': 'green',\n        '处理中': 'blue',\n        '已拒绝': 'red',\n        '待审核': 'orange'\n      }\n      return colorMap[status] || 'default'\n    },\n\n     // 格式化数字显示\n     formatNumber(num) {\n       if (num === null || num === undefined) return '0'\n       const number = parseFloat(num)\n       if (isNaN(number)) return '0'\n       \n       // 如果是金额，保留两位小数\n       if (num === this.totalEarnings) {\n         return number.toLocaleString('zh-CN', {\n           minimumFractionDigits: 2,\n           maximumFractionDigits: 2\n         })\n       }\n       \n       // 其他数字不保留小数\n       return number.toLocaleString('zh-CN')\n     }\n   }\n }\n</script>\n\n<style scoped>\n.affiliate-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 3rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0 0 1.5rem 0;\n}\n\n.commission-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 12px;\n  background: white;\n  padding: 12px 24px;\n  border-radius: 50px;\n  border: 2px solid #e2e8f0;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.badge-text {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.badge-level {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n/* 分销内容区域 */\n.affiliate-section {\n  padding: 4rem 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.section-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 1.5rem 0;\n  text-align: center;\n}\n\n/* 收益仪表板 */\n.earnings-dashboard {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  margin-bottom: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.earnings-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n  gap: 24px;\n}\n\n.earning-card {\n  display: flex;\n  align-items: center;\n  padding: 24px;\n  border-radius: 16px;\n  background: white;\n  border: 2px solid #f1f5f9;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.earning-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: var(--card-color);\n}\n\n.earning-card.primary {\n  --card-color: #3b82f6;\n}\n\n.earning-card.success {\n  --card-color: #10b981;\n}\n\n.earning-card.warning {\n  --card-color: #f59e0b;\n}\n\n.earning-card.info {\n  --card-color: #8b5cf6;\n}\n\n.earning-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);\n  border-color: var(--card-color);\n}\n\n.card-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  color: white;\n  font-size: 20px;\n  background: var(--card-color);\n}\n\n.card-content {\n  flex: 1;\n}\n\n.earning-number {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 4px;\n  line-height: 1;\n}\n\n.earning-label {\n  font-size: 0.9rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n/* 佣金等级进度 */\n.commission-progress {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  margin-bottom: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.progress-card {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  border-radius: 16px;\n  padding: 32px;\n  border: 2px solid #e2e8f0;\n}\n\n.current-level {\n  margin-bottom: 24px;\n}\n\n.level-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.level-name {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.level-rate {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 6px 16px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 600;\n}\n\n.level-progress {\n  margin-bottom: 8px;\n}\n\n.progress-text {\n  text-align: center;\n  font-size: 0.9rem;\n  color: #6b7280;\n  margin-top: 8px;\n}\n\n.next-level {\n  padding-top: 24px;\n  border-top: 1px solid #e5e7eb;\n}\n\n.next-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.next-text {\n  font-size: 1rem;\n  color: #374151;\n  font-weight: 500;\n}\n\n.next-rate {\n  background: #f3f4f6;\n  color: #6b7280;\n  padding: 4px 12px;\n  border-radius: 16px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.remaining {\n  font-size: 0.9rem;\n  color: #9ca3af;\n  text-align: center;\n}\n\n/* 推广工具 */\n.tools-section {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.tools-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 32px;\n}\n\n.tool-card {\n  background: #fafbfc;\n  border: 2px solid #f1f5f9;\n  border-radius: 16px;\n  padding: 32px;\n  transition: all 0.3s ease;\n}\n\n.tool-card:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.15);\n}\n\n.tool-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 24px;\n}\n\n.tool-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  color: white;\n  font-size: 20px;\n  flex-shrink: 0;\n}\n\n.tool-info h3 {\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n  color: #1f2937;\n}\n\n.tool-info p {\n  color: #6b7280;\n  line-height: 1.5;\n  margin: 0;\n}\n\n.tool-content {\n  margin-top: 16px;\n}\n\n.link-input {\n  width: 100%;\n}\n\n/* 二维码弹窗 */\n.qr-modal-content {\n  text-align: center;\n}\n\n.qr-code-container {\n  margin-bottom: 24px;\n  padding: 20px;\n  background: #f8fafc;\n  border-radius: 12px;\n  border: 2px dashed #d1d5db;\n}\n\n.qr-code-image {\n  max-width: 100%;\n  height: auto;\n  border-radius: 8px;\n}\n\n.qr-actions {\n  margin-top: 16px;\n}\n\n/* 提现弹窗 */\n.withdraw-modal-content {\n  padding: 8px 0;\n}\n\n.withdraw-info {\n  background: #f8fafc;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 24px;\n  border: 1px solid #e2e8f0;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.info-item:last-child {\n  margin-bottom: 0;\n}\n\n.info-label {\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n.info-value {\n  color: #1e293b;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n.withdraw-actions {\n  text-align: right;\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid #f1f5f9;\n}\n\n.card-action {\n  margin-top: 8px;\n}\n\n/* 推广链接区域 */\n.promotion-link-section {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  border: 2px solid #e2e8f0;\n}\n\n.link-main-container {\n  margin-bottom: 1rem;\n}\n\n.link-input-large {\n  margin-bottom: 1rem;\n}\n\n.link-input-large .ant-input {\n  font-size: 1rem;\n  padding: 12px 16px;\n  border-radius: 8px;\n  border: 2px solid #e2e8f0;\n}\n\n.link-actions {\n  display: flex;\n  gap: 12px;\n  flex-wrap: wrap;\n}\n\n.copy-btn {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  border: none;\n  font-weight: 600;\n}\n\n.qr-btn {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  border: none;\n  color: white;\n  font-weight: 600;\n}\n\n\n\n.link-tips {\n  background: #f0f9ff;\n  border: 1px solid #bae6fd;\n  border-radius: 8px;\n  padding: 12px 16px;\n  color: #0369a1;\n  font-size: 0.9rem;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.link-tips strong {\n  color: #1e40af;\n  font-weight: 700;\n}\n\n/* 分成规则表格 */\n.commission-rules {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n}\n\n.rules-table {\n  border: 1px solid #e2e8f0;\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.rule-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.rule-row:last-child {\n  border-bottom: none;\n}\n\n.rule-row.header {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  font-weight: 700;\n  color: #1e293b;\n}\n\n.rule-row.vip {\n  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\n}\n\n.rule-row.svip {\n  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);\n}\n\n.rule-cell {\n  padding: 16px;\n  text-align: center;\n  border-right: 1px solid #e2e8f0;\n}\n\n.rule-cell:last-child {\n  border-right: none;\n}\n\n.rule-cell.highlight {\n  font-weight: 700;\n  color: #dc2626;\n  font-size: 1.1rem;\n}\n\n/* 表格容器 */\n.users-table-container,\n.records-table-container {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n}\n\n.referral-users,\n.withdraw-records {\n  margin-bottom: 3rem;\n}\n\n.reward-amount,\n.withdraw-amount {\n  font-weight: 600;\n  color: #059669;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .page-title {\n    font-size: 2rem;\n  }\n\n  .page-subtitle {\n    font-size: 1rem;\n  }\n\n  .commission-badge {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .earnings-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .tools-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .earnings-dashboard,\n  .commission-progress,\n  .tools-section {\n    padding: 24px;\n  }\n\n  .tool-card {\n    padding: 24px;\n  }\n\n  .progress-card {\n    padding: 24px;\n  }\n\n  .level-info,\n  .next-info {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .tool-header {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .tool-icon {\n    margin: 0 auto 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .affiliate-section {\n    padding: 0 16px 60px;\n  }\n\n  .page-header {\n    padding: 40px 16px 24px;\n  }\n\n  .earning-card {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .card-icon {\n    margin: 0 auto 12px;\n  }\n\n  .link-actions {\n    flex-direction: column;\n  }\n\n  .rule-row {\n    grid-template-columns: 1fr;\n    text-align: left;\n  }\n\n  .rule-cell {\n    border-right: none;\n    border-bottom: 1px solid #e2e8f0;\n    text-align: left;\n  }\n\n  .rule-cell:last-child {\n    border-bottom: none;\n  }\n\n  .promotion-link-section,\n  .commission-rules,\n  .users-table-container,\n  .records-table-container {\n    padding: 1.5rem;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/affiliate"}]}