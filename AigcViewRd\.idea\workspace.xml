<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="306e7930-831d-4433-a8b7-b70d74b9abf2" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/demo/usercenter/controller/UserCenterDataController.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/demo/usercenter/controller/UserCenterDataController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianying/service/JianyingDataboxService.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianying/service/JianyingDataboxService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/dto/request/JianyingProDataConversionRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/dto/request/JianyingProDataConversionRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/system/service/impl/UserRegisterServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/system/service/impl/UserRegisterServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/resources/application-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/resources/application-prod.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/JeecgOneGUI.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/JeecgOneToMainUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/JeecgSystemApplication.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/init/CodeGenerateDbConfig.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/init/SystemInitListener.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/init/TomcatFactoryConfig$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/init/TomcatFactoryConfig.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/jimureport/JimuReportTokenService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/config/AigcApiConfig$FileStorage.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/config/AigcApiConfig$HtmlSecurity.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/config/AigcApiConfig.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/AigcApiController$ImageTransferResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/AigcApiController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/CozeVideoController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/PriceCalculationResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/SystemAPIController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/dto/PluginVerifyResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/entity/AicgApiLog.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/entity/AicgOnlineUsers.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/exception/CozeVideoExceptionHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/exception/VideoGenerationException$ErrorCodes.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/exception/VideoGenerationException.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/mapper/AicgApiLogMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/mapper/AicgOnlineUsersMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/service/DouBaoVideoApiService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/service/IAigcApiService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/service/VideoGenerationService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/service/impl/AigcApiServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/util/QRCodeUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/util/SecurityUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/util/VideoParameterValidator.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/controller/CasClientController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/CASServiceUtil$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/CASServiceUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/XmlUtils$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/XmlUtils$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/XmlUtils$CustomAttributeHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/XmlUtils.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/controller/AicgUserApiUsageController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/entity/AicgUserApiUsage.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/mapper/AicgUserApiUsageMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/service/IAicgUserApiUsageService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/service/impl/AicgUserApiUsageServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/controller/AicgExchangeCodeController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/entity/AicgExchangeCode.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/mapper/AicgExchangeCodeMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/service/IAicgExchangeCodeService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/service/impl/AicgExchangeCodeServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/controller/AigcHomeCarouselController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/entity/AigcHomeCarousel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/mapper/AigcHomeCarouselMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/service/IAigcHomeCarouselService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/service/impl/AigcHomeCarouselServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/controller/AicgUserMembershipHistoryController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/entity/AicgUserMembershipHistory.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/mapper/AicgUserMembershipHistoryMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/service/IAicgUserMembershipHistoryService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/service/impl/AicgUserMembershipHistoryServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/controller/AicgUserNotificationController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/entity/AicgUserNotification.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/mapper/AicgUserNotificationMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/service/IAicgUserNotificationService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/service/impl/AicgUserNotificationServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/controller/AigcPlubAuthorController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/entity/AigcPlubAuthor.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/mapper/AigcPlubAuthorMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/service/IAigcPlubAuthorService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/service/impl/AigcPlubAuthorServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/controller/AigcPlubShopController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/entity/AigcPlubShop.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/mapper/AigcPlubShopMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/service/IAigcPlubShopService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/service/impl/AigcPlubShopServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/controller/AicgUserReferralController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/entity/AicgUserReferral.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/mapper/AicgUserReferralMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/service/IAicgUserReferralService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/service/impl/AicgUserReferralServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/controller/AicgUserReferralRewardController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/entity/AicgUserReferralReward.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/mapper/AicgUserReferralRewardMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/service/IAicgUserReferralRewardService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/service/impl/AicgUserReferralRewardServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/usercenter/controller/UserCenterDataController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/controller/AicgUserProfileController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/entity/AicgUserProfile.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/mapper/AicgUserProfileMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/service/IAicgUserProfileService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/service/impl/AicgUserProfileServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/controller/AicgUserRecordController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/entity/AicgUserRecord.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/mapper/AicgUserRecordMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/service/IAicgUserRecordService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/service/impl/AicgUserRecordServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/controller/AigcVersionControlController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/dto/PublicVersionInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/entity/AigcVersionControl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/mapper/AigcVersionControlMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/service/IAigcVersionControlService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/service/impl/AigcVersionControlServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/controller/AigcVideoTeacherController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/entity/AigcVideoTeacher.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/mapper/AigcVideoTeacherMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/service/IAigcVideoTeacherService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/service/impl/AigcVideoTeacherServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/controller/AigcVideoTutorialController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/entity/AigcVideoTutorial.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/mapper/AigcVideoTutorialMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/service/IAigcVideoTutorialService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/service/impl/AigcVideoTutorialServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/controller/AigcWebsiteFeaturesController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/entity/AigcWebsiteFeatures.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/mapper/AigcWebsiteFeaturesMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/service/IAigcWebsiteFeaturesService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/service/impl/AigcWebsiteFeaturesServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/controller/AigcWebsiteStatsController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/entity/AigcWebsiteStats.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/mapper/AigcWebsiteStatsMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/service/IAigcWebsiteStatsService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/service/impl/AigcWebsiteStatsServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/aspect/JianyingAccessAspect.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/aspect/JianyingAccessKeyAspect.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/config/JianyingWebConfig.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/config/TosConfig$Internal.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/config/TosConfig.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/controller/JianyingDataboxController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/controller/JianyingToolboxController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddAudiosRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddCaptionsRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddEffectsRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddImagesRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddKeyframesRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddMasksRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddStickerRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddTextStyleRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddVideosRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AsrTimelinesRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioDownloadResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioInfosRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioProcessingLog.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioProcessingSummary.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioTimelinesRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/BaseJianyingRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/BgmSearchRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/CaptionInfosRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/CreateDraftRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/EasyCreateMaterialRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/EffectInfo$FileUrlInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/EffectInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/EffectInfosRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GenVideoRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GenVideoStatusRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GetAudioDurationRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GetImageAnimationsRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GetTextAnimationsRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GetUrlRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/ImgsInfosRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/KeyframesInfosRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/ObjsToStrListRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/SaveDraftRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/SearchStickerRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/SoundEffectsSearchRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/StrListToObjsRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/StrToListRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/TimelinesRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/VideoInfosRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/exception/JianyingExceptionHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/exception/JianyingParameterException.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/interceptor/JianyingApiInterceptor.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/ApiKeyVerificationService$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/ApiKeyVerificationService$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/ApiKeyVerificationService$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/ApiKeyVerificationService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$10.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$12.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$13.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$14.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$15.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$16.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$17.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$18.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$19$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$19.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$20.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$21.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$22.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$4.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$5.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$6.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$7.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$8.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$9$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$9.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/DraftConfigGenerator.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/DraftContentGenerator.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/DraftPackageService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$10.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$11.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$12$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$12$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$12$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$12.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$13.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$14.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$15.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$16.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$17.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$18.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$19.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$2$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$2$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$20.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$21.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$22.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$23.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$24$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$24.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$25.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$26.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$27.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$28.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$29.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$30.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$31$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$31$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$31$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$31.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$32.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$33.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$34.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$35.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$36.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$37.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$4.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$5.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$6$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$6$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$6.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$7.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$8.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$9$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$9$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$9.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$MaskInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$VideoMaterialResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$4.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$5.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingEffectSearchService$CacheEntry.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingEffectSearchService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingIdResolverService$AnimationInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingIdResolverService$TransitionCacheEntry.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingIdResolverService$TransitionInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingIdResolverService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingMaskSearchService$MaskInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingMaskSearchService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/TosService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/task/TosCleanupTask.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/validator/JianyingAccessValidator.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/config/JianyingProConfig.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/controller/JianyingProController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/BaseJianyingProRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddAudiosRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddCaptionsRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddEffectsRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddImagesRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddKeyframesRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddVideosRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProDataConversionRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProTimelinesRequest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/response/BaseJianyingProResponse.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/response/JianyingProAddAudiosResponse.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/response/JianyingProAddVideosResponse.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/enums/JianyingProErrorCode.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/exception/JianyingProBusinessException.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/exception/JianyingProGlobalExceptionHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/JianyingProService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$10.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$11.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$13.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$14.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$15.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$16.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$17.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$18.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$19.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$21.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$22.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$23.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$25.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$26.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$27.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$28.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$29.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$30.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$32.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$33.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$35.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$36.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$37.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$38.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$39.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$4.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$40.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$41.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$5.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$7.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$8.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$9.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$KeywordRange.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$MaskInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$10.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$11.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$12$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$12$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$12$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$12.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$13.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$14.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$15.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$16.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$17.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$18.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$19.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$20.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$21.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$22$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$22.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$23.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$24.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$25.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$27.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$28.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$29.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$30.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$31.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$32.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$33.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$34.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$35.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$36.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$37.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$4.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$5.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$7.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$8.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$MaskInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$VideoMaterialResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProCozeApiService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$4.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$5.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDraftContentGenerator.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProEffectSearchService$CacheEntry.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProEffectSearchService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProIdResolverService$AnimationInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProIdResolverService$TransitionCacheEntry.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProIdResolverService$TransitionInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProIdResolverService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProTosService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/util/JianyingProResponseUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/controller/SysMessageController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/controller/SysMessageTemplateController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/entity/MsgParams.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/entity/SysMessage.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/entity/SysMessageTemplate.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/ISendMsgHandle.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/enums/SendMsgStatusEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/enums/SendMsgTypeEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/impl/EmailSendMsgHandle.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/impl/SmsSendMsgHandle.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/impl/WxSendMsgHandle.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/job/SendMsgJob.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/mapper/SysMessageMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/mapper/SysMessageTemplateMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/service/ISysMessageService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/service/ISysMessageTemplateService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/service/impl/SysMessageServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/service/impl/SysMessageTemplateServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/util/PushMsgUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/websocket/SocketHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/websocket/TestSocketController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/websocket/WebSocket.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/controller/ActuatorRedisController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/domain/RedisInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/exception/RedisConnectException.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/service/RedisService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/service/impl/MailHealthIndicator.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/service/impl/RedisServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/ngalain/aop/LogRecordAspect.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/ngalain/controller/NgAlainController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/ngalain/service/NgAlainService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/ngalain/service/impl/NgAlainServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/controller/OSSFileController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/entity/OSSFile.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/mapper/OSSFileMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/service/IOSSFileService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/service/impl/OSSFileServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/controller/QuartzJobController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/entity/QuartzJob.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/job/AsyncJob.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/job/SampleJob.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/job/SampleParamJob.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/mapper/QuartzJobMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/service/IQuartzJobService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/service/impl/QuartzJobServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$InviteCode.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$Password.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$Security.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$VerifyCode$Captcha.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$VerifyCode$Email.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$VerifyCode$Sms.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$VerifyCode.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/SmsConfig.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/CacheManagementController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/CommonController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/DuplicateCheckController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/LoginController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysAnnouncementController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysAnnouncementSendController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysCategoryController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysCheckRuleController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDataLogController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDataSourceController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartController$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartController$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartPermissionController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartRoleController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDictController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDictItemController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysFillRuleController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysGatewayRouteController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysLogController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysPermissionController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysPositionController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysRoleController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysSensitiveWordController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysTenantController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysUploadController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysUserAgentController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysUserController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysUserOnlineController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/ThirdAppController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/ThirdLoginController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/UserCenterController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/UserRegisterController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/dto/RegisterDTO$WechatInfo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/dto/RegisterDTO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/AicgVerifyCode.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/AicgWechatTemp.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/AigcDesktopDownloadLog.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysAnnouncement.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysAnnouncementSend.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysCategory.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysCheckRule.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDataLog.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDataSource.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepart.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepartPermission.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepartRole.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepartRolePermission.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepartRoleUser.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDict.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDictItem.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysFillRule.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysGatewayRoute.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysLog.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysPermission.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysPermissionDataRule.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysPosition.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysRole.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysRolePermission.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysSensitiveWord.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysSensitiveWordHitLog.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysTenant.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysThirdAccount.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysUser.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysUserAgent.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysUserDepart.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysUserRole.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/VerifyCodeErrorType.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/VerifyCodeResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/AicgVerifyCodeMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/AicgWechatTempMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/AigcDesktopDownloadLogMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysAnnouncementMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysAnnouncementSendMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysCategoryMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysCheckRuleMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDataLogMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDataSourceMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartPermissionMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartRoleMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartRolePermissionMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartRoleUserMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDictItemMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDictMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysFillRuleMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysGatewayRouteMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysLogMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysPermissionDataRuleMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysPermissionMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysPositionMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysRoleMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysRolePermissionMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysSensitiveWordHitLogMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysSensitiveWordMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysTenantMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysThirdAccountMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysUserAgentMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysUserDepartMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysUserMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysUserRoleMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/AnnouncementSendModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/DepartIdModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/DuplicateCheckVo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysDepartTreeModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysDictTree.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysLoginModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysPermissionTree.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysUserSysDepartModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/ThirdLoginModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/TreeModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/TreeSelectModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/rule/CategoryCodeRule.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/rule/OrderNumberRule.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/rule/OrgCodeRule.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/IAicgVerifyCodeService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/IAigcDesktopDownloadLogService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISensitiveWordService$NicknameValidationResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISensitiveWordService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysAnnouncementSendService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysAnnouncementService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysCategoryService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysCheckRuleService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDataLogService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDataSourceService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartPermissionService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartRolePermissionService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartRoleService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartRoleUserService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDictItemService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDictService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysFillRuleService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysGatewayRouteService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysLogService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysPermissionDataRuleService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysPermissionService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysPositionService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysRolePermissionService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysRoleService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysSensitiveWordHitLogService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysSensitiveWordService$ImportResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysSensitiveWordService$SensitiveWordCheckResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysSensitiveWordService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysTenantService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysThirdAccountService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysUserAgentService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysUserDepartService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysUserRoleService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysUserService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/IThirdAppService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/IUserRegisterService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/UserCacheCleanupService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/AicgVerifyCodeServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/AigcDesktopDownloadLogServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/ImportFileServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SensitiveWordServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysAnnouncementSendServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysAnnouncementServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysBaseApiImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysCategoryServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysCheckRuleServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDataLogServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDataSourceServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartPermissionServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartRolePermissionServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartRoleServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartRoleUserServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDictItemServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDictServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysFillRuleServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysGatewayRouteServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysLogServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysPermissionDataRuleImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysPermissionServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysPositionServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysRolePermissionServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysRoleServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysSensitiveWordHitLogServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysSensitiveWordServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysTenantServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysThirdAccountServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysUserAgentServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysUserDepartServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysUserRoleServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysUserServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/ThirdAppDingtalkServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/ThirdAppWechatEnterpriseServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/UserRegisterServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/desform/SysTranslateAPIImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/AnnouncementManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/FindsDepartsChildrenUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/LoginConflictChecker.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/LogoutCacheVerifier$CacheVerificationResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/LogoutCacheVerifier.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/PermissionDataUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/RandImageUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/RoleChecker.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/SecurityUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/SingleLoginManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/XSSUtils.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/LoginConflictResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysDepartUsersVO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysDictPage.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysOnlineVO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysUserDepVo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysUserOnlineVO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysUserRoleVO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/thirdapp/JdtDepartmentTreeVo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/thirdapp/JwDepartmentTreeVo.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/thirdapp/SyncInfoVo.class" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="explicitlyEnabledProfiles" value="dev" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2ySsI0hrVVD7Z6hSzII5gP8jPBf" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.jeecg-boot-module-system [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-module-system [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-parent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-parent [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-parent [validate].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.JeecgSystemApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/AigcView/AigcViewRd&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;advanced.settings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="JeecgSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="jeecg-boot-module-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.jeecg.JeecgSystemApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="306e7930-831d-4433-a8b7-b70d74b9abf2" name="更改" comment="" />
      <created>1749839225426</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749839225426</updated>
      <workItem from="1749839226610" duration="2126000" />
      <workItem from="1749846796291" duration="37818000" />
      <workItem from="1750127497474" duration="664000" />
      <workItem from="1750147622680" duration="8219000" />
      <workItem from="1750182992775" duration="828000" />
      <workItem from="1750219207386" duration="34463000" />
      <workItem from="1750395165498" duration="16469000" />
      <workItem from="1750481146931" duration="18858000" />
      <workItem from="1750576054427" duration="13926000" />
      <workItem from="1750662956240" duration="24712000" />
      <workItem from="1750844199724" duration="10000" />
      <workItem from="1750924195138" duration="28172000" />
      <workItem from="1750964802742" duration="9819000" />
      <workItem from="1750999295568" duration="9206000" />
      <workItem from="1751010645542" duration="31817000" />
      <workItem from="1751184251064" duration="3114000" />
      <workItem from="1751308217715" duration="5256000" />
      <workItem from="1751366071895" duration="82680000" />
      <workItem from="1751786686767" duration="103917000" />
      <workItem from="1752333468590" duration="3861000" />
      <workItem from="1752422504660" duration="9302000" />
      <workItem from="1752506359093" duration="3126000" />
      <workItem from="1752633991731" duration="11995000" />
      <workItem from="1752716420676" duration="1695000" />
      <workItem from="1752752996018" duration="4905000" />
      <workItem from="1752833179798" duration="17286000" />
      <workItem from="1752909924462" duration="2263000" />
      <workItem from="1753102236494" duration="60366000" />
      <workItem from="1753502491755" duration="4485000" />
      <workItem from="1753638631113" duration="3262000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>