{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue", "mtime": 1753667921656}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport { getReferralStats, generateReferralLink } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport Vue from 'vue'\n\nexport default {\n  name: 'Affiliate',\n  components: {\n    WebsitePage\n  },\n  data() {\n    return {\n      loading: true,\n      qrLoading: false,\n\n      // 收益数据\n      totalEarnings: 0,\n      availableEarnings: 0,\n      totalReferrals: 0,\n      memberReferrals: 0,\n\n      // 推广链接\n      affiliateLink: '',\n\n      // 佣金等级\n      userRole: 'NORMAL', // NORMAL, VIP, SVIP\n      currentCommissionRate: 30,\n      commissionLevelText: '新手推广员',\n      levelProgress: 0,\n      nextLevelRequirement: 10,\n      nextLevelText: '高级推广员',\n      nextLevelRate: 40,\n      progressColor: '#1890ff',\n\n      // 二维码\n      showQRModal: false,\n      qrCodeUrl: '',\n      qrPreGenerated: false, // 是否已预生成二维码\n\n      // 提现\n      showWithdrawModal: false,\n      withdrawLoading: false,\n      withdrawForm: this.$form.createForm(this),\n\n      // 推广用户列表\n      referralUsers: [],\n      usersLoading: false,\n      userColumns: [\n        {\n          title: '头像',\n          dataIndex: 'avatar',\n          key: 'avatar',\n          scopedSlots: { customRender: 'avatar' },\n          width: 80\n        },\n        {\n          title: '用户昵称',\n          dataIndex: 'nickname',\n          key: 'nickname'\n        },\n        {\n          title: '注册时间',\n          dataIndex: 'registerTime',\n          key: 'registerTime'\n        },\n        {\n          title: '转化状态',\n          dataIndex: 'status',\n          key: 'status',\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '获得奖励',\n          dataIndex: 'reward',\n          key: 'reward',\n          scopedSlots: { customRender: 'reward' }\n        }\n      ],\n\n      // 提现记录\n      withdrawRecords: [],\n      recordsLoading: false,\n      withdrawColumns: [\n        {\n          title: '提现金额',\n          dataIndex: 'amount',\n          key: 'amount',\n          scopedSlots: { customRender: 'amount' }\n        },\n        {\n          title: '提现方式',\n          dataIndex: 'method',\n          key: 'method'\n        },\n        {\n          title: '申请时间',\n          dataIndex: 'applyTime',\n          key: 'applyTime'\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '完成时间',\n          dataIndex: 'completeTime',\n          key: 'completeTime'\n        }\n      ],\n\n      // 用户信息\n      userInfo: null\n    }\n  },\n  async mounted() {\n    await this.checkLoginAndLoadData()\n  },\n  methods: {\n    // 检查登录状态并加载数据\n    async checkLoginAndLoadData() {\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      if (!token) {\n        this.$router.push({ path: '/login', query: { redirect: this.$route.fullPath } })\n        return\n      }\n\n      try {\n        await Promise.all([\n          this.loadReferralData(),\n          this.loadReferralLink(),\n          this.loadUserRole(),\n          this.loadReferralUsers(),\n          this.loadWithdrawRecords()\n        ])\n\n        // 计算佣金等级\n        this.calculateCommissionLevel()\n\n        // 自动预生成二维码\n        this.preGenerateQRCode()\n      } catch (error) {\n        console.error('加载分销数据失败:', error)\n        this.$notification.error({\n          message: '加载失败',\n          description: '获取分销数据失败，请稍后重试',\n          placement: 'topRight'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载推荐统计数据\n    async loadReferralData() {\n      try {\n        const response = await getReferralStats()\n        if (response.success) {\n          const data = response.result\n          this.totalEarnings = data.total_reward_amount || 0\n          this.availableEarnings = data.available_rewards || 0\n          this.totalReferrals = data.total_referrals || 0\n          this.memberReferrals = data.member_referrals || 0\n\n\n        }\n      } catch (error) {\n        console.error('获取推荐统计失败:', error)\n        throw error\n      }\n    },\n\n    // 加载推荐链接\n    async loadReferralLink() {\n      try {\n        const response = await generateReferralLink({})\n        if (response.success) {\n          this.affiliateLink = response.result || ''\n        }\n      } catch (error) {\n        console.error('获取推荐链接失败:', error)\n        // 如果获取失败，使用默认链接格式\n        this.affiliateLink = `${window.location.origin}?ref=loading...`\n      }\n    },\n\n    // 加载用户角色信息\n    async loadUserRole() {\n      try {\n        // TODO: 从用户信息API获取用户角色\n        // 暂时使用默认值\n        this.userRole = 'NORMAL'\n      } catch (error) {\n        console.error('获取用户角色失败:', error)\n        this.userRole = 'NORMAL'\n      }\n    },\n\n    // 计算佣金等级和进度\n    calculateCommissionLevel() {\n      const memberCount = this.memberReferrals\n\n      if (this.userRole === 'SVIP') {\n        this.currentCommissionRate = 50\n        this.commissionLevelText = 'SVIP推广员'\n        this.levelProgress = 100\n        this.nextLevelRequirement = 0\n        this.nextLevelText = '已达最高等级'\n        this.nextLevelRate = 50\n        this.progressColor = '#722ed1'\n      } else if (this.userRole === 'VIP') {\n        if (memberCount >= 30) {\n          this.currentCommissionRate = 50\n          this.commissionLevelText = 'VIP顶级推广员'\n          this.levelProgress = 100\n          this.nextLevelRequirement = 0\n          this.nextLevelText = '已达最高等级'\n          this.nextLevelRate = 50\n          this.progressColor = '#722ed1'\n        } else if (memberCount >= 10) {\n          this.currentCommissionRate = 45\n          this.commissionLevelText = 'VIP高级推广员'\n          this.levelProgress = (memberCount / 30) * 100\n          this.nextLevelRequirement = 30\n          this.nextLevelText = 'VIP顶级推广员'\n          this.nextLevelRate = 50\n          this.progressColor = '#13c2c2'\n        } else {\n          this.currentCommissionRate = 35\n          this.commissionLevelText = 'VIP推广员'\n          this.levelProgress = (memberCount / 10) * 100\n          this.nextLevelRequirement = 10\n          this.nextLevelText = 'VIP高级推广员'\n          this.nextLevelRate = 45\n          this.progressColor = '#1890ff'\n        }\n      } else {\n        // NORMAL用户\n        if (memberCount >= 30) {\n          this.currentCommissionRate = 50\n          this.commissionLevelText = '顶级推广员'\n          this.levelProgress = 100\n          this.nextLevelRequirement = 0\n          this.nextLevelText = '已达最高等级'\n          this.nextLevelRate = 50\n          this.progressColor = '#722ed1'\n        } else if (memberCount >= 10) {\n          this.currentCommissionRate = 40\n          this.commissionLevelText = '高级推广员'\n          this.levelProgress = (memberCount / 30) * 100\n          this.nextLevelRequirement = 30\n          this.nextLevelText = '顶级推广员'\n          this.nextLevelRate = 50\n          this.progressColor = '#13c2c2'\n        } else {\n          this.currentCommissionRate = 30\n          this.commissionLevelText = '新手推广员'\n          this.levelProgress = (memberCount / 10) * 100\n          this.nextLevelRequirement = 10\n          this.nextLevelText = '高级推广员'\n          this.nextLevelRate = 40\n          this.progressColor = '#1890ff'\n        }\n      }\n    },\n\n     // 复制推广链接\n     copyLink() {\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '推广链接未生成',\n          description: '推广链接正在生成中，请稍后再试',\n          placement: 'topRight'\n        })\n        return\n      }\n      \n      navigator.clipboard.writeText(this.affiliateLink).then(() => {\n        this.$notification.success({\n          message: '推广链接已复制',\n          description: '推广链接已成功复制到剪贴板，快去分享给好友吧！',\n          placement: 'topRight',\n          duration: 3,\n          style: {\n            width: '380px',\n            marginTop: '101px',\n            borderRadius: '8px',\n            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n          }\n        })\n      }).catch(() => {\n        this.$notification.error({\n          message: '复制失败',\n          description: '复制推广链接失败，请手动复制',\n          placement: 'topRight'\n        })\n      })\n    },\n\n    // 预生成二维码（后台静默生成）\n    async preGenerateQRCode() {\n      if (!this.affiliateLink || this.qrPreGenerated) {\n        return\n      }\n\n      try {\n        console.log('开始预生成二维码...')\n\n        // 调用后端API生成二维码并上传到TOS\n        const response = await this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n          params: {\n            url: this.affiliateLink\n          }\n        })\n\n        if (response && response.success) {\n          // 静默保存二维码URL\n          this.qrCodeUrl = response.result\n          this.qrPreGenerated = true\n          console.log('二维码预生成成功:', this.qrCodeUrl)\n        }\n      } catch (error) {\n        console.error('预生成二维码失败:', error)\n        // 预生成失败不显示错误提示，用户点击时再重试\n      }\n    },\n\n    // 生成推广二维码（用户主动点击）\n    async generateQRCode() {\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '推广链接未生成',\n          description: '请等待推广链接生成完成后再生成二维码',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      // 如果已经预生成，直接显示\n      if (this.qrPreGenerated && this.qrCodeUrl) {\n        this.showQRModal = true\n        return\n      }\n\n      try {\n        this.qrLoading = true\n\n        // 调用后端API生成二维码并上传到TOS\n        const response = await this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n          params: {\n            url: this.affiliateLink\n          }\n        })\n\n        console.log('二维码生成响应:', response)\n\n        if (response && response.success) {\n          // 使用CDN地址\n          this.qrCodeUrl = response.result\n          this.qrPreGenerated = true\n          this.showQRModal = true\n\n          this.$notification.success({\n            message: '二维码生成成功',\n            description: '推广二维码已生成并存储到CDN，可以下载保存',\n            placement: 'topRight'\n          })\n        } else {\n          const errorMsg = (response && response.message) || '生成失败'\n          throw new Error(errorMsg)\n        }\n      } catch (error) {\n        console.error('生成二维码失败:', error)\n        this.$notification.error({\n          message: '生成失败',\n          description: error.message || '二维码生成失败，请稍后重试',\n          placement: 'topRight'\n        })\n      } finally {\n        this.qrLoading = false\n      }\n    },\n\n    // 下载二维码\n    downloadQRCode() {\n      if (!this.qrCodeUrl) return\n\n      try {\n        // 通过后端代理下载，避免CORS问题\n        const downloadUrl = `/api/usercenter/downloadReferralQRCode?url=${encodeURIComponent(this.qrCodeUrl)}&t=${Date.now()}`\n\n        // 直接打开下载链接\n        window.open(downloadUrl, '_blank')\n\n        this.$notification.success({\n          message: '下载开始',\n          description: '二维码正在下载到本地',\n          placement: 'topRight'\n        })\n      } catch (error) {\n        console.error('下载二维码失败:', error)\n        this.$notification.error({\n          message: '下载失败',\n          description: '二维码下载失败，请稍后重试',\n          placement: 'topRight'\n        })\n      }\n    },\n\n    // 显示提现弹窗\n    openWithdrawModal() {\n      if (this.availableEarnings < 100) {\n        this.$notification.warning({\n          message: '提现金额不足',\n          description: '最低提现金额为100元，请继续推广获得更多收益',\n          placement: 'topRight'\n        })\n        return\n      }\n      this.showWithdrawModal = true\n    },\n\n    // 处理提现申请\n    handleWithdraw() {\n      this.withdrawForm.validateFields((err, values) => {\n        if (err) return\n\n        this.withdrawLoading = true\n\n        // TODO: 调用提现API\n        setTimeout(() => {\n          this.withdrawLoading = false\n          this.showWithdrawModal = false\n          this.withdrawForm.resetFields()\n\n          this.$notification.success({\n            message: '提现申请成功',\n            description: '您的提现申请已提交，预计1-3个工作日到账',\n            placement: 'topRight'\n          })\n        }, 2000)\n      })\n    },\n\n\n\n    // 加载推广用户列表\n    async loadReferralUsers() {\n      try {\n        this.usersLoading = true\n\n        const params = {\n          current: 1,\n          size: 10\n        }\n\n        const response = await this.$http.get('/api/usercenter/referralList', { params })\n\n        if (response.data.success) {\n          const records = (response.data.result && response.data.result.records) || []\n          // 转换数据格式\n          this.referralUsers = records.map((item, index) => ({\n            key: item.id || index,\n            nickname: item.referee_nickname || `用户***${index + 1}`,\n            avatar: item.referee_avatar || '',\n            registerTime: item.register_time || '',\n            status: item.has_membership ? '已转化' : '已注册',\n            reward: item.total_reward || '0.00'\n          }))\n        } else {\n          this.referralUsers = []\n        }\n      } catch (error) {\n        console.error('获取推广用户列表失败:', error)\n        this.referralUsers = []\n      } finally {\n        this.usersLoading = false\n      }\n    },\n\n    // 加载提现记录\n    async loadWithdrawRecords() {\n      try {\n        this.recordsLoading = true\n\n        const params = {\n          current: 1,\n          size: 10\n        }\n\n        const response = await this.$http.get('/api/usercenter/withdrawalHistory', { params })\n\n        if (response.data.success) {\n          const records = (response.data.result && response.data.result.records) || []\n          // 转换数据格式\n          this.withdrawRecords = records.map((item, index) => ({\n            key: item.id || index,\n            amount: item.amount || '0.00',\n            method: item.withdrawalMethod || '支付宝',\n            applyTime: item.applyTime || '',\n            status: this.getWithdrawStatusText(item.status),\n            completeTime: item.completeTime || '-'\n          }))\n        } else {\n          this.withdrawRecords = []\n        }\n      } catch (error) {\n        console.error('获取提现记录失败:', error)\n        this.withdrawRecords = []\n      } finally {\n        this.recordsLoading = false\n      }\n    },\n\n    // 获取提现状态文本\n    getWithdrawStatusText(status) {\n      const statusMap = {\n        0: '待审核',\n        1: '处理中',\n        2: '已完成',\n        3: '已拒绝'\n      }\n      return statusMap[status] || '未知状态'\n    },\n\n    // 获取状态颜色\n    getStatusColor(status) {\n      const colorMap = {\n        '已完成': 'green',\n        '处理中': 'blue',\n        '已拒绝': 'red',\n        '待审核': 'orange'\n      }\n      return colorMap[status] || 'default'\n    },\n\n     // 格式化数字显示\n     formatNumber(num) {\n       if (num === null || num === undefined) return '0'\n       const number = parseFloat(num)\n       if (isNaN(number)) return '0'\n       \n       // 如果是金额，保留两位小数\n       if (num === this.totalEarnings) {\n         return number.toLocaleString('zh-CN', {\n           minimumFractionDigits: 2,\n           maximumFractionDigits: 2\n         })\n       }\n       \n       // 其他数字不保留小数\n       return number.toLocaleString('zh-CN')\n     }\n   }\n }\n", {"version": 3, "sources": ["Affiliate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4WA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Affiliate.vue", "sourceRoot": "src/views/website/affiliate", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"affiliate-container\">\n      <!-- 简洁页面标题 -->\n      <div class=\"simple-header\">\n        <h1 class=\"simple-title\">分销推广</h1>\n        <p class=\"simple-subtitle\">加入分销计划，推广智界AIGC获得丰厚佣金</p>\n        <div class=\"commission-badge\">\n          <span class=\"badge-text\">当前佣金率：{{ currentCommissionRate }}%</span>\n          <span class=\"badge-level\">{{ commissionLevelText }}</span>\n        </div>\n      </div>\n\n      <!-- 分销内容区域 -->\n      <section class=\"affiliate-section\">\n        <div class=\"container\">\n          <!-- 推广链接区域 - 最显眼位置 -->\n          <div class=\"promotion-link-section\">\n            <h2 class=\"section-title\">您的专属推广链接</h2>\n            <div class=\"link-main-container\">\n              <div class=\"link-input-large\">\n                <a-input\n                  :value=\"affiliateLink || '正在生成推广链接...'\"\n                  readonly\n                  :loading=\"loading\"\n                  size=\"large\"\n                  placeholder=\"推广链接生成中...\"\n                />\n              </div>\n              <div class=\"link-actions\">\n                <a-button\n                  type=\"primary\"\n                  size=\"large\"\n                  :disabled=\"!affiliateLink || loading\"\n                  @click=\"copyLink\"\n                  class=\"copy-btn\"\n                >\n                  <a-icon type=\"copy\" />\n                  复制链接\n                </a-button>\n                <a-button\n                  size=\"large\"\n                  :loading=\"qrLoading\"\n                  @click=\"generateQRCode\"\n                  class=\"qr-btn\"\n                >\n                  <a-icon type=\"qrcode\" />\n                  推广二维码\n                </a-button>\n              </div>\n            </div>\n            <div class=\"link-tips\">\n              <a-icon type=\"info-circle\" />\n              分享此链接，好友注册并订阅会员后，您将获得 <strong>{{ currentCommissionRate }}%</strong> 的佣金奖励\n            </div>\n          </div>\n\n          <!-- 收益展示 -->\n          <div class=\"earnings-dashboard\">\n            <h2 class=\"section-title\">收益概览</h2>\n            <div class=\"earnings-grid\">\n              <div class=\"earning-card primary\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"dollar\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">¥{{ formatNumber(totalEarnings) }}</div>\n                    <div class=\"earning-label\">累计收益</div>\n                  </a-spin>\n                </div>\n              </div>\n\n              <div class=\"earning-card success\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"wallet\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">¥{{ formatNumber(availableEarnings) }}</div>\n                    <div class=\"earning-label\">可提现金额</div>\n                  </a-spin>\n                  <div class=\"card-action\">\n                    <a-button\n                      type=\"primary\"\n                      size=\"small\"\n                      :disabled=\"availableEarnings <= 0 || loading\"\n                      @click=\"openWithdrawModal\"\n                    >\n                      立即提现\n                    </a-button>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"earning-card info\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"team\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">{{ formatNumber(totalReferrals) }}</div>\n                    <div class=\"earning-label\">推荐注册</div>\n                  </a-spin>\n                </div>\n              </div>\n\n              <div class=\"earning-card warning\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"crown\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">{{ formatNumber(memberReferrals) }}</div>\n                    <div class=\"earning-label\">转化人数</div>\n                  </a-spin>\n                </div>\n              </div>\n\n\n            </div>\n          </div>\n\n          <!-- 佣金等级进度 -->\n          <div class=\"commission-progress\">\n            <h2 class=\"section-title\">佣金等级进度</h2>\n            <div class=\"progress-card\">\n              <div class=\"current-level\">\n                <div class=\"level-info\">\n                  <span class=\"level-name\">{{ commissionLevelText }}</span>\n                  <span class=\"level-rate\">{{ currentCommissionRate }}%佣金</span>\n                </div>\n                <div class=\"level-progress\">\n                  <a-progress\n                    :percent=\"levelProgress\"\n                    :stroke-color=\"progressColor\"\n                    :show-info=\"false\"\n                  />\n                  <div class=\"progress-text\">\n                    {{ memberReferrals }}/{{ nextLevelRequirement }} 转化用户\n                  </div>\n                </div>\n              </div>\n              <div class=\"next-level\" v-if=\"nextLevelRequirement > 0\">\n                <div class=\"next-info\">\n                  <span class=\"next-text\">下一等级：{{ nextLevelText }}</span>\n                  <span class=\"next-rate\">{{ nextLevelRate }}%佣金</span>\n                </div>\n                <div class=\"remaining\">\n                  还需 {{ nextLevelRequirement - memberReferrals }} 个转化用户\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 分成规则说明 -->\n          <div class=\"commission-rules\">\n            <h2 class=\"section-title\">分成规则</h2>\n            <div class=\"rules-table\">\n              <div class=\"rule-row header\">\n                <div class=\"rule-cell\">用户等级</div>\n                <div class=\"rule-cell\">推广人数要求</div>\n                <div class=\"rule-cell\">佣金比例</div>\n                <div class=\"rule-cell\">说明</div>\n              </div>\n              <div class=\"rule-row\">\n                <div class=\"rule-cell\">普通用户</div>\n                <div class=\"rule-cell\">0-9人</div>\n                <div class=\"rule-cell highlight\">30%</div>\n                <div class=\"rule-cell\">新手推广员</div>\n              </div>\n              <div class=\"rule-row\">\n                <div class=\"rule-cell\">普通用户</div>\n                <div class=\"rule-cell\">10-29人</div>\n                <div class=\"rule-cell highlight\">40%</div>\n                <div class=\"rule-cell\">高级推广员</div>\n              </div>\n              <div class=\"rule-row\">\n                <div class=\"rule-cell\">普通用户</div>\n                <div class=\"rule-cell\">30人以上</div>\n                <div class=\"rule-cell highlight\">50%</div>\n                <div class=\"rule-cell\">顶级推广员</div>\n              </div>\n              <div class=\"rule-row vip\">\n                <div class=\"rule-cell\">VIP用户</div>\n                <div class=\"rule-cell\">基础+5%</div>\n                <div class=\"rule-cell highlight\">35%-50%</div>\n                <div class=\"rule-cell\">VIP推广员</div>\n              </div>\n              <div class=\"rule-row svip\">\n                <div class=\"rule-cell\">SVIP用户</div>\n                <div class=\"rule-cell\">无要求</div>\n                <div class=\"rule-cell highlight\">50%</div>\n                <div class=\"rule-cell\">SVIP推广员</div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 推广用户列表 -->\n          <div class=\"referral-users\">\n            <h2 class=\"section-title\">我的推广用户</h2>\n            <div class=\"users-table-container\">\n              <a-table\n                :columns=\"userColumns\"\n                :data-source=\"referralUsers\"\n                :loading=\"usersLoading\"\n                :pagination=\"{ pageSize: 10, showSizeChanger: false }\"\n                size=\"middle\"\n              >\n                <template slot=\"avatar\" slot-scope=\"text, record\">\n                  <a-avatar :src=\"record.avatar\" :style=\"{ backgroundColor: '#87d068' }\">\n                    {{ record.nickname ? record.nickname.charAt(0) : 'U' }}\n                  </a-avatar>\n                </template>\n                <template slot=\"status\" slot-scope=\"text\">\n                  <a-tag :color=\"text === '已转化' ? 'green' : 'blue'\">\n                    {{ text }}\n                  </a-tag>\n                </template>\n                <template slot=\"reward\" slot-scope=\"text\">\n                  <span class=\"reward-amount\">¥{{ text || '0.00' }}</span>\n                </template>\n              </a-table>\n            </div>\n          </div>\n\n          <!-- 提现记录 -->\n          <div class=\"withdraw-records\">\n            <h2 class=\"section-title\">提现记录</h2>\n            <div class=\"records-table-container\">\n              <a-table\n                :columns=\"withdrawColumns\"\n                :data-source=\"withdrawRecords\"\n                :loading=\"recordsLoading\"\n                :pagination=\"{ pageSize: 10, showSizeChanger: false }\"\n                size=\"middle\"\n              >\n                <template slot=\"status\" slot-scope=\"text\">\n                  <a-tag :color=\"getStatusColor(text)\">\n                    {{ text }}\n                  </a-tag>\n                </template>\n                <template slot=\"amount\" slot-scope=\"text\">\n                  <span class=\"withdraw-amount\">¥{{ text }}</span>\n                </template>\n              </a-table>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 二维码弹窗 -->\n      <a-modal\n        v-model=\"showQRModal\"\n        title=\"推广二维码\"\n        :footer=\"null\"\n        width=\"400px\"\n        centered\n      >\n        <div class=\"qr-modal-content\">\n          <div class=\"qr-code-container\" v-if=\"qrCodeUrl\">\n            <img :src=\"qrCodeUrl\" alt=\"推广二维码\" class=\"qr-code-image\" />\n          </div>\n          <div class=\"qr-actions\">\n            <a-button type=\"primary\" block @click=\"downloadQRCode\" v-if=\"qrCodeUrl\">\n              <a-icon type=\"download\" />\n              下载二维码\n            </a-button>\n          </div>\n        </div>\n      </a-modal>\n\n      <!-- 提现弹窗 -->\n      <a-modal\n        v-model=\"showWithdrawModal\"\n        title=\"申请提现\"\n        :footer=\"null\"\n        width=\"500px\"\n        centered\n      >\n        <div class=\"withdraw-modal-content\">\n          <div class=\"withdraw-info\">\n            <div class=\"info-item\">\n              <span class=\"info-label\">可提现金额：</span>\n              <span class=\"info-value\">¥{{ formatNumber(availableEarnings) }}</span>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"info-label\">最低提现金额：</span>\n              <span class=\"info-value\">¥100.00</span>\n            </div>\n          </div>\n\n          <a-form :form=\"withdrawForm\" @submit=\"handleWithdraw\">\n            <a-form-item label=\"提现金额\">\n              <a-input-number\n                v-decorator=\"['amount', {\n                  rules: [\n                    { required: true, message: '请输入提现金额' },\n                    { type: 'number', min: 100, message: '最低提现金额为100元' },\n                    { type: 'number', max: availableEarnings, message: '提现金额不能超过可提现金额' }\n                  ]\n                }]\"\n                :min=\"100\"\n                :max=\"availableEarnings\"\n                :precision=\"2\"\n                style=\"width: 100%\"\n                placeholder=\"请输入提现金额\"\n              >\n                <template slot=\"addonAfter\">元</template>\n              </a-input-number>\n            </a-form-item>\n\n            <a-form-item label=\"提现方式\">\n              <a-select\n                v-decorator=\"['method', {\n                  rules: [{ required: true, message: '请选择提现方式' }],\n                  initialValue: 'alipay'\n                }]\"\n                placeholder=\"请选择提现方式\"\n              >\n                <a-select-option value=\"alipay\">支付宝</a-select-option>\n                <a-select-option value=\"wechat\">微信</a-select-option>\n                <a-select-option value=\"bank\">银行卡</a-select-option>\n              </a-select>\n            </a-form-item>\n\n            <a-form-item label=\"收款账号\">\n              <a-input\n                v-decorator=\"['account', {\n                  rules: [{ required: true, message: '请输入收款账号' }]\n                }]\"\n                placeholder=\"请输入收款账号\"\n              />\n            </a-form-item>\n\n            <a-form-item label=\"收款人姓名\">\n              <a-input\n                v-decorator=\"['name', {\n                  rules: [{ required: true, message: '请输入收款人姓名' }]\n                }]\"\n                placeholder=\"请输入收款人姓名\"\n              />\n            </a-form-item>\n          </a-form>\n\n          <div class=\"withdraw-actions\">\n            <a-button @click=\"showWithdrawModal = false\" style=\"margin-right: 8px\">\n              取消\n            </a-button>\n            <a-button\n              type=\"primary\"\n              :loading=\"withdrawLoading\"\n              @click=\"handleWithdraw\"\n            >\n              申请提现\n            </a-button>\n          </div>\n        </div>\n      </a-modal>\n    </div>\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport { getReferralStats, generateReferralLink } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport Vue from 'vue'\n\nexport default {\n  name: 'Affiliate',\n  components: {\n    WebsitePage\n  },\n  data() {\n    return {\n      loading: true,\n      qrLoading: false,\n\n      // 收益数据\n      totalEarnings: 0,\n      availableEarnings: 0,\n      totalReferrals: 0,\n      memberReferrals: 0,\n\n      // 推广链接\n      affiliateLink: '',\n\n      // 佣金等级\n      userRole: 'NORMAL', // NORMAL, VIP, SVIP\n      currentCommissionRate: 30,\n      commissionLevelText: '新手推广员',\n      levelProgress: 0,\n      nextLevelRequirement: 10,\n      nextLevelText: '高级推广员',\n      nextLevelRate: 40,\n      progressColor: '#1890ff',\n\n      // 二维码\n      showQRModal: false,\n      qrCodeUrl: '',\n      qrPreGenerated: false, // 是否已预生成二维码\n\n      // 提现\n      showWithdrawModal: false,\n      withdrawLoading: false,\n      withdrawForm: this.$form.createForm(this),\n\n      // 推广用户列表\n      referralUsers: [],\n      usersLoading: false,\n      userColumns: [\n        {\n          title: '头像',\n          dataIndex: 'avatar',\n          key: 'avatar',\n          scopedSlots: { customRender: 'avatar' },\n          width: 80\n        },\n        {\n          title: '用户昵称',\n          dataIndex: 'nickname',\n          key: 'nickname'\n        },\n        {\n          title: '注册时间',\n          dataIndex: 'registerTime',\n          key: 'registerTime'\n        },\n        {\n          title: '转化状态',\n          dataIndex: 'status',\n          key: 'status',\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '获得奖励',\n          dataIndex: 'reward',\n          key: 'reward',\n          scopedSlots: { customRender: 'reward' }\n        }\n      ],\n\n      // 提现记录\n      withdrawRecords: [],\n      recordsLoading: false,\n      withdrawColumns: [\n        {\n          title: '提现金额',\n          dataIndex: 'amount',\n          key: 'amount',\n          scopedSlots: { customRender: 'amount' }\n        },\n        {\n          title: '提现方式',\n          dataIndex: 'method',\n          key: 'method'\n        },\n        {\n          title: '申请时间',\n          dataIndex: 'applyTime',\n          key: 'applyTime'\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '完成时间',\n          dataIndex: 'completeTime',\n          key: 'completeTime'\n        }\n      ],\n\n      // 用户信息\n      userInfo: null\n    }\n  },\n  async mounted() {\n    await this.checkLoginAndLoadData()\n  },\n  methods: {\n    // 检查登录状态并加载数据\n    async checkLoginAndLoadData() {\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      if (!token) {\n        this.$router.push({ path: '/login', query: { redirect: this.$route.fullPath } })\n        return\n      }\n\n      try {\n        await Promise.all([\n          this.loadReferralData(),\n          this.loadReferralLink(),\n          this.loadUserRole(),\n          this.loadReferralUsers(),\n          this.loadWithdrawRecords()\n        ])\n\n        // 计算佣金等级\n        this.calculateCommissionLevel()\n\n        // 自动预生成二维码\n        this.preGenerateQRCode()\n      } catch (error) {\n        console.error('加载分销数据失败:', error)\n        this.$notification.error({\n          message: '加载失败',\n          description: '获取分销数据失败，请稍后重试',\n          placement: 'topRight'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载推荐统计数据\n    async loadReferralData() {\n      try {\n        const response = await getReferralStats()\n        if (response.success) {\n          const data = response.result\n          this.totalEarnings = data.total_reward_amount || 0\n          this.availableEarnings = data.available_rewards || 0\n          this.totalReferrals = data.total_referrals || 0\n          this.memberReferrals = data.member_referrals || 0\n\n\n        }\n      } catch (error) {\n        console.error('获取推荐统计失败:', error)\n        throw error\n      }\n    },\n\n    // 加载推荐链接\n    async loadReferralLink() {\n      try {\n        const response = await generateReferralLink({})\n        if (response.success) {\n          this.affiliateLink = response.result || ''\n        }\n      } catch (error) {\n        console.error('获取推荐链接失败:', error)\n        // 如果获取失败，使用默认链接格式\n        this.affiliateLink = `${window.location.origin}?ref=loading...`\n      }\n    },\n\n    // 加载用户角色信息\n    async loadUserRole() {\n      try {\n        // TODO: 从用户信息API获取用户角色\n        // 暂时使用默认值\n        this.userRole = 'NORMAL'\n      } catch (error) {\n        console.error('获取用户角色失败:', error)\n        this.userRole = 'NORMAL'\n      }\n    },\n\n    // 计算佣金等级和进度\n    calculateCommissionLevel() {\n      const memberCount = this.memberReferrals\n\n      if (this.userRole === 'SVIP') {\n        this.currentCommissionRate = 50\n        this.commissionLevelText = 'SVIP推广员'\n        this.levelProgress = 100\n        this.nextLevelRequirement = 0\n        this.nextLevelText = '已达最高等级'\n        this.nextLevelRate = 50\n        this.progressColor = '#722ed1'\n      } else if (this.userRole === 'VIP') {\n        if (memberCount >= 30) {\n          this.currentCommissionRate = 50\n          this.commissionLevelText = 'VIP顶级推广员'\n          this.levelProgress = 100\n          this.nextLevelRequirement = 0\n          this.nextLevelText = '已达最高等级'\n          this.nextLevelRate = 50\n          this.progressColor = '#722ed1'\n        } else if (memberCount >= 10) {\n          this.currentCommissionRate = 45\n          this.commissionLevelText = 'VIP高级推广员'\n          this.levelProgress = (memberCount / 30) * 100\n          this.nextLevelRequirement = 30\n          this.nextLevelText = 'VIP顶级推广员'\n          this.nextLevelRate = 50\n          this.progressColor = '#13c2c2'\n        } else {\n          this.currentCommissionRate = 35\n          this.commissionLevelText = 'VIP推广员'\n          this.levelProgress = (memberCount / 10) * 100\n          this.nextLevelRequirement = 10\n          this.nextLevelText = 'VIP高级推广员'\n          this.nextLevelRate = 45\n          this.progressColor = '#1890ff'\n        }\n      } else {\n        // NORMAL用户\n        if (memberCount >= 30) {\n          this.currentCommissionRate = 50\n          this.commissionLevelText = '顶级推广员'\n          this.levelProgress = 100\n          this.nextLevelRequirement = 0\n          this.nextLevelText = '已达最高等级'\n          this.nextLevelRate = 50\n          this.progressColor = '#722ed1'\n        } else if (memberCount >= 10) {\n          this.currentCommissionRate = 40\n          this.commissionLevelText = '高级推广员'\n          this.levelProgress = (memberCount / 30) * 100\n          this.nextLevelRequirement = 30\n          this.nextLevelText = '顶级推广员'\n          this.nextLevelRate = 50\n          this.progressColor = '#13c2c2'\n        } else {\n          this.currentCommissionRate = 30\n          this.commissionLevelText = '新手推广员'\n          this.levelProgress = (memberCount / 10) * 100\n          this.nextLevelRequirement = 10\n          this.nextLevelText = '高级推广员'\n          this.nextLevelRate = 40\n          this.progressColor = '#1890ff'\n        }\n      }\n    },\n\n     // 复制推广链接\n     copyLink() {\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '推广链接未生成',\n          description: '推广链接正在生成中，请稍后再试',\n          placement: 'topRight'\n        })\n        return\n      }\n      \n      navigator.clipboard.writeText(this.affiliateLink).then(() => {\n        this.$notification.success({\n          message: '推广链接已复制',\n          description: '推广链接已成功复制到剪贴板，快去分享给好友吧！',\n          placement: 'topRight',\n          duration: 3,\n          style: {\n            width: '380px',\n            marginTop: '101px',\n            borderRadius: '8px',\n            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n          }\n        })\n      }).catch(() => {\n        this.$notification.error({\n          message: '复制失败',\n          description: '复制推广链接失败，请手动复制',\n          placement: 'topRight'\n        })\n      })\n    },\n\n    // 预生成二维码（后台静默生成）\n    async preGenerateQRCode() {\n      if (!this.affiliateLink || this.qrPreGenerated) {\n        return\n      }\n\n      try {\n        console.log('开始预生成二维码...')\n\n        // 调用后端API生成二维码并上传到TOS\n        const response = await this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n          params: {\n            url: this.affiliateLink\n          }\n        })\n\n        if (response && response.success) {\n          // 静默保存二维码URL\n          this.qrCodeUrl = response.result\n          this.qrPreGenerated = true\n          console.log('二维码预生成成功:', this.qrCodeUrl)\n        }\n      } catch (error) {\n        console.error('预生成二维码失败:', error)\n        // 预生成失败不显示错误提示，用户点击时再重试\n      }\n    },\n\n    // 生成推广二维码（用户主动点击）\n    async generateQRCode() {\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '推广链接未生成',\n          description: '请等待推广链接生成完成后再生成二维码',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      // 如果已经预生成，直接显示\n      if (this.qrPreGenerated && this.qrCodeUrl) {\n        this.showQRModal = true\n        return\n      }\n\n      try {\n        this.qrLoading = true\n\n        // 调用后端API生成二维码并上传到TOS\n        const response = await this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n          params: {\n            url: this.affiliateLink\n          }\n        })\n\n        console.log('二维码生成响应:', response)\n\n        if (response && response.success) {\n          // 使用CDN地址\n          this.qrCodeUrl = response.result\n          this.qrPreGenerated = true\n          this.showQRModal = true\n\n          this.$notification.success({\n            message: '二维码生成成功',\n            description: '推广二维码已生成并存储到CDN，可以下载保存',\n            placement: 'topRight'\n          })\n        } else {\n          const errorMsg = (response && response.message) || '生成失败'\n          throw new Error(errorMsg)\n        }\n      } catch (error) {\n        console.error('生成二维码失败:', error)\n        this.$notification.error({\n          message: '生成失败',\n          description: error.message || '二维码生成失败，请稍后重试',\n          placement: 'topRight'\n        })\n      } finally {\n        this.qrLoading = false\n      }\n    },\n\n    // 下载二维码\n    downloadQRCode() {\n      if (!this.qrCodeUrl) return\n\n      try {\n        // 通过后端代理下载，避免CORS问题\n        const downloadUrl = `/api/usercenter/downloadReferralQRCode?url=${encodeURIComponent(this.qrCodeUrl)}&t=${Date.now()}`\n\n        // 直接打开下载链接\n        window.open(downloadUrl, '_blank')\n\n        this.$notification.success({\n          message: '下载开始',\n          description: '二维码正在下载到本地',\n          placement: 'topRight'\n        })\n      } catch (error) {\n        console.error('下载二维码失败:', error)\n        this.$notification.error({\n          message: '下载失败',\n          description: '二维码下载失败，请稍后重试',\n          placement: 'topRight'\n        })\n      }\n    },\n\n    // 显示提现弹窗\n    openWithdrawModal() {\n      if (this.availableEarnings < 100) {\n        this.$notification.warning({\n          message: '提现金额不足',\n          description: '最低提现金额为100元，请继续推广获得更多收益',\n          placement: 'topRight'\n        })\n        return\n      }\n      this.showWithdrawModal = true\n    },\n\n    // 处理提现申请\n    handleWithdraw() {\n      this.withdrawForm.validateFields((err, values) => {\n        if (err) return\n\n        this.withdrawLoading = true\n\n        // TODO: 调用提现API\n        setTimeout(() => {\n          this.withdrawLoading = false\n          this.showWithdrawModal = false\n          this.withdrawForm.resetFields()\n\n          this.$notification.success({\n            message: '提现申请成功',\n            description: '您的提现申请已提交，预计1-3个工作日到账',\n            placement: 'topRight'\n          })\n        }, 2000)\n      })\n    },\n\n\n\n    // 加载推广用户列表\n    async loadReferralUsers() {\n      try {\n        this.usersLoading = true\n\n        const params = {\n          current: 1,\n          size: 10\n        }\n\n        const response = await this.$http.get('/api/usercenter/referralList', { params })\n\n        if (response.data.success) {\n          const records = (response.data.result && response.data.result.records) || []\n          // 转换数据格式\n          this.referralUsers = records.map((item, index) => ({\n            key: item.id || index,\n            nickname: item.referee_nickname || `用户***${index + 1}`,\n            avatar: item.referee_avatar || '',\n            registerTime: item.register_time || '',\n            status: item.has_membership ? '已转化' : '已注册',\n            reward: item.total_reward || '0.00'\n          }))\n        } else {\n          this.referralUsers = []\n        }\n      } catch (error) {\n        console.error('获取推广用户列表失败:', error)\n        this.referralUsers = []\n      } finally {\n        this.usersLoading = false\n      }\n    },\n\n    // 加载提现记录\n    async loadWithdrawRecords() {\n      try {\n        this.recordsLoading = true\n\n        const params = {\n          current: 1,\n          size: 10\n        }\n\n        const response = await this.$http.get('/api/usercenter/withdrawalHistory', { params })\n\n        if (response.data.success) {\n          const records = (response.data.result && response.data.result.records) || []\n          // 转换数据格式\n          this.withdrawRecords = records.map((item, index) => ({\n            key: item.id || index,\n            amount: item.amount || '0.00',\n            method: item.withdrawalMethod || '支付宝',\n            applyTime: item.applyTime || '',\n            status: this.getWithdrawStatusText(item.status),\n            completeTime: item.completeTime || '-'\n          }))\n        } else {\n          this.withdrawRecords = []\n        }\n      } catch (error) {\n        console.error('获取提现记录失败:', error)\n        this.withdrawRecords = []\n      } finally {\n        this.recordsLoading = false\n      }\n    },\n\n    // 获取提现状态文本\n    getWithdrawStatusText(status) {\n      const statusMap = {\n        0: '待审核',\n        1: '处理中',\n        2: '已完成',\n        3: '已拒绝'\n      }\n      return statusMap[status] || '未知状态'\n    },\n\n    // 获取状态颜色\n    getStatusColor(status) {\n      const colorMap = {\n        '已完成': 'green',\n        '处理中': 'blue',\n        '已拒绝': 'red',\n        '待审核': 'orange'\n      }\n      return colorMap[status] || 'default'\n    },\n\n     // 格式化数字显示\n     formatNumber(num) {\n       if (num === null || num === undefined) return '0'\n       const number = parseFloat(num)\n       if (isNaN(number)) return '0'\n       \n       // 如果是金额，保留两位小数\n       if (num === this.totalEarnings) {\n         return number.toLocaleString('zh-CN', {\n           minimumFractionDigits: 2,\n           maximumFractionDigits: 2\n         })\n       }\n       \n       // 其他数字不保留小数\n       return number.toLocaleString('zh-CN')\n     }\n   }\n }\n</script>\n\n<style scoped>\n.affiliate-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 3rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0 0 1.5rem 0;\n}\n\n.commission-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 12px;\n  background: white;\n  padding: 12px 24px;\n  border-radius: 50px;\n  border: 2px solid #e2e8f0;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.badge-text {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.badge-level {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n/* 分销内容区域 */\n.affiliate-section {\n  padding: 4rem 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.section-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 1.5rem 0;\n  text-align: center;\n}\n\n/* 收益仪表板 */\n.earnings-dashboard {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  margin-bottom: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.earnings-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n  gap: 24px;\n}\n\n.earning-card {\n  display: flex;\n  align-items: center;\n  padding: 24px;\n  border-radius: 16px;\n  background: white;\n  border: 2px solid #f1f5f9;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.earning-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: var(--card-color);\n}\n\n.earning-card.primary {\n  --card-color: #3b82f6;\n}\n\n.earning-card.success {\n  --card-color: #10b981;\n}\n\n.earning-card.warning {\n  --card-color: #f59e0b;\n}\n\n.earning-card.info {\n  --card-color: #8b5cf6;\n}\n\n.earning-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);\n  border-color: var(--card-color);\n}\n\n.card-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  color: white;\n  font-size: 20px;\n  background: var(--card-color);\n}\n\n.card-content {\n  flex: 1;\n}\n\n.earning-number {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 4px;\n  line-height: 1;\n}\n\n.earning-label {\n  font-size: 0.9rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n/* 佣金等级进度 */\n.commission-progress {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  margin-bottom: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.progress-card {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  border-radius: 16px;\n  padding: 32px;\n  border: 2px solid #e2e8f0;\n}\n\n.current-level {\n  margin-bottom: 24px;\n}\n\n.level-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.level-name {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.level-rate {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 6px 16px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 600;\n}\n\n.level-progress {\n  margin-bottom: 8px;\n}\n\n.progress-text {\n  text-align: center;\n  font-size: 0.9rem;\n  color: #6b7280;\n  margin-top: 8px;\n}\n\n.next-level {\n  padding-top: 24px;\n  border-top: 1px solid #e5e7eb;\n}\n\n.next-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.next-text {\n  font-size: 1rem;\n  color: #374151;\n  font-weight: 500;\n}\n\n.next-rate {\n  background: #f3f4f6;\n  color: #6b7280;\n  padding: 4px 12px;\n  border-radius: 16px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.remaining {\n  font-size: 0.9rem;\n  color: #9ca3af;\n  text-align: center;\n}\n\n/* 推广工具 */\n.tools-section {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.tools-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 32px;\n}\n\n.tool-card {\n  background: #fafbfc;\n  border: 2px solid #f1f5f9;\n  border-radius: 16px;\n  padding: 32px;\n  transition: all 0.3s ease;\n}\n\n.tool-card:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.15);\n}\n\n.tool-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 24px;\n}\n\n.tool-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  color: white;\n  font-size: 20px;\n  flex-shrink: 0;\n}\n\n.tool-info h3 {\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n  color: #1f2937;\n}\n\n.tool-info p {\n  color: #6b7280;\n  line-height: 1.5;\n  margin: 0;\n}\n\n.tool-content {\n  margin-top: 16px;\n}\n\n.link-input {\n  width: 100%;\n}\n\n/* 二维码弹窗 */\n.qr-modal-content {\n  text-align: center;\n}\n\n.qr-code-container {\n  margin-bottom: 24px;\n  padding: 20px;\n  background: #f8fafc;\n  border-radius: 12px;\n  border: 2px dashed #d1d5db;\n}\n\n.qr-code-image {\n  max-width: 100%;\n  height: auto;\n  border-radius: 8px;\n}\n\n.qr-actions {\n  margin-top: 16px;\n}\n\n/* 提现弹窗 */\n.withdraw-modal-content {\n  padding: 8px 0;\n}\n\n.withdraw-info {\n  background: #f8fafc;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 24px;\n  border: 1px solid #e2e8f0;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.info-item:last-child {\n  margin-bottom: 0;\n}\n\n.info-label {\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n.info-value {\n  color: #1e293b;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n.withdraw-actions {\n  text-align: right;\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid #f1f5f9;\n}\n\n.card-action {\n  margin-top: 8px;\n}\n\n/* 推广链接区域 */\n.promotion-link-section {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  border: 2px solid #e2e8f0;\n}\n\n.link-main-container {\n  margin-bottom: 1rem;\n}\n\n.link-input-large {\n  margin-bottom: 1rem;\n}\n\n.link-input-large .ant-input {\n  font-size: 1rem;\n  padding: 12px 16px;\n  border-radius: 8px;\n  border: 2px solid #e2e8f0;\n}\n\n.link-actions {\n  display: flex;\n  gap: 12px;\n  flex-wrap: wrap;\n}\n\n.copy-btn {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  border: none;\n  font-weight: 600;\n}\n\n.qr-btn {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  border: none;\n  color: white;\n  font-weight: 600;\n}\n\n\n\n.link-tips {\n  background: #f0f9ff;\n  border: 1px solid #bae6fd;\n  border-radius: 8px;\n  padding: 12px 16px;\n  color: #0369a1;\n  font-size: 0.9rem;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.link-tips strong {\n  color: #1e40af;\n  font-weight: 700;\n}\n\n/* 分成规则表格 */\n.commission-rules {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n}\n\n.rules-table {\n  border: 1px solid #e2e8f0;\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.rule-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.rule-row:last-child {\n  border-bottom: none;\n}\n\n.rule-row.header {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  font-weight: 700;\n  color: #1e293b;\n}\n\n.rule-row.vip {\n  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\n}\n\n.rule-row.svip {\n  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);\n}\n\n.rule-cell {\n  padding: 16px;\n  text-align: center;\n  border-right: 1px solid #e2e8f0;\n}\n\n.rule-cell:last-child {\n  border-right: none;\n}\n\n.rule-cell.highlight {\n  font-weight: 700;\n  color: #dc2626;\n  font-size: 1.1rem;\n}\n\n/* 表格容器 */\n.users-table-container,\n.records-table-container {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n}\n\n.referral-users,\n.withdraw-records {\n  margin-bottom: 3rem;\n}\n\n.reward-amount,\n.withdraw-amount {\n  font-weight: 600;\n  color: #059669;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .page-title {\n    font-size: 2rem;\n  }\n\n  .page-subtitle {\n    font-size: 1rem;\n  }\n\n  .commission-badge {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .earnings-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .tools-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .earnings-dashboard,\n  .commission-progress,\n  .tools-section {\n    padding: 24px;\n  }\n\n  .tool-card {\n    padding: 24px;\n  }\n\n  .progress-card {\n    padding: 24px;\n  }\n\n  .level-info,\n  .next-info {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .tool-header {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .tool-icon {\n    margin: 0 auto 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .affiliate-section {\n    padding: 0 16px 60px;\n  }\n\n  .page-header {\n    padding: 40px 16px 24px;\n  }\n\n  .earning-card {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .card-icon {\n    margin: 0 auto 12px;\n  }\n\n  .link-actions {\n    flex-direction: column;\n  }\n\n  .rule-row {\n    grid-template-columns: 1fr;\n    text-align: left;\n  }\n\n  .rule-cell {\n    border-right: none;\n    border-bottom: 1px solid #e2e8f0;\n    text-align: left;\n  }\n\n  .rule-cell:last-child {\n    border-bottom: none;\n  }\n\n  .promotion-link-section,\n  .commission-rules,\n  .users-table-container,\n  .records-table-container {\n    padding: 1.5rem;\n  }\n}\n</style>\n"]}]}