{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue?vue&type=template&id=a1183866&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue", "mtime": 1753663019793}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"WebsitePage\", [\n    _c(\n      \"div\",\n      { staticClass: \"affiliate-container\" },\n      [\n        _c(\"div\", { staticClass: \"page-header\" }, [\n          _c(\"div\", { staticClass: \"header-content\" }, [\n            _c(\"h1\", { staticClass: \"page-title\" }, [_vm._v(\"分销推广中心\")]),\n            _c(\"p\", { staticClass: \"page-subtitle\" }, [\n              _vm._v(\"推广智界AIGC会员订阅，享受分层佣金奖励\")\n            ]),\n            _c(\"div\", { staticClass: \"commission-badge\" }, [\n              _c(\"span\", { staticClass: \"badge-text\" }, [\n                _vm._v(\"当前佣金率：\" + _vm._s(_vm.currentCommissionRate) + \"%\")\n              ]),\n              _c(\"span\", { staticClass: \"badge-level\" }, [\n                _vm._v(_vm._s(_vm.commissionLevelText))\n              ])\n            ])\n          ])\n        ]),\n        _c(\"section\", { staticClass: \"affiliate-section\" }, [\n          _c(\"div\", { staticClass: \"container\" }, [\n            _c(\"div\", { staticClass: \"earnings-dashboard\" }, [\n              _c(\"h2\", { staticClass: \"section-title\" }, [_vm._v(\"收益概览\")]),\n              _c(\"div\", { staticClass: \"earnings-grid\" }, [\n                _c(\"div\", { staticClass: \"earning-card primary\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"dollar\" } })],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-content\" },\n                    [\n                      _c(\n                        \"a-spin\",\n                        { attrs: { spinning: _vm.loading, size: \"small\" } },\n                        [\n                          _c(\"div\", { staticClass: \"earning-number\" }, [\n                            _vm._v(\n                              \"¥\" + _vm._s(_vm.formatNumber(_vm.totalEarnings))\n                            )\n                          ]),\n                          _c(\"div\", { staticClass: \"earning-label\" }, [\n                            _vm._v(\"累计收益\")\n                          ])\n                        ]\n                      )\n                    ],\n                    1\n                  )\n                ]),\n                _c(\"div\", { staticClass: \"earning-card success\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"team\" } })],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-content\" },\n                    [\n                      _c(\n                        \"a-spin\",\n                        { attrs: { spinning: _vm.loading, size: \"small\" } },\n                        [\n                          _c(\"div\", { staticClass: \"earning-number\" }, [\n                            _vm._v(_vm._s(_vm.formatNumber(_vm.totalReferrals)))\n                          ]),\n                          _c(\"div\", { staticClass: \"earning-label\" }, [\n                            _vm._v(\"推荐注册\")\n                          ])\n                        ]\n                      )\n                    ],\n                    1\n                  )\n                ]),\n                _c(\"div\", { staticClass: \"earning-card warning\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"crown\" } })],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-content\" },\n                    [\n                      _c(\n                        \"a-spin\",\n                        { attrs: { spinning: _vm.loading, size: \"small\" } },\n                        [\n                          _c(\"div\", { staticClass: \"earning-number\" }, [\n                            _vm._v(\n                              _vm._s(_vm.formatNumber(_vm.memberReferrals))\n                            )\n                          ]),\n                          _c(\"div\", { staticClass: \"earning-label\" }, [\n                            _vm._v(\"会员转化\")\n                          ])\n                        ]\n                      )\n                    ],\n                    1\n                  )\n                ]),\n                _c(\"div\", { staticClass: \"earning-card info\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"percentage\" } })],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-content\" },\n                    [\n                      _c(\n                        \"a-spin\",\n                        { attrs: { spinning: _vm.loading, size: \"small\" } },\n                        [\n                          _c(\"div\", { staticClass: \"earning-number\" }, [\n                            _vm._v(_vm._s(_vm.conversionRate) + \"%\")\n                          ]),\n                          _c(\"div\", { staticClass: \"earning-label\" }, [\n                            _vm._v(\"会员转化率\")\n                          ])\n                        ]\n                      )\n                    ],\n                    1\n                  )\n                ])\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"commission-progress\" }, [\n              _c(\"h2\", { staticClass: \"section-title\" }, [\n                _vm._v(\"佣金等级进度\")\n              ]),\n              _c(\"div\", { staticClass: \"progress-card\" }, [\n                _c(\"div\", { staticClass: \"current-level\" }, [\n                  _c(\"div\", { staticClass: \"level-info\" }, [\n                    _c(\"span\", { staticClass: \"level-name\" }, [\n                      _vm._v(_vm._s(_vm.commissionLevelText))\n                    ]),\n                    _c(\"span\", { staticClass: \"level-rate\" }, [\n                      _vm._v(_vm._s(_vm.currentCommissionRate) + \"%佣金\")\n                    ])\n                  ]),\n                  _c(\n                    \"div\",\n                    { staticClass: \"level-progress\" },\n                    [\n                      _c(\"a-progress\", {\n                        attrs: {\n                          percent: _vm.levelProgress,\n                          \"stroke-color\": _vm.progressColor,\n                          \"show-info\": false\n                        }\n                      }),\n                      _c(\"div\", { staticClass: \"progress-text\" }, [\n                        _vm._v(\n                          \"\\n                  \" +\n                            _vm._s(_vm.memberReferrals) +\n                            \"/\" +\n                            _vm._s(_vm.nextLevelRequirement) +\n                            \" 会员转化\\n                \"\n                        )\n                      ])\n                    ],\n                    1\n                  )\n                ]),\n                _vm.nextLevelRequirement > 0\n                  ? _c(\"div\", { staticClass: \"next-level\" }, [\n                      _c(\"div\", { staticClass: \"next-info\" }, [\n                        _c(\"span\", { staticClass: \"next-text\" }, [\n                          _vm._v(\"下一等级：\" + _vm._s(_vm.nextLevelText))\n                        ]),\n                        _c(\"span\", { staticClass: \"next-rate\" }, [\n                          _vm._v(_vm._s(_vm.nextLevelRate) + \"%佣金\")\n                        ])\n                      ]),\n                      _c(\"div\", { staticClass: \"remaining\" }, [\n                        _vm._v(\n                          \"\\n                还需 \" +\n                            _vm._s(\n                              _vm.nextLevelRequirement - _vm.memberReferrals\n                            ) +\n                            \" 个会员转化\\n              \"\n                        )\n                      ])\n                    ])\n                  : _vm._e()\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"tools-section\" }, [\n              _c(\"h2\", { staticClass: \"section-title\" }, [_vm._v(\"推广工具\")]),\n              _c(\"div\", { staticClass: \"tools-grid\" }, [\n                _c(\"div\", { staticClass: \"tool-card\" }, [\n                  _c(\"div\", { staticClass: \"tool-header\" }, [\n                    _c(\n                      \"div\",\n                      { staticClass: \"tool-icon\" },\n                      [_c(\"a-icon\", { attrs: { type: \"link\" } })],\n                      1\n                    ),\n                    _c(\"div\", { staticClass: \"tool-info\" }, [\n                      _c(\"h3\", [_vm._v(\"专属推广链接\")]),\n                      _c(\"p\", [_vm._v(\"分享链接，好友订阅会员即可获得佣金\")])\n                    ])\n                  ]),\n                  _c(\"div\", { staticClass: \"tool-content\" }, [\n                    _c(\n                      \"div\",\n                      { staticClass: \"link-input\" },\n                      [\n                        _c(\n                          \"a-input\",\n                          {\n                            attrs: {\n                              value: _vm.affiliateLink || \"正在生成推广链接...\",\n                              readonly: \"\",\n                              loading: _vm.loading\n                            }\n                          },\n                          [\n                            _c(\n                              \"template\",\n                              { slot: \"addonAfter\" },\n                              [\n                                _c(\n                                  \"a-button\",\n                                  {\n                                    attrs: {\n                                      type: \"primary\",\n                                      size: \"small\",\n                                      disabled:\n                                        !_vm.affiliateLink || _vm.loading\n                                    },\n                                    on: { click: _vm.copyLink }\n                                  },\n                                  [\n                                    _vm._v(\n                                      \"\\n                        复制链接\\n                      \"\n                                    )\n                                  ]\n                                )\n                              ],\n                              1\n                            )\n                          ],\n                          2\n                        )\n                      ],\n                      1\n                    )\n                  ])\n                ]),\n                _c(\"div\", { staticClass: \"tool-card\" }, [\n                  _c(\"div\", { staticClass: \"tool-header\" }, [\n                    _c(\n                      \"div\",\n                      { staticClass: \"tool-icon\" },\n                      [_c(\"a-icon\", { attrs: { type: \"qrcode\" } })],\n                      1\n                    ),\n                    _c(\"div\", { staticClass: \"tool-info\" }, [\n                      _c(\"h3\", [_vm._v(\"推广二维码\")]),\n                      _c(\"p\", [_vm._v(\"生成专属二维码，方便线下推广分享\")])\n                    ])\n                  ]),\n                  _c(\n                    \"div\",\n                    { staticClass: \"tool-content\" },\n                    [\n                      _c(\n                        \"a-button\",\n                        {\n                          attrs: {\n                            type: \"default\",\n                            block: \"\",\n                            loading: _vm.qrLoading\n                          },\n                          on: { click: _vm.generateQRCode }\n                        },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"qrcode\" } }),\n                          _vm._v(\n                            \"\\n                  生成二维码\\n                \"\n                          )\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                ])\n              ])\n            ])\n          ])\n        ]),\n        _c(\n          \"a-modal\",\n          {\n            attrs: {\n              title: \"推广二维码\",\n              footer: null,\n              width: \"400px\",\n              centered: \"\"\n            },\n            model: {\n              value: _vm.showQRModal,\n              callback: function($$v) {\n                _vm.showQRModal = $$v\n              },\n              expression: \"showQRModal\"\n            }\n          },\n          [\n            _c(\"div\", { staticClass: \"qr-modal-content\" }, [\n              _vm.qrCodeUrl\n                ? _c(\"div\", { staticClass: \"qr-code-container\" }, [\n                    _c(\"img\", {\n                      staticClass: \"qr-code-image\",\n                      attrs: { src: _vm.qrCodeUrl, alt: \"推广二维码\" }\n                    })\n                  ])\n                : _vm._e(),\n              _c(\n                \"div\",\n                { staticClass: \"qr-actions\" },\n                [\n                  _vm.qrCodeUrl\n                    ? _c(\n                        \"a-button\",\n                        {\n                          attrs: { type: \"primary\", block: \"\" },\n                          on: { click: _vm.downloadQRCode }\n                        },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"download\" } }),\n                          _vm._v(\"\\n            下载二维码\\n          \")\n                        ],\n                        1\n                      )\n                    : _vm._e()\n                ],\n                1\n              )\n            ])\n          ]\n        )\n      ],\n      1\n    )\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}