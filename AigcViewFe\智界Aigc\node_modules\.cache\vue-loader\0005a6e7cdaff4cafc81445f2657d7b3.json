{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue?vue&type=template&id=a1183866&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue", "mtime": 1753663019793}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<WebsitePage>\n  <div class=\"affiliate-container\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <h1 class=\"page-title\">分销推广中心</h1>\n        <p class=\"page-subtitle\">推广智界AIGC会员订阅，享受分层佣金奖励</p>\n        <div class=\"commission-badge\">\n          <span class=\"badge-text\">当前佣金率：{{ currentCommissionRate }}%</span>\n          <span class=\"badge-level\">{{ commissionLevelText }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 分销内容区域 -->\n    <section class=\"affiliate-section\">\n      <div class=\"container\">\n        <!-- 收益展示 -->\n        <div class=\"earnings-dashboard\">\n          <h2 class=\"section-title\">收益概览</h2>\n          <div class=\"earnings-grid\">\n            <div class=\"earning-card primary\">\n              <div class=\"card-icon\">\n                <a-icon type=\"dollar\" />\n              </div>\n              <div class=\"card-content\">\n                <a-spin :spinning=\"loading\" size=\"small\">\n                  <div class=\"earning-number\">¥{{ formatNumber(totalEarnings) }}</div>\n                  <div class=\"earning-label\">累计收益</div>\n                </a-spin>\n              </div>\n            </div>\n\n            <div class=\"earning-card success\">\n              <div class=\"card-icon\">\n                <a-icon type=\"team\" />\n              </div>\n              <div class=\"card-content\">\n                <a-spin :spinning=\"loading\" size=\"small\">\n                  <div class=\"earning-number\">{{ formatNumber(totalReferrals) }}</div>\n                  <div class=\"earning-label\">推荐注册</div>\n                </a-spin>\n              </div>\n            </div>\n\n            <div class=\"earning-card warning\">\n              <div class=\"card-icon\">\n                <a-icon type=\"crown\" />\n              </div>\n              <div class=\"card-content\">\n                <a-spin :spinning=\"loading\" size=\"small\">\n                  <div class=\"earning-number\">{{ formatNumber(memberReferrals) }}</div>\n                  <div class=\"earning-label\">会员转化</div>\n                </a-spin>\n              </div>\n            </div>\n\n            <div class=\"earning-card info\">\n              <div class=\"card-icon\">\n                <a-icon type=\"percentage\" />\n              </div>\n              <div class=\"card-content\">\n                <a-spin :spinning=\"loading\" size=\"small\">\n                  <div class=\"earning-number\">{{ conversionRate }}%</div>\n                  <div class=\"earning-label\">会员转化率</div>\n                </a-spin>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 佣金等级进度 -->\n        <div class=\"commission-progress\">\n          <h2 class=\"section-title\">佣金等级进度</h2>\n          <div class=\"progress-card\">\n            <div class=\"current-level\">\n              <div class=\"level-info\">\n                <span class=\"level-name\">{{ commissionLevelText }}</span>\n                <span class=\"level-rate\">{{ currentCommissionRate }}%佣金</span>\n              </div>\n              <div class=\"level-progress\">\n                <a-progress\n                  :percent=\"levelProgress\"\n                  :stroke-color=\"progressColor\"\n                  :show-info=\"false\"\n                />\n                <div class=\"progress-text\">\n                  {{ memberReferrals }}/{{ nextLevelRequirement }} 会员转化\n                </div>\n              </div>\n            </div>\n            <div class=\"next-level\" v-if=\"nextLevelRequirement > 0\">\n              <div class=\"next-info\">\n                <span class=\"next-text\">下一等级：{{ nextLevelText }}</span>\n                <span class=\"next-rate\">{{ nextLevelRate }}%佣金</span>\n              </div>\n              <div class=\"remaining\">\n                还需 {{ nextLevelRequirement - memberReferrals }} 个会员转化\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 推广工具 -->\n        <div class=\"tools-section\">\n          <h2 class=\"section-title\">推广工具</h2>\n          <div class=\"tools-grid\">\n            <div class=\"tool-card\">\n              <div class=\"tool-header\">\n                <div class=\"tool-icon\">\n                  <a-icon type=\"link\" />\n                </div>\n                <div class=\"tool-info\">\n                  <h3>专属推广链接</h3>\n                  <p>分享链接，好友订阅会员即可获得佣金</p>\n                </div>\n              </div>\n              <div class=\"tool-content\">\n                <div class=\"link-input\">\n                  <a-input\n                    :value=\"affiliateLink || '正在生成推广链接...'\"\n                    readonly\n                    :loading=\"loading\"\n                  >\n                    <template slot=\"addonAfter\">\n                      <a-button\n                        type=\"primary\"\n                        size=\"small\"\n                        :disabled=\"!affiliateLink || loading\"\n                        @click=\"copyLink\"\n                      >\n                        复制链接\n                      </a-button>\n                    </template>\n                  </a-input>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"tool-card\">\n              <div class=\"tool-header\">\n                <div class=\"tool-icon\">\n                  <a-icon type=\"qrcode\" />\n                </div>\n                <div class=\"tool-info\">\n                  <h3>推广二维码</h3>\n                  <p>生成专属二维码，方便线下推广分享</p>\n                </div>\n              </div>\n              <div class=\"tool-content\">\n                <a-button\n                  type=\"default\"\n                  block\n                  :loading=\"qrLoading\"\n                  @click=\"generateQRCode\"\n                >\n                  <a-icon type=\"qrcode\" />\n                  生成二维码\n                </a-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 二维码弹窗 -->\n    <a-modal\n      v-model=\"showQRModal\"\n      title=\"推广二维码\"\n      :footer=\"null\"\n      width=\"400px\"\n      centered\n    >\n      <div class=\"qr-modal-content\">\n        <div class=\"qr-code-container\" v-if=\"qrCodeUrl\">\n          <img :src=\"qrCodeUrl\" alt=\"推广二维码\" class=\"qr-code-image\" />\n        </div>\n        <div class=\"qr-actions\">\n          <a-button type=\"primary\" block @click=\"downloadQRCode\" v-if=\"qrCodeUrl\">\n            <a-icon type=\"download\" />\n            下载二维码\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n  </div>\n</WebsitePage>\n", null]}