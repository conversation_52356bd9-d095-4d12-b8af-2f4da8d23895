{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue?vue&type=template&id=a1183866&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue", "mtime": 1753666109942}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<WebsitePage>\n  <div class=\"affiliate-container\">\n    <!-- 简洁页面标题 -->\n    <div class=\"simple-header\">\n      <h1 class=\"simple-title\">分销推广</h1>\n      <p class=\"simple-subtitle\">加入分销计划，推广智界AIGC获得丰厚佣金</p>\n      <div class=\"commission-badge\">\n        <span class=\"badge-text\">当前佣金率：{{ currentCommissionRate }}%</span>\n        <span class=\"badge-level\">{{ commissionLevelText }}</span>\n      </div>\n    </div>\n\n    <!-- 分销内容区域 -->\n    <section class=\"affiliate-section\">\n      <div class=\"container\">\n        <!-- 推广链接区域 - 最显眼位置 -->\n        <div class=\"promotion-link-section\">\n          <h2 class=\"section-title\">您的专属推广链接</h2>\n          <div class=\"link-main-container\">\n            <div class=\"link-input-large\">\n              <a-input\n                :value=\"affiliateLink || '正在生成推广链接...'\"\n                readonly\n                :loading=\"loading\"\n                size=\"large\"\n                placeholder=\"推广链接生成中...\"\n              />\n            </div>\n            <div class=\"link-actions\">\n              <a-button\n                type=\"primary\"\n                size=\"large\"\n                :disabled=\"!affiliateLink || loading\"\n                @click=\"copyLink\"\n                class=\"copy-btn\"\n              >\n                <a-icon type=\"copy\" />\n                复制链接\n              </a-button>\n              <a-button\n                size=\"large\"\n                :loading=\"qrLoading\"\n                @click=\"generateQRCode\"\n                class=\"qr-btn\"\n              >\n                <a-icon type=\"qrcode\" />\n                生成二维码\n              </a-button>\n            </div>\n          </div>\n          <div class=\"link-tips\">\n            <a-icon type=\"info-circle\" />\n            分享此链接，好友注册并订阅会员后，您将获得 <strong>{{ currentCommissionRate }}%</strong> 的佣金奖励\n          </div>\n        </div>\n\n        <!-- 收益展示 -->\n        <div class=\"earnings-dashboard\">\n          <h2 class=\"section-title\">收益概览</h2>\n          <div class=\"earnings-grid\">\n            <div class=\"earning-card primary\">\n              <div class=\"card-icon\">\n                <a-icon type=\"dollar\" />\n              </div>\n              <div class=\"card-content\">\n                <a-spin :spinning=\"loading\" size=\"small\">\n                  <div class=\"earning-number\">¥{{ formatNumber(totalEarnings) }}</div>\n                  <div class=\"earning-label\">累计收益</div>\n                </a-spin>\n              </div>\n            </div>\n\n            <div class=\"earning-card success\">\n              <div class=\"card-icon\">\n                <a-icon type=\"wallet\" />\n              </div>\n              <div class=\"card-content\">\n                <a-spin :spinning=\"loading\" size=\"small\">\n                  <div class=\"earning-number\">¥{{ formatNumber(availableEarnings) }}</div>\n                  <div class=\"earning-label\">可提现金额</div>\n                </a-spin>\n                <div class=\"card-action\">\n                  <a-button\n                    type=\"primary\"\n                    size=\"small\"\n                    :disabled=\"availableEarnings <= 0 || loading\"\n                    @click=\"openWithdrawModal\"\n                  >\n                    立即提现\n                  </a-button>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"earning-card info\">\n              <div class=\"card-icon\">\n                <a-icon type=\"team\" />\n              </div>\n              <div class=\"card-content\">\n                <a-spin :spinning=\"loading\" size=\"small\">\n                  <div class=\"earning-number\">{{ formatNumber(totalReferrals) }}</div>\n                  <div class=\"earning-label\">推荐注册</div>\n                </a-spin>\n              </div>\n            </div>\n\n            <div class=\"earning-card warning\">\n              <div class=\"card-icon\">\n                <a-icon type=\"crown\" />\n              </div>\n              <div class=\"card-content\">\n                <a-spin :spinning=\"loading\" size=\"small\">\n                  <div class=\"earning-number\">{{ formatNumber(memberReferrals) }}</div>\n                  <div class=\"earning-label\">转化人数</div>\n                </a-spin>\n              </div>\n            </div>\n\n\n          </div>\n        </div>\n\n        <!-- 佣金等级进度 -->\n        <div class=\"commission-progress\">\n          <h2 class=\"section-title\">佣金等级进度</h2>\n          <div class=\"progress-card\">\n            <div class=\"current-level\">\n              <div class=\"level-info\">\n                <span class=\"level-name\">{{ commissionLevelText }}</span>\n                <span class=\"level-rate\">{{ currentCommissionRate }}%佣金</span>\n              </div>\n              <div class=\"level-progress\">\n                <a-progress\n                  :percent=\"levelProgress\"\n                  :stroke-color=\"progressColor\"\n                  :show-info=\"false\"\n                />\n                <div class=\"progress-text\">\n                  {{ memberReferrals }}/{{ nextLevelRequirement }} 转化用户\n                </div>\n              </div>\n            </div>\n            <div class=\"next-level\" v-if=\"nextLevelRequirement > 0\">\n              <div class=\"next-info\">\n                <span class=\"next-text\">下一等级：{{ nextLevelText }}</span>\n                <span class=\"next-rate\">{{ nextLevelRate }}%佣金</span>\n              </div>\n              <div class=\"remaining\">\n                还需 {{ nextLevelRequirement - memberReferrals }} 个转化用户\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分成规则说明 -->\n        <div class=\"commission-rules\">\n          <h2 class=\"section-title\">分成规则</h2>\n          <div class=\"rules-table\">\n            <div class=\"rule-row header\">\n              <div class=\"rule-cell\">用户等级</div>\n              <div class=\"rule-cell\">推广人数要求</div>\n              <div class=\"rule-cell\">佣金比例</div>\n              <div class=\"rule-cell\">说明</div>\n            </div>\n            <div class=\"rule-row\">\n              <div class=\"rule-cell\">普通用户</div>\n              <div class=\"rule-cell\">0-9人</div>\n              <div class=\"rule-cell highlight\">30%</div>\n              <div class=\"rule-cell\">新手推广员</div>\n            </div>\n            <div class=\"rule-row\">\n              <div class=\"rule-cell\">普通用户</div>\n              <div class=\"rule-cell\">10-29人</div>\n              <div class=\"rule-cell highlight\">40%</div>\n              <div class=\"rule-cell\">高级推广员</div>\n            </div>\n            <div class=\"rule-row\">\n              <div class=\"rule-cell\">普通用户</div>\n              <div class=\"rule-cell\">30人以上</div>\n              <div class=\"rule-cell highlight\">50%</div>\n              <div class=\"rule-cell\">顶级推广员</div>\n            </div>\n            <div class=\"rule-row vip\">\n              <div class=\"rule-cell\">VIP用户</div>\n              <div class=\"rule-cell\">基础+5%</div>\n              <div class=\"rule-cell highlight\">35%-50%</div>\n              <div class=\"rule-cell\">VIP推广员</div>\n            </div>\n            <div class=\"rule-row svip\">\n              <div class=\"rule-cell\">SVIP用户</div>\n              <div class=\"rule-cell\">无要求</div>\n              <div class=\"rule-cell highlight\">50%</div>\n              <div class=\"rule-cell\">SVIP推广员</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 推广用户列表 -->\n        <div class=\"referral-users\">\n          <h2 class=\"section-title\">我的推广用户</h2>\n          <div class=\"users-table-container\">\n            <a-table\n              :columns=\"userColumns\"\n              :data-source=\"referralUsers\"\n              :loading=\"usersLoading\"\n              :pagination=\"{ pageSize: 10, showSizeChanger: false }\"\n              size=\"middle\"\n            >\n              <template slot=\"avatar\" slot-scope=\"text, record\">\n                <a-avatar :src=\"record.avatar\" :style=\"{ backgroundColor: '#87d068' }\">\n                  {{ record.nickname ? record.nickname.charAt(0) : 'U' }}\n                </a-avatar>\n              </template>\n              <template slot=\"status\" slot-scope=\"text\">\n                <a-tag :color=\"text === '已转化' ? 'green' : 'blue'\">\n                  {{ text }}\n                </a-tag>\n              </template>\n              <template slot=\"reward\" slot-scope=\"text\">\n                <span class=\"reward-amount\">¥{{ text || '0.00' }}</span>\n              </template>\n            </a-table>\n          </div>\n        </div>\n\n        <!-- 提现记录 -->\n        <div class=\"withdraw-records\">\n          <h2 class=\"section-title\">提现记录</h2>\n          <div class=\"records-table-container\">\n            <a-table\n              :columns=\"withdrawColumns\"\n              :data-source=\"withdrawRecords\"\n              :loading=\"recordsLoading\"\n              :pagination=\"{ pageSize: 10, showSizeChanger: false }\"\n              size=\"middle\"\n            >\n              <template slot=\"status\" slot-scope=\"text\">\n                <a-tag :color=\"getStatusColor(text)\">\n                  {{ text }}\n                </a-tag>\n              </template>\n              <template slot=\"amount\" slot-scope=\"text\">\n                <span class=\"withdraw-amount\">¥{{ text }}</span>\n              </template>\n            </a-table>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 二维码弹窗 -->\n    <a-modal\n      v-model=\"showQRModal\"\n      title=\"推广二维码\"\n      :footer=\"null\"\n      width=\"400px\"\n      centered\n    >\n      <div class=\"qr-modal-content\">\n        <div class=\"qr-code-container\" v-if=\"qrCodeUrl\">\n          <img :src=\"qrCodeUrl\" alt=\"推广二维码\" class=\"qr-code-image\" />\n        </div>\n        <div class=\"qr-actions\">\n          <a-button type=\"primary\" block @click=\"downloadQRCode\" v-if=\"qrCodeUrl\">\n            <a-icon type=\"download\" />\n            下载二维码\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n\n    <!-- 提现弹窗 -->\n    <a-modal\n      v-model=\"showWithdrawModal\"\n      title=\"申请提现\"\n      :footer=\"null\"\n      width=\"500px\"\n      centered\n    >\n      <div class=\"withdraw-modal-content\">\n        <div class=\"withdraw-info\">\n          <div class=\"info-item\">\n            <span class=\"info-label\">可提现金额：</span>\n            <span class=\"info-value\">¥{{ formatNumber(availableEarnings) }}</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"info-label\">最低提现金额：</span>\n            <span class=\"info-value\">¥100.00</span>\n          </div>\n        </div>\n\n        <a-form :form=\"withdrawForm\" @submit=\"handleWithdraw\">\n          <a-form-item label=\"提现金额\">\n            <a-input-number\n              v-decorator=\"['amount', {\n                rules: [\n                  { required: true, message: '请输入提现金额' },\n                  { type: 'number', min: 100, message: '最低提现金额为100元' },\n                  { type: 'number', max: availableEarnings, message: '提现金额不能超过可提现金额' }\n                ]\n              }]\"\n              :min=\"100\"\n              :max=\"availableEarnings\"\n              :precision=\"2\"\n              style=\"width: 100%\"\n              placeholder=\"请输入提现金额\"\n            >\n              <template slot=\"addonAfter\">元</template>\n            </a-input-number>\n          </a-form-item>\n\n          <a-form-item label=\"提现方式\">\n            <a-select\n              v-decorator=\"['method', {\n                rules: [{ required: true, message: '请选择提现方式' }],\n                initialValue: 'alipay'\n              }]\"\n              placeholder=\"请选择提现方式\"\n            >\n              <a-select-option value=\"alipay\">支付宝</a-select-option>\n              <a-select-option value=\"wechat\">微信</a-select-option>\n              <a-select-option value=\"bank\">银行卡</a-select-option>\n            </a-select>\n          </a-form-item>\n\n          <a-form-item label=\"收款账号\">\n            <a-input\n              v-decorator=\"['account', {\n                rules: [{ required: true, message: '请输入收款账号' }]\n              }]\"\n              placeholder=\"请输入收款账号\"\n            />\n          </a-form-item>\n\n          <a-form-item label=\"收款人姓名\">\n            <a-input\n              v-decorator=\"['name', {\n                rules: [{ required: true, message: '请输入收款人姓名' }]\n              }]\"\n              placeholder=\"请输入收款人姓名\"\n            />\n          </a-form-item>\n        </a-form>\n\n        <div class=\"withdraw-actions\">\n          <a-button @click=\"showWithdrawModal = false\" style=\"margin-right: 8px\">\n            取消\n          </a-button>\n          <a-button\n            type=\"primary\"\n            :loading=\"withdrawLoading\"\n            @click=\"handleWithdraw\"\n          >\n            申请提现\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n  </div>\n</WebsitePage>\n", null]}