package org.jeecg.modules.system.service;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.config.AlipayConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 支付宝支付服务
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Slf4j
@Service
public class AlipayService {

    @Autowired
    private AlipayConfig alipayConfig;

    private AlipayClient alipayClient;

    /**
     * 获取支付宝客户端
     */
    private AlipayClient getAlipayClient() {
        if (alipayClient == null) {
            alipayClient = new DefaultAlipayClient(
                alipayConfig.getGatewayUrl(),
                alipayConfig.getAppId(),
                alipayConfig.getPrivateKey(),
                alipayConfig.getFormat(),
                alipayConfig.getCharset(),
                alipayConfig.getAlipayPublicKey(),
                alipayConfig.getSignType()
            );
        }
        return alipayClient;
    }

    /**
     * 创建支付订单
     * 
     * @param orderId 订单号
     * @param amount 支付金额
     * @param subject 订单标题
     * @param body 订单描述
     * @return 支付表单HTML
     */
    public String createPayOrder(String orderId, BigDecimal amount, String subject, String body) {
        try {
            log.info("💰 创建支付宝支付订单 - 订单号: {}, 金额: {}", orderId, amount);

            AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
            
            // 设置回调地址
            request.setNotifyUrl(alipayConfig.getFullNotifyUrl());
            request.setReturnUrl(alipayConfig.getFullReturnUrl());

            // 设置请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("out_trade_no", orderId);
            bizContent.put("total_amount", amount.toString());
            bizContent.put("subject", subject);
            bizContent.put("body", body);
            bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");

            request.setBizContent(com.alibaba.fastjson.JSON.toJSONString(bizContent));

            AlipayTradePagePayResponse response = getAlipayClient().pageExecute(request);
            
            if (response.isSuccess()) {
                log.info("💰 支付宝支付订单创建成功 - 订单号: {}", orderId);
                return response.getBody();
            } else {
                log.error("💰 支付宝支付订单创建失败 - 订单号: {}, 错误: {}", orderId, response.getMsg());
                throw new RuntimeException("创建支付订单失败: " + response.getMsg());
            }

        } catch (AlipayApiException e) {
            log.error("💰 支付宝API调用异常 - 订单号: {}", orderId, e);
            throw new RuntimeException("支付宝API调用异常: " + e.getMessage());
        }
    }

    /**
     * 查询支付订单状态
     * 
     * @param orderId 订单号
     * @return 订单状态信息
     */
    public Map<String, Object> queryPayOrder(String orderId) {
        try {
            log.info("💰 查询支付宝订单状态 - 订单号: {}", orderId);

            AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
            
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("out_trade_no", orderId);
            request.setBizContent(com.alibaba.fastjson.JSON.toJSONString(bizContent));

            AlipayTradeQueryResponse response = getAlipayClient().execute(request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", response.isSuccess());
            result.put("tradeStatus", response.getTradeStatus());
            result.put("totalAmount", response.getTotalAmount());
            result.put("buyerPayAmount", response.getBuyerPayAmount());
            result.put("tradeNo", response.getTradeNo());
            result.put("outTradeNo", response.getOutTradeNo());

            log.info("💰 支付宝订单状态查询结果 - 订单号: {}, 状态: {}", orderId, response.getTradeStatus());
            return result;

        } catch (AlipayApiException e) {
            log.error("💰 查询支付宝订单状态异常 - 订单号: {}", orderId, e);
            throw new RuntimeException("查询订单状态异常: " + e.getMessage());
        }
    }

    /**
     * 验证支付宝异步通知签名
     * 
     * @param request HTTP请求
     * @return 验证结果
     */
    public boolean verifyNotify(HttpServletRequest request) {
        try {
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> requestParams = request.getParameterMap();
            
            for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext();) {
                String name = iter.next();
                String[] values = requestParams.get(name);
                String valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
                }
                params.put(name, valueStr);
            }

            boolean signVerified = AlipaySignature.rsaCheckV1(
                params, 
                alipayConfig.getAlipayPublicKey(), 
                alipayConfig.getCharset(), 
                alipayConfig.getSignType()
            );

            log.info("💰 支付宝异步通知签名验证结果: {}", signVerified);
            return signVerified;

        } catch (AlipayApiException e) {
            log.error("💰 支付宝异步通知签名验证异常", e);
            return false;
        }
    }
}
