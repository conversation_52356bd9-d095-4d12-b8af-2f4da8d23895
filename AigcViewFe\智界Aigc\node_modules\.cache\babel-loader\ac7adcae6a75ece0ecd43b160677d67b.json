{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue", "mtime": 1753512620053}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { login, getCaptcha, phoneLogin, emailLogin } from '@/api/login';\nimport { encryption, getEncryptedString } from '@/utils/encryption/aesEncrypt';\nimport { getAction } from '@/api/manage';\nimport { gsap } from 'gsap';\nimport WebsiteHeader from '@/components/website/WebsiteHeader.vue';\nimport LogoImage from '@/components/common/LogoImage.vue';\nimport { ACCESS_TOKEN, USER_NAME, USER_INFO, UI_CACHE_DB_DICT_DATA } from '@/store/mutation-types';\nimport { checkUsername, sendSmsCode, sendEmailCode, register, generateWechatQrCode } from '@/api/register';\nimport Vue from 'vue';\nimport { handleLoginConflict } from '@/utils/loginConflictHandler';\nexport default {\n  name: 'WebsiteLogin',\n  components: {\n    WebsiteHeader: WebsiteHeader,\n    LogoImage: LogoImage\n  },\n  data: function data() {\n    return {\n      // 登录相关\n      form: this.$form.createForm(this),\n      phoneLoginForm: this.$form.createForm(this),\n      emailLoginForm: this.$form.createForm(this),\n      loginLoading: false,\n      phoneLoginLoading: false,\n      emailLoginLoading: false,\n      rememberMe: false,\n      randCodeImage: '',\n      currdatetime: new Date().getTime(),\n      encryptedString: '',\n      // 登录方式切换\n      loginType: 'phone',\n      // password, phone, email, wechat - 默认手机号登录\n      // 验证码相关\n      smsCodeSending: false,\n      smsCountdown: 0,\n      emailCodeSending: false,\n      emailCountdown: 0,\n      // 邀请码（静默处理）\n      inviteCodeFromUrl: '',\n      // 微信登录\n      wechatLoginQrCode: '',\n      features: [{\n        icon: 'robot',\n        title: 'AI智能创作',\n        description: '强大的AI算法，助您快速生成高质量内容'\n      }, {\n        icon: 'thunderbolt',\n        title: '极速响应',\n        description: '毫秒级响应速度，让创作灵感不再等待'\n      }, {\n        icon: 'safety-certificate',\n        title: '安全可靠',\n        description: '企业级安全保障，保护您的创作成果'\n      }, {\n        icon: 'global',\n        title: '全球服务',\n        description: '覆盖全球的CDN网络，随时随地畅享服务'\n      }]\n    };\n  },\n  mounted: function mounted() {\n    this.getEncrypte();\n    this.handleChangeCheckCode();\n    this.initAnimations();\n    this.checkInviteCode();\n  },\n  methods: {\n    // 获取密码加密规则\n    getEncrypte: function getEncrypte() {\n      var _this = this;\n\n      getEncryptedString().then(function (data) {\n        _this.encryptedString = data;\n      });\n    },\n    // 刷新验证码\n    handleChangeCheckCode: function handleChangeCheckCode() {\n      var _this2 = this;\n\n      this.currdatetime = new Date().getTime();\n      getAction(\"/sys/randomImage/\".concat(this.currdatetime)).then(function (res) {\n        if (res.success) {\n          _this2.randCodeImage = res.result;\n        } else {\n          _this2.$message.error(res.message);\n        }\n      }).catch(function () {\n        _this2.$message.error('验证码加载失败');\n      });\n    },\n    handleSubmit: function handleSubmit(e) {\n      var _this3 = this;\n\n      e.preventDefault();\n      this.form.validateFields(function (err, values) {\n        if (!err) {\n          _this3.loginLoading = true;\n          console.log('官网登录信息:', values); // 使用真实的登录API\n\n          var user = encryption(values.username, _this3.encryptedString.key, _this3.encryptedString.iv);\n          var pwd = encryption(values.password, _this3.encryptedString.key, _this3.encryptedString.iv);\n          var loginParams = {\n            username: user,\n            password: pwd,\n            captcha: values.inputCode,\n            checkKey: _this3.currdatetime,\n            remember_me: _this3.rememberMe,\n            loginType: 'website' // 标识为官网用户登录\n\n          };\n          console.log(\"官网登录参数\", loginParams);\n          login(loginParams).then( /*#__PURE__*/function () {\n            var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee(res) {\n              var result, userInfo, roleRes, userRole, departId, redirectPath, _redirectPath, _redirectPath2;\n\n              return _regeneratorRuntime.wrap(function _callee$(_context) {\n                while (1) {\n                  switch (_context.prev = _context.next) {\n                    case 0:\n                      _this3.loginLoading = false;\n                      console.log(\"🔍 登录响应:\", res);\n                      console.log(\"🔍 响应code:\", res.code, \"类型:\", _typeof(res.code));\n\n                      if (!(res.code === 200 || res.code === '200')) {\n                        _context.next = 25;\n                        break;\n                      }\n\n                      _this3.$notification.success({\n                        message: '登录成功',\n                        description: '欢迎回来！正在跳转到个人中心...',\n                        placement: 'topRight',\n                        duration: 3,\n                        style: {\n                          width: '350px',\n                          marginTop: '101px',\n                          borderRadius: '8px',\n                          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                        }\n                      }); // ✅ 存储登录信息\n\n\n                      result = res.result;\n                      userInfo = result.userInfo;\n                      Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000);\n                      Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000);\n                      Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000);\n                      Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000); // ✅ 获取用户角色信息\n\n                      _context.prev = 11;\n                      _context.next = 14;\n                      return getAction(\"/sys/user/getCurrentUserDeparts\");\n\n                    case 14:\n                      roleRes = _context.sent;\n\n                      if (roleRes.success) {\n                        userRole = roleRes.result.role;\n                        departId = roleRes.result.departId; // 存储角色信息\n\n                        localStorage.setItem('userRole', userRole || '');\n                        localStorage.setItem('departId', departId || ''); // 优先处理重定向参数\n\n                        redirectPath = _this3.$route.query.redirect;\n                        console.log('🔍 登录成功，检查重定向参数:', redirectPath);\n\n                        if (redirectPath) {\n                          // 有重定向参数，直接跳转到目标页面\n                          console.log('🔄 有重定向参数，跳转到:', redirectPath);\n\n                          _this3.$router.push(redirectPath);\n                        } else {\n                          // 没有重定向参数，根据角色决定跳转\n                          if (_this3.isAdminRole(userRole)) {\n                            // 管理员用户，跳转到后台\n                            console.log('🔄 管理员用户，跳转到后台管理');\n\n                            _this3.$router.push('/dashboard/analysis');\n                          } else {\n                            // 普通用户，跳转到个人中心\n                            console.log('🔄 普通用户，跳转到个人中心');\n\n                            _this3.$router.push('/usercenter');\n                          }\n                        }\n                      } else {\n                        // 获取角色失败，检查重定向参数\n                        _redirectPath = _this3.$route.query.redirect;\n\n                        if (_redirectPath) {\n                          _this3.$router.push(_redirectPath);\n                        } else {\n                          _this3.$router.push('/usercenter');\n                        }\n                      }\n\n                      _context.next = 23;\n                      break;\n\n                    case 18:\n                      _context.prev = 18;\n                      _context.t0 = _context[\"catch\"](11);\n                      console.error('获取角色信息失败:', _context.t0); // 出错时也检查重定向参数\n\n                      _redirectPath2 = _this3.$route.query.redirect;\n\n                      if (_redirectPath2) {\n                        _this3.$router.push(_redirectPath2);\n                      } else {\n                        _this3.$router.push('/usercenter');\n                      }\n\n                    case 23:\n                      _context.next = 27;\n                      break;\n\n                    case 25:\n                      _this3.$notification.error({\n                        message: '登录失败',\n                        description: res.message || '用户名或密码错误，请检查后重试',\n                        placement: 'topRight',\n                        duration: 4,\n                        style: {\n                          width: '380px',\n                          marginTop: '101px',\n                          borderRadius: '8px',\n                          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                        }\n                      });\n\n                      _this3.handleChangeCheckCode(); // 刷新验证码\n\n\n                    case 27:\n                    case \"end\":\n                      return _context.stop();\n                  }\n                }\n              }, _callee, null, [[11, 18]]);\n            }));\n\n            return function (_x) {\n              return _ref.apply(this, arguments);\n            };\n          }()).catch( /*#__PURE__*/function () {\n            var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3(err) {\n              var conflictInfo, forceLoginFn, forceLoginResponse, result, userInfo, redirectPath;\n              return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n                while (1) {\n                  switch (_context3.prev = _context3.next) {\n                    case 0:\n                      _this3.loginLoading = false; // 检查是否是登录冲突错误\n\n                      if (!(err.response && err.response.data && err.response.data.code === 4002)) {\n                        _context3.next = 36;\n                        break;\n                      }\n\n                      console.log('检测到用户名密码登录冲突，显示确认弹窗');\n                      conflictInfo = err.response.data.result; // 创建强制登录函数\n\n                      forceLoginFn = /*#__PURE__*/function () {\n                        var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n                          var forceLoginParams;\n                          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n                            while (1) {\n                              switch (_context2.prev = _context2.next) {\n                                case 0:\n                                  forceLoginParams = _objectSpread(_objectSpread({}, loginParams), {}, {\n                                    loginType: 'force' // 修改登录类型为强制登录\n\n                                  });\n                                  console.log('用户名密码强制登录数据:', forceLoginParams);\n                                  _context2.next = 4;\n                                  return login(forceLoginParams);\n\n                                case 4:\n                                  return _context2.abrupt(\"return\", _context2.sent);\n\n                                case 5:\n                                case \"end\":\n                                  return _context2.stop();\n                              }\n                            }\n                          }, _callee2);\n                        }));\n\n                        return function forceLoginFn() {\n                          return _ref3.apply(this, arguments);\n                        };\n                      }();\n\n                      _context3.prev = 5;\n                      _context3.next = 8;\n                      return handleLoginConflict(conflictInfo, forceLoginFn);\n\n                    case 8:\n                      forceLoginResponse = _context3.sent;\n\n                      if (!(forceLoginResponse && (forceLoginResponse.code === 200 || forceLoginResponse.code === '200'))) {\n                        _context3.next = 21;\n                        break;\n                      }\n\n                      // 强制登录成功，执行登录成功的逻辑\n                      _this3.$notification.success({\n                        message: '登录成功',\n                        description: '欢迎回来！正在跳转到个人中心...',\n                        placement: 'topRight',\n                        duration: 3\n                      }); // 存储登录信息\n\n\n                      result = forceLoginResponse.result;\n                      userInfo = result.userInfo;\n                      Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000);\n                      Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000);\n                      Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000);\n                      Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000); // 跳转逻辑\n\n                      redirectPath = _this3.$route.query.redirect;\n\n                      if (redirectPath) {\n                        _this3.$router.push(redirectPath);\n                      } else {\n                        _this3.$router.push('/usercenter');\n                      }\n\n                      _context3.next = 22;\n                      break;\n\n                    case 21:\n                      throw new Error(forceLoginResponse && forceLoginResponse.message || '强制登录失败');\n\n                    case 22:\n                      _context3.next = 34;\n                      break;\n\n                    case 24:\n                      _context3.prev = 24;\n                      _context3.t0 = _context3[\"catch\"](5);\n\n                      if (!(_context3.t0.message === 'USER_CANCELLED')) {\n                        _context3.next = 32;\n                        break;\n                      }\n\n                      // 用户取消登录\n                      console.log('用户取消用户名密码强制登录');\n\n                      _this3.handleChangeCheckCode(); // 刷新验证码\n\n\n                      return _context3.abrupt(\"return\");\n\n                    case 32:\n                      _this3.$notification.error({\n                        message: '登录失败',\n                        description: _context3.t0.message || '强制登录失败',\n                        placement: 'topRight',\n                        duration: 4\n                      });\n\n                      _this3.handleChangeCheckCode(); // 刷新验证码\n\n\n                    case 34:\n                      _context3.next = 38;\n                      break;\n\n                    case 36:\n                      // 其他错误，显示原有的错误处理\n                      _this3.$notification.error({\n                        message: '登录失败',\n                        description: err.message || '网络连接异常，请检查网络后重试',\n                        placement: 'topRight',\n                        duration: 4,\n                        style: {\n                          width: '380px',\n                          marginTop: '101px',\n                          borderRadius: '8px',\n                          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                        }\n                      });\n\n                      _this3.handleChangeCheckCode(); // 刷新验证码\n\n\n                    case 38:\n                    case \"end\":\n                      return _context3.stop();\n                  }\n                }\n              }, _callee3, null, [[5, 24]]);\n            }));\n\n            return function (_x2) {\n              return _ref2.apply(this, arguments);\n            };\n          }());\n        }\n      });\n    },\n    handleForgotPassword: function handleForgotPassword() {\n      this.$notification.info({\n        message: '忘记密码',\n        description: '忘记密码功能正在开发中，敬请期待...',\n        placement: 'topRight',\n        duration: 3,\n        style: {\n          width: '350px',\n          marginTop: '101px',\n          borderRadius: '8px',\n          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n        }\n      }); // TODO: 跳转到忘记密码页面\n    },\n    handleSocialLogin: function handleSocialLogin(type) {\n      var typeMap = {\n        wechat: '微信',\n        qq: 'QQ',\n        alipay: '支付宝'\n      };\n      this.$message.info(\"\".concat(typeMap[type], \"\\u767B\\u5F55\\u529F\\u80FD\\u5F00\\u53D1\\u4E2D...\")); // TODO: 实现第三方登录\n    },\n    // 检查URL中的邀请码（静默处理）\n    checkInviteCode: function checkInviteCode() {\n      // 支持两种参数格式：ref（推广链接）和 invite（邀请码）\n      var refCode = this.$route.query.ref;\n      var inviteCode = this.$route.query.invite;\n      var finalInviteCode = refCode || inviteCode;\n\n      if (finalInviteCode) {\n        this.inviteCodeFromUrl = finalInviteCode; // 静默记录邀请码，不显示给用户\n\n        console.log('检测到推荐码:', finalInviteCode, '来源:', refCode ? 'ref参数' : 'invite参数');\n      }\n    },\n    // 登录方式切换\n    switchLoginType: function switchLoginType(type) {\n      this.loginType = type; // 重置验证码倒计时\n\n      this.smsCountdown = 0;\n      this.emailCountdown = 0;\n\n      if (type === 'wechat') {\n        this.generateWechatLoginQrCode();\n      }\n    },\n    // 手机号登录（自动注册）\n    handlePhoneLogin: function handlePhoneLogin(e) {\n      var _this4 = this;\n\n      e.preventDefault();\n      this.phoneLoginForm.validateFields( /*#__PURE__*/function () {\n        var _ref4 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4(err, values) {\n          var checkResponse;\n          return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n            while (1) {\n              switch (_context4.prev = _context4.next) {\n                case 0:\n                  if (err) {\n                    _context4.next = 19;\n                    break;\n                  }\n\n                  _this4.phoneLoginLoading = true;\n                  _context4.prev = 2;\n                  _context4.next = 5;\n                  return checkUsername(values.phone, 'phone');\n\n                case 5:\n                  checkResponse = _context4.sent;\n\n                  if (!checkResponse.success) {\n                    _context4.next = 11;\n                    break;\n                  }\n\n                  _context4.next = 9;\n                  return _this4.autoRegisterAndLogin('phone', values);\n\n                case 9:\n                  _context4.next = 13;\n                  break;\n\n                case 11:\n                  _context4.next = 13;\n                  return _this4.loginWithSmsCode(values);\n\n                case 13:\n                  _context4.next = 19;\n                  break;\n\n                case 15:\n                  _context4.prev = 15;\n                  _context4.t0 = _context4[\"catch\"](2);\n                  _this4.phoneLoginLoading = false;\n\n                  _this4.$notification.error({\n                    message: '登录失败',\n                    description: _context4.t0.message || '登录过程中发生错误',\n                    placement: 'topRight',\n                    duration: 4\n                  });\n\n                case 19:\n                case \"end\":\n                  return _context4.stop();\n              }\n            }\n          }, _callee4, null, [[2, 15]]);\n        }));\n\n        return function (_x3, _x4) {\n          return _ref4.apply(this, arguments);\n        };\n      }());\n    },\n    // 邮箱登录（自动注册）\n    handleEmailLogin: function handleEmailLogin(e) {\n      var _this5 = this;\n\n      e.preventDefault();\n      this.emailLoginForm.validateFields( /*#__PURE__*/function () {\n        var _ref5 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5(err, values) {\n          var checkResponse;\n          return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n            while (1) {\n              switch (_context5.prev = _context5.next) {\n                case 0:\n                  if (err) {\n                    _context5.next = 19;\n                    break;\n                  }\n\n                  _this5.emailLoginLoading = true;\n                  _context5.prev = 2;\n                  _context5.next = 5;\n                  return checkUsername(values.email, 'email');\n\n                case 5:\n                  checkResponse = _context5.sent;\n\n                  if (!checkResponse.success) {\n                    _context5.next = 11;\n                    break;\n                  }\n\n                  _context5.next = 9;\n                  return _this5.autoRegisterAndLogin('email', values);\n\n                case 9:\n                  _context5.next = 13;\n                  break;\n\n                case 11:\n                  _context5.next = 13;\n                  return _this5.loginWithEmailCode(values);\n\n                case 13:\n                  _context5.next = 19;\n                  break;\n\n                case 15:\n                  _context5.prev = 15;\n                  _context5.t0 = _context5[\"catch\"](2);\n                  _this5.emailLoginLoading = false;\n\n                  _this5.$notification.error({\n                    message: '登录失败',\n                    description: _context5.t0.message || '登录过程中发生错误',\n                    placement: 'topRight',\n                    duration: 4\n                  });\n\n                case 19:\n                case \"end\":\n                  return _context5.stop();\n              }\n            }\n          }, _callee5, null, [[2, 15]]);\n        }));\n\n        return function (_x5, _x6) {\n          return _ref5.apply(this, arguments);\n        };\n      }());\n    },\n    // 自动注册并登录（无密码账户）\n    autoRegisterAndLogin: function () {\n      var _autoRegisterAndLogin = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6(type, values) {\n        var _registerData, randomPassword, registerData, registerResponse;\n\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.prev = 0;\n                // 为无密码账户生成符合要求的随机密码\n                randomPassword = this.generateSecurePassword(); // 构建注册数据\n\n                registerData = (_registerData = {\n                  type: type\n                }, _defineProperty(_registerData, type, values[type]), _defineProperty(_registerData, \"verifyCode\", values[type === 'phone' ? 'smsCode' : 'emailCode']), _defineProperty(_registerData, \"password\", randomPassword), _defineProperty(_registerData, \"confirmPassword\", randomPassword), _defineProperty(_registerData, \"inviteCode\", this.inviteCodeFromUrl), _defineProperty(_registerData, \"inviteSource\", this.inviteCodeFromUrl ? 'link' : null), _registerData);\n                console.log('自动注册数据:', registerData); // 调用注册接口\n\n                _context6.next = 6;\n                return register(registerData);\n\n              case 6:\n                registerResponse = _context6.sent;\n\n                if (!registerResponse.success) {\n                  _context6.next = 13;\n                  break;\n                }\n\n                // 注册成功，现在需要自动登录获取token\n                console.log('注册成功，用户ID:', registerResponse.result); // 使用生成的密码进行自动登录\n\n                _context6.next = 11;\n                return this.performAutoLogin(type, values, randomPassword);\n\n              case 11:\n                _context6.next = 14;\n                break;\n\n              case 13:\n                throw new Error(registerResponse.message || '注册失败');\n\n              case 14:\n                _context6.next = 19;\n                break;\n\n              case 16:\n                _context6.prev = 16;\n                _context6.t0 = _context6[\"catch\"](0);\n                throw _context6.t0;\n\n              case 19:\n                _context6.prev = 19;\n                this.phoneLoginLoading = false;\n                this.emailLoginLoading = false;\n                return _context6.finish(19);\n\n              case 23:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[0, 16, 19, 23]]);\n      }));\n\n      function autoRegisterAndLogin(_x7, _x8) {\n        return _autoRegisterAndLogin.apply(this, arguments);\n      }\n\n      return autoRegisterAndLogin;\n    }(),\n    // 使用短信验证码登录\n    loginWithSmsCode: function () {\n      var _loginWithSmsCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee8(values) {\n        var loginData, loginResponse, conflictInfo, forceLoginFn, forceLoginResponse;\n        return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n          while (1) {\n            switch (_context8.prev = _context8.next) {\n              case 0:\n                _context8.prev = 0;\n                // 构建登录数据\n                loginData = {\n                  mobile: values.phone,\n                  captcha: values.smsCode,\n                  loginType: 'website' // 标识为官网用户登录\n\n                };\n                console.log('短信验证码登录:', loginData); // 调用短信验证码登录接口\n\n                _context8.next = 5;\n                return phoneLogin(loginData);\n\n              case 5:\n                loginResponse = _context8.sent;\n\n                if (!loginResponse.success) {\n                  _context8.next = 11;\n                  break;\n                }\n\n                _context8.next = 9;\n                return this.handleLoginSuccess(loginResponse.result);\n\n              case 9:\n                _context8.next = 38;\n                break;\n\n              case 11:\n                if (!(loginResponse.code === 4002)) {\n                  _context8.next = 37;\n                  break;\n                }\n\n                console.log('检测到手机号登录冲突，显示确认弹窗');\n                conflictInfo = loginResponse.result; // 创建强制登录函数\n\n                forceLoginFn = /*#__PURE__*/function () {\n                  var _ref6 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7() {\n                    var forceLoginData;\n                    return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n                      while (1) {\n                        switch (_context7.prev = _context7.next) {\n                          case 0:\n                            forceLoginData = _objectSpread(_objectSpread({}, loginData), {}, {\n                              loginType: 'force' // 修改登录类型为强制登录\n\n                            });\n                            console.log('手机号强制登录数据:', forceLoginData);\n                            _context7.next = 4;\n                            return phoneLogin(forceLoginData);\n\n                          case 4:\n                            return _context7.abrupt(\"return\", _context7.sent);\n\n                          case 5:\n                          case \"end\":\n                            return _context7.stop();\n                        }\n                      }\n                    }, _callee7);\n                  }));\n\n                  return function forceLoginFn() {\n                    return _ref6.apply(this, arguments);\n                  };\n                }();\n\n                _context8.prev = 15;\n                _context8.next = 18;\n                return handleLoginConflict(conflictInfo, forceLoginFn);\n\n              case 18:\n                forceLoginResponse = _context8.sent;\n\n                if (!(forceLoginResponse && forceLoginResponse.success)) {\n                  _context8.next = 24;\n                  break;\n                }\n\n                _context8.next = 22;\n                return this.handleLoginSuccess(forceLoginResponse.result);\n\n              case 22:\n                _context8.next = 25;\n                break;\n\n              case 24:\n                throw new Error(forceLoginResponse && forceLoginResponse.message || '强制登录失败');\n\n              case 25:\n                _context8.next = 35;\n                break;\n\n              case 27:\n                _context8.prev = 27;\n                _context8.t0 = _context8[\"catch\"](15);\n\n                if (!(_context8.t0.message === 'USER_CANCELLED')) {\n                  _context8.next = 34;\n                  break;\n                }\n\n                // 用户取消登录\n                console.log('用户取消手机号强制登录');\n                return _context8.abrupt(\"return\");\n\n              case 34:\n                throw _context8.t0;\n\n              case 35:\n                _context8.next = 38;\n                break;\n\n              case 37:\n                throw new Error(loginResponse.message || '登录失败');\n\n              case 38:\n                _context8.next = 43;\n                break;\n\n              case 40:\n                _context8.prev = 40;\n                _context8.t1 = _context8[\"catch\"](0);\n                throw _context8.t1;\n\n              case 43:\n                _context8.prev = 43;\n                this.phoneLoginLoading = false;\n                return _context8.finish(43);\n\n              case 46:\n              case \"end\":\n                return _context8.stop();\n            }\n          }\n        }, _callee8, this, [[0, 40, 43, 46], [15, 27]]);\n      }));\n\n      function loginWithSmsCode(_x9) {\n        return _loginWithSmsCode.apply(this, arguments);\n      }\n\n      return loginWithSmsCode;\n    }(),\n    // 使用邮箱验证码登录\n    loginWithEmailCode: function () {\n      var _loginWithEmailCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee10(values) {\n        var loginData, loginResponse, conflictInfo, forceLoginFn, forceLoginResponse;\n        return _regeneratorRuntime.wrap(function _callee10$(_context10) {\n          while (1) {\n            switch (_context10.prev = _context10.next) {\n              case 0:\n                _context10.prev = 0;\n                // 构建登录数据\n                loginData = {\n                  email: values.email,\n                  emailCode: values.emailCode,\n                  loginType: 'website' // 标识为官网用户登录\n\n                };\n                console.log('邮箱验证码登录:', loginData); // 调用邮箱验证码登录接口\n\n                _context10.next = 5;\n                return emailLogin(loginData);\n\n              case 5:\n                loginResponse = _context10.sent;\n\n                if (!loginResponse.success) {\n                  _context10.next = 11;\n                  break;\n                }\n\n                _context10.next = 9;\n                return this.handleLoginSuccess(loginResponse.result);\n\n              case 9:\n                _context10.next = 38;\n                break;\n\n              case 11:\n                if (!(loginResponse.code === 4002)) {\n                  _context10.next = 37;\n                  break;\n                }\n\n                console.log('检测到邮箱登录冲突，显示确认弹窗');\n                conflictInfo = loginResponse.result; // 创建强制登录函数\n\n                forceLoginFn = /*#__PURE__*/function () {\n                  var _ref7 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee9() {\n                    var forceLoginData;\n                    return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n                      while (1) {\n                        switch (_context9.prev = _context9.next) {\n                          case 0:\n                            forceLoginData = _objectSpread(_objectSpread({}, loginData), {}, {\n                              loginType: 'force' // 修改登录类型为强制登录\n\n                            });\n                            console.log('邮箱强制登录数据:', forceLoginData);\n                            _context9.next = 4;\n                            return emailLogin(forceLoginData);\n\n                          case 4:\n                            return _context9.abrupt(\"return\", _context9.sent);\n\n                          case 5:\n                          case \"end\":\n                            return _context9.stop();\n                        }\n                      }\n                    }, _callee9);\n                  }));\n\n                  return function forceLoginFn() {\n                    return _ref7.apply(this, arguments);\n                  };\n                }();\n\n                _context10.prev = 15;\n                _context10.next = 18;\n                return handleLoginConflict(conflictInfo, forceLoginFn);\n\n              case 18:\n                forceLoginResponse = _context10.sent;\n\n                if (!(forceLoginResponse && forceLoginResponse.success)) {\n                  _context10.next = 24;\n                  break;\n                }\n\n                _context10.next = 22;\n                return this.handleLoginSuccess(forceLoginResponse.result);\n\n              case 22:\n                _context10.next = 25;\n                break;\n\n              case 24:\n                throw new Error(forceLoginResponse && forceLoginResponse.message || '强制登录失败');\n\n              case 25:\n                _context10.next = 35;\n                break;\n\n              case 27:\n                _context10.prev = 27;\n                _context10.t0 = _context10[\"catch\"](15);\n\n                if (!(_context10.t0.message === 'USER_CANCELLED')) {\n                  _context10.next = 34;\n                  break;\n                }\n\n                // 用户取消登录\n                console.log('用户取消邮箱强制登录');\n                return _context10.abrupt(\"return\");\n\n              case 34:\n                throw _context10.t0;\n\n              case 35:\n                _context10.next = 38;\n                break;\n\n              case 37:\n                throw new Error(loginResponse.message || '登录失败');\n\n              case 38:\n                _context10.next = 43;\n                break;\n\n              case 40:\n                _context10.prev = 40;\n                _context10.t1 = _context10[\"catch\"](0);\n                throw _context10.t1;\n\n              case 43:\n                _context10.prev = 43;\n                this.emailLoginLoading = false;\n                return _context10.finish(43);\n\n              case 46:\n              case \"end\":\n                return _context10.stop();\n            }\n          }\n        }, _callee10, this, [[0, 40, 43, 46], [15, 27]]);\n      }));\n\n      function loginWithEmailCode(_x10) {\n        return _loginWithEmailCode.apply(this, arguments);\n      }\n\n      return loginWithEmailCode;\n    }(),\n    // 处理登录成功\n    handleLoginSuccess: function () {\n      var _handleLoginSuccess = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee11(result) {\n        var redirect;\n        return _regeneratorRuntime.wrap(function _callee11$(_context11) {\n          while (1) {\n            switch (_context11.prev = _context11.next) {\n              case 0:\n                _context11.prev = 0;\n                // 存储token和用户信息\n                Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000);\n                Vue.ls.set(USER_NAME, result.userInfo.username, 7 * 24 * 60 * 60 * 1000);\n                Vue.ls.set(USER_INFO, result.userInfo, 7 * 24 * 60 * 60 * 1000);\n                Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000); // 显示登录成功消息\n\n                this.$notification.success({\n                  message: '登录成功',\n                  description: \"\\u6B22\\u8FCE\\u56DE\\u6765\\uFF0C\".concat(result.userInfo.realname || result.userInfo.username, \"\\uFF01\"),\n                  placement: 'topRight',\n                  duration: 3\n                }); // 跳转到目标页面\n\n                redirect = this.$route.query.redirect || '/';\n                this.$router.push(redirect);\n                _context11.next = 14;\n                break;\n\n              case 10:\n                _context11.prev = 10;\n                _context11.t0 = _context11[\"catch\"](0);\n                console.error('处理登录成功失败:', _context11.t0);\n                throw new Error('登录后处理失败');\n\n              case 14:\n              case \"end\":\n                return _context11.stop();\n            }\n          }\n        }, _callee11, this, [[0, 10]]);\n      }));\n\n      function handleLoginSuccess(_x11) {\n        return _handleLoginSuccess.apply(this, arguments);\n      }\n\n      return handleLoginSuccess;\n    }(),\n    // 发送登录短信验证码\n    sendLoginSmsCode: function () {\n      var _sendLoginSmsCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee12() {\n        var phone, response;\n        return _regeneratorRuntime.wrap(function _callee12$(_context12) {\n          while (1) {\n            switch (_context12.prev = _context12.next) {\n              case 0:\n                phone = this.phoneLoginForm.getFieldValue('phone');\n\n                if (phone) {\n                  _context12.next = 4;\n                  break;\n                }\n\n                this.$message.error('请先输入手机号');\n                return _context12.abrupt(\"return\");\n\n              case 4:\n                if (/^1[3-9]\\d{9}$/.test(phone)) {\n                  _context12.next = 7;\n                  break;\n                }\n\n                this.$message.error('手机号格式不正确');\n                return _context12.abrupt(\"return\");\n\n              case 7:\n                this.smsCodeSending = true;\n                _context12.prev = 8;\n                _context12.next = 11;\n                return sendSmsCode(phone, 'register');\n\n              case 11:\n                response = _context12.sent;\n\n                if (response.success) {\n                  this.$message.success('验证码发送成功，请查收短信');\n                  this.startSmsCountdown();\n                } else {\n                  this.$message.error(response.message || '验证码发送失败');\n                }\n\n                _context12.next = 19;\n                break;\n\n              case 15:\n                _context12.prev = 15;\n                _context12.t0 = _context12[\"catch\"](8);\n                console.error('发送短信验证码失败:', _context12.t0);\n                this.$message.error('验证码发送失败，请稍后重试');\n\n              case 19:\n                _context12.prev = 19;\n                this.smsCodeSending = false;\n                return _context12.finish(19);\n\n              case 22:\n              case \"end\":\n                return _context12.stop();\n            }\n          }\n        }, _callee12, this, [[8, 15, 19, 22]]);\n      }));\n\n      function sendLoginSmsCode() {\n        return _sendLoginSmsCode.apply(this, arguments);\n      }\n\n      return sendLoginSmsCode;\n    }(),\n    // 发送登录邮箱验证码\n    sendLoginEmailCode: function () {\n      var _sendLoginEmailCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee13() {\n        var email, response;\n        return _regeneratorRuntime.wrap(function _callee13$(_context13) {\n          while (1) {\n            switch (_context13.prev = _context13.next) {\n              case 0:\n                email = this.emailLoginForm.getFieldValue('email');\n\n                if (email) {\n                  _context13.next = 4;\n                  break;\n                }\n\n                this.$message.error('请先输入邮箱');\n                return _context13.abrupt(\"return\");\n\n              case 4:\n                if (/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n                  _context13.next = 7;\n                  break;\n                }\n\n                this.$message.error('邮箱格式不正确');\n                return _context13.abrupt(\"return\");\n\n              case 7:\n                this.emailCodeSending = true;\n                _context13.prev = 8;\n                _context13.next = 11;\n                return sendEmailCode(email, 'register');\n\n              case 11:\n                response = _context13.sent;\n\n                if (response.success) {\n                  this.$message.success('验证码发送成功，请查收邮件');\n                  this.startEmailCountdown();\n                } else {\n                  this.$message.error(response.message || '验证码发送失败');\n                }\n\n                _context13.next = 19;\n                break;\n\n              case 15:\n                _context13.prev = 15;\n                _context13.t0 = _context13[\"catch\"](8);\n                console.error('发送邮箱验证码失败:', _context13.t0);\n                this.$message.error('验证码发送失败，请稍后重试');\n\n              case 19:\n                _context13.prev = 19;\n                this.emailCodeSending = false;\n                return _context13.finish(19);\n\n              case 22:\n              case \"end\":\n                return _context13.stop();\n            }\n          }\n        }, _callee13, this, [[8, 15, 19, 22]]);\n      }));\n\n      function sendLoginEmailCode() {\n        return _sendLoginEmailCode.apply(this, arguments);\n      }\n\n      return sendLoginEmailCode;\n    }(),\n    // 短信验证码倒计时\n    startSmsCountdown: function startSmsCountdown() {\n      var _this6 = this;\n\n      this.smsCountdown = 60;\n      var timer = setInterval(function () {\n        _this6.smsCountdown--;\n\n        if (_this6.smsCountdown <= 0) {\n          clearInterval(timer);\n        }\n      }, 1000);\n    },\n    // 邮箱验证码倒计时\n    startEmailCountdown: function startEmailCountdown() {\n      var _this7 = this;\n\n      this.emailCountdown = 60;\n      var timer = setInterval(function () {\n        _this7.emailCountdown--;\n\n        if (_this7.emailCountdown <= 0) {\n          clearInterval(timer);\n        }\n      }, 1000);\n    },\n    // 生成微信登录二维码\n    generateWechatLoginQrCode: function () {\n      var _generateWechatLoginQrCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee14() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee14$(_context14) {\n          while (1) {\n            switch (_context14.prev = _context14.next) {\n              case 0:\n                _context14.prev = 0;\n                _context14.next = 3;\n                return generateWechatQrCode('login', this.inviteCodeFromUrl);\n\n              case 3:\n                response = _context14.sent;\n\n                if (response.success) {\n                  this.wechatLoginQrCode = response.result.qrCodeUrl;\n                } else {\n                  this.$message.error('生成微信二维码失败');\n                }\n\n                _context14.next = 11;\n                break;\n\n              case 7:\n                _context14.prev = 7;\n                _context14.t0 = _context14[\"catch\"](0);\n                console.error('生成微信二维码失败:', _context14.t0);\n                this.$message.error('生成微信二维码失败');\n\n              case 11:\n              case \"end\":\n                return _context14.stop();\n            }\n          }\n        }, _callee14, this, [[0, 7]]);\n      }));\n\n      function generateWechatLoginQrCode() {\n        return _generateWechatLoginQrCode.apply(this, arguments);\n      }\n\n      return generateWechatLoginQrCode;\n    }(),\n    // 生成符合要求的安全密码（至少8位，包含字母和数字）\n    generateSecurePassword: function generateSecurePassword() {\n      var letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';\n      var numbers = '0123456789';\n      var allChars = letters + numbers;\n      var password = ''; // 确保至少包含一个字母和一个数字\n\n      password += letters.charAt(Math.floor(Math.random() * letters.length));\n      password += numbers.charAt(Math.floor(Math.random() * numbers.length)); // 生成剩余的6位字符\n\n      for (var i = 0; i < 10; i++) {\n        password += allChars.charAt(Math.floor(Math.random() * allChars.length));\n      } // 打乱字符顺序\n\n\n      return password.split('').sort(function () {\n        return Math.random() - 0.5;\n      }).join('');\n    },\n    // 注册成功后自动登录\n    performAutoLogin: function () {\n      var _performAutoLogin = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee15(type, values, password) {\n        var _this8 = this;\n\n        var username, user, pwd, loginParams, loginResponse, result, userInfo;\n        return _regeneratorRuntime.wrap(function _callee15$(_context15) {\n          while (1) {\n            switch (_context15.prev = _context15.next) {\n              case 0:\n                _context15.prev = 0;\n                // 先获取验证码图片\n                this.handleChangeCheckCode(); // 构建登录参数 - 完全按照正常登录的格式\n\n                username = values[type]; // phone 或 email\n\n                user = encryption(username, this.encryptedString.key, this.encryptedString.iv);\n                pwd = encryption(password, this.encryptedString.key, this.encryptedString.iv);\n                loginParams = {\n                  username: user,\n                  password: pwd,\n                  captcha: 'AUTO_LOGIN_2025',\n                  // 使用特殊验证码绕过验证\n                  checkKey: this.currdatetime,\n                  remember_me: true,\n                  loginType: 'website'\n                };\n                console.log('自动登录参数:', {\n                  username: username,\n                  loginType: 'auto',\n                  checkKey: this.currdatetime\n                });\n                _context15.next = 9;\n                return login(loginParams);\n\n              case 9:\n                loginResponse = _context15.sent;\n\n                if (!(loginResponse.code === 200 || loginResponse.code === '200')) {\n                  _context15.next = 21;\n                  break;\n                }\n\n                // 登录成功提示\n                this.$notification.success({\n                  message: '欢迎加入智界AIGC！',\n                  description: \"\\u60A8\\u5DF2\\u6210\\u529F\\u6CE8\\u518C\\u5E76\\u767B\\u5F55\\uFF0C\\u8D26\\u6237\\u5DF2\\u521B\\u5EFA\\u4E3A\\u65E0\\u5BC6\\u7801\\u6A21\\u5F0F\\uFF0C\\u4ECA\\u540E\\u53EF\\u76F4\\u63A5\\u4F7F\\u7528\".concat(type === 'phone' ? '手机号' : '邮箱', \"\\u9A8C\\u8BC1\\u7801\\u767B\\u5F55\\uFF01\"),\n                  placement: 'topRight',\n                  duration: 6\n                }); // 存储登录信息\n\n                result = loginResponse.result;\n                userInfo = result.userInfo;\n                Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000);\n                Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000);\n                Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000);\n                Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000); // 延迟跳转\n\n                setTimeout(function () {\n                  _this8.$router.push('/usercenter');\n                }, 1500);\n                _context15.next = 22;\n                break;\n\n              case 21:\n                throw new Error(loginResponse.message || '自动登录失败');\n\n              case 22:\n                _context15.next = 28;\n                break;\n\n              case 24:\n                _context15.prev = 24;\n                _context15.t0 = _context15[\"catch\"](0);\n                console.error('自动登录失败:', _context15.t0);\n                this.$notification.error({\n                  message: '注册成功，但自动登录失败',\n                  description: '请手动使用验证码登录',\n                  placement: 'topRight',\n                  duration: 4\n                });\n\n              case 28:\n              case \"end\":\n                return _context15.stop();\n            }\n          }\n        }, _callee15, this, [[0, 24]]);\n      }));\n\n      function performAutoLogin(_x12, _x13, _x14) {\n        return _performAutoLogin.apply(this, arguments);\n      }\n\n      return performAutoLogin;\n    }(),\n    // 初始化页面动画\n    initAnimations: function initAnimations() {\n      // ✅ 创建主时间线，确保动画流畅连贯\n      var tl = gsap.timeline(); // ✅ 左侧信息区域动画 - 从初始状态开始\n\n      tl.to(this.$refs.loginInfo, {\n        duration: 0.8,\n        x: 0,\n        opacity: 1,\n        ease: \"power3.out\"\n      }); // ✅ 右侧登录表单动画 - 与左侧稍微重叠\n\n      tl.to(this.$refs.loginContainer, {\n        duration: 0.8,\n        x: 0,\n        opacity: 1,\n        ease: \"power3.out\"\n      }, \"-=0.6\"); // 提前0.6秒开始，创造重叠效果\n      // ✅ 特性列表依次出现 - 更流畅的时序\n\n      tl.to(\".feature-item\", {\n        duration: 0.5,\n        y: 0,\n        opacity: 1,\n        stagger: 0.08,\n        // 减少间隔，更流畅\n        ease: \"power2.out\"\n      }, \"-=0.4\"); // 与右侧动画重叠\n    }\n  }\n};", {"version": 3, "sources": ["Login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0VA,SAAA,KAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,QAAA,aAAA;AACA,SAAA,UAAA,EAAA,kBAAA,QAAA,+BAAA;AACA,SAAA,SAAA,QAAA,cAAA;AACA,SAAA,IAAA,QAAA,MAAA;AACA,OAAA,aAAA,MAAA,wCAAA;AACA,OAAA,SAAA,MAAA,mCAAA;AACA,SAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAAA,qBAAA,QAAA,wBAAA;AACA,SACA,aADA,EAEA,WAFA,EAGA,aAHA,EAIA,QAJA,EAKA,oBALA,QAMA,gBANA;AAOA,OAAA,GAAA,MAAA,KAAA;AACA,SAAA,mBAAA,QAAA,8BAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,cADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,aAAA,EAAA,aADA;AAEA,IAAA,SAAA,EAAA;AAFA,GAFA;AAMA,EAAA,IANA,kBAMA;AACA,WAAA;AACA;AACA,MAAA,IAAA,EAAA,KAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CAFA;AAGA,MAAA,cAAA,EAAA,KAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CAHA;AAIA,MAAA,cAAA,EAAA,KAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CAJA;AAKA,MAAA,YAAA,EAAA,KALA;AAMA,MAAA,iBAAA,EAAA,KANA;AAOA,MAAA,iBAAA,EAAA,KAPA;AAQA,MAAA,UAAA,EAAA,KARA;AASA,MAAA,aAAA,EAAA,EATA;AAUA,MAAA,YAAA,EAAA,IAAA,IAAA,GAAA,OAAA,EAVA;AAWA,MAAA,eAAA,EAAA,EAXA;AAaA;AACA,MAAA,SAAA,EAAA,OAdA;AAcA;AAEA;AACA,MAAA,cAAA,EAAA,KAjBA;AAkBA,MAAA,YAAA,EAAA,CAlBA;AAmBA,MAAA,gBAAA,EAAA,KAnBA;AAoBA,MAAA,cAAA,EAAA,CApBA;AAsBA;AACA,MAAA,iBAAA,EAAA,EAvBA;AAyBA;AACA,MAAA,iBAAA,EAAA,EA1BA;AA4BA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OADA,EAMA;AACA,QAAA,IAAA,EAAA,aADA;AAEA,QAAA,KAAA,EAAA,MAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OANA,EAWA;AACA,QAAA,IAAA,EAAA,oBADA;AAEA,QAAA,KAAA,EAAA,MAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OAXA,EAgBA;AACA,QAAA,IAAA,EAAA,QADA;AAEA,QAAA,KAAA,EAAA,MAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OAhBA;AA5BA,KAAA;AAmDA,GA1DA;AA2DA,EAAA,OA3DA,qBA2DA;AACA,SAAA,WAAA;AACA,SAAA,qBAAA;AACA,SAAA,cAAA;AACA,SAAA,eAAA;AACA,GAhEA;AAiEA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,WAFA,yBAEA;AAAA;;AACA,MAAA,kBAAA,GAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,KAAA,CAAA,eAAA,GAAA,IAAA;AACA,OAFA;AAGA,KANA;AAQA;AACA,IAAA,qBATA,mCASA;AAAA;;AACA,WAAA,YAAA,GAAA,IAAA,IAAA,GAAA,OAAA,EAAA;AACA,MAAA,SAAA,4BAAA,KAAA,YAAA,EAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,OAAA,EAAA;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,GAAA,CAAA,MAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,OAAA;AACA;AACA,OANA,EAMA,KANA,CAMA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,SAAA;AACA,OARA;AASA,KApBA;AAsBA,IAAA,YAtBA,wBAsBA,CAtBA,EAsBA;AAAA;;AACA,MAAA,CAAA,CAAA,cAAA;AACA,WAAA,IAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA,MAAA,EAAA;AACA,YAAA,CAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,IAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,MAAA,EAFA,CAIA;;AACA,cAAA,IAAA,GAAA,UAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,CAAA,eAAA,CAAA,GAAA,EAAA,MAAA,CAAA,eAAA,CAAA,EAAA,CAAA;AACA,cAAA,GAAA,GAAA,UAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,CAAA,eAAA,CAAA,GAAA,EAAA,MAAA,CAAA,eAAA,CAAA,EAAA,CAAA;AACA,cAAA,WAAA,GAAA;AACA,YAAA,QAAA,EAAA,IADA;AAEA,YAAA,QAAA,EAAA,GAFA;AAGA,YAAA,OAAA,EAAA,MAAA,CAAA,SAHA;AAIA,YAAA,QAAA,EAAA,MAAA,CAAA,YAJA;AAKA,YAAA,WAAA,EAAA,MAAA,CAAA,UALA;AAMA,YAAA,SAAA,EAAA,SANA,CAMA;;AANA,WAAA;AASA,UAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EAAA,WAAA;AACA,UAAA,KAAA,CAAA,WAAA,CAAA,CAAA,IAAA;AAAA,gFAAA,iBAAA,GAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,sBAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AACA,sBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,GAAA;AACA,sBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,GAAA,CAAA,IAAA,EAAA,KAAA,UAAA,GAAA,CAAA,IAAA;;AAHA,4BAIA,GAAA,CAAA,IAAA,KAAA,GAAA,IAAA,GAAA,CAAA,IAAA,KAAA,KAJA;AAAA;AAAA;AAAA;;AAKA,sBAAA,MAAA,CAAA,aAAA,CAAA,OAAA,CAAA;AACA,wBAAA,OAAA,EAAA,MADA;AAEA,wBAAA,WAAA,EAAA,mBAFA;AAGA,wBAAA,SAAA,EAAA,UAHA;AAIA,wBAAA,QAAA,EAAA,CAJA;AAKA,wBAAA,KAAA,EAAA;AACA,0BAAA,KAAA,EAAA,OADA;AAEA,0BAAA,SAAA,EAAA,OAFA;AAGA,0BAAA,YAAA,EAAA,KAHA;AAIA,0BAAA,SAAA,EAAA;AAJA;AALA,uBAAA,EALA,CAkBA;;;AACA,sBAAA,MAnBA,GAmBA,GAAA,CAAA,MAnBA;AAoBA,sBAAA,QApBA,GAoBA,MAAA,CAAA,QApBA;AAqBA,sBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,YAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,sBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,SAAA,EAAA,QAAA,CAAA,QAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,sBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,SAAA,EAAA,QAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,sBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,qBAAA,EAAA,MAAA,CAAA,eAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,EAxBA,CA0BA;;AA1BA;AAAA;AAAA,6BA4BA,SAAA,CAAA,iCAAA,CA5BA;;AAAA;AA4BA,sBAAA,OA5BA;;AA6BA,0BAAA,OAAA,CAAA,OAAA,EAAA;AACA,wBAAA,QADA,GACA,OAAA,CAAA,MAAA,CAAA,IADA;AAEA,wBAAA,QAFA,GAEA,OAAA,CAAA,MAAA,CAAA,QAFA,EAIA;;AACA,wBAAA,YAAA,CAAA,OAAA,CAAA,UAAA,EAAA,QAAA,IAAA,EAAA;AACA,wBAAA,YAAA,CAAA,OAAA,CAAA,UAAA,EAAA,QAAA,IAAA,EAAA,EANA,CAQA;;AACA,wBAAA,YATA,GASA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,QATA;AAUA,wBAAA,OAAA,CAAA,GAAA,CAAA,kBAAA,EAAA,YAAA;;AAEA,4BAAA,YAAA,EAAA;AACA;AACA,0BAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,YAAA;;AACA,0BAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,YAAA;AACA,yBAJA,MAIA;AACA;AACA,8BAAA,MAAA,CAAA,WAAA,CAAA,QAAA,CAAA,EAAA;AACA;AACA,4BAAA,OAAA,CAAA,GAAA,CAAA,kBAAA;;AACA,4BAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,qBAAA;AACA,2BAJA,MAIA;AACA;AACA,4BAAA,OAAA,CAAA,GAAA,CAAA,iBAAA;;AACA,4BAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA;AACA;AACA,uBA5BA,MA4BA;AACA;AACA,wBAAA,aAFA,GAEA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,QAFA;;AAGA,4BAAA,aAAA,EAAA;AACA,0BAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA,yBAFA,MAEA;AACA,0BAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA;AACA;;AAjEA;AAAA;;AAAA;AAAA;AAAA;AAmEA,sBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,eAnEA,CAoEA;;AACA,sBAAA,cArEA,GAqEA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,QArEA;;AAsEA,0BAAA,cAAA,EAAA;AACA,wBAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,cAAA;AACA,uBAFA,MAEA;AACA,wBAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA;;AA1EA;AAAA;AAAA;;AAAA;AA6EA,sBAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,wBAAA,OAAA,EAAA,MADA;AAEA,wBAAA,WAAA,EAAA,GAAA,CAAA,OAAA,IAAA,iBAFA;AAGA,wBAAA,SAAA,EAAA,UAHA;AAIA,wBAAA,QAAA,EAAA,CAJA;AAKA,wBAAA,KAAA,EAAA;AACA,0BAAA,KAAA,EAAA,OADA;AAEA,0BAAA,SAAA,EAAA,OAFA;AAGA,0BAAA,YAAA,EAAA,KAHA;AAIA,0BAAA,SAAA,EAAA;AAJA;AALA,uBAAA;;AAYA,sBAAA,MAAA,CAAA,qBAAA,GAzFA,CAyFA;;;AAzFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAA;;AAAA;AAAA;AAAA;AAAA,eA2FA,KA3FA;AAAA,iFA2FA,kBAAA,GAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAA,MAAA,CAAA,YAAA,GAAA,KAAA,CADA,CAGA;;AAHA,4BAIA,GAAA,CAAA,QAAA,IAAA,GAAA,CAAA,QAAA,CAAA,IAAA,IAAA,GAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,KAAA,IAJA;AAAA;AAAA;AAAA;;AAKA,sBAAA,OAAA,CAAA,GAAA,CAAA,qBAAA;AACA,sBAAA,YANA,GAMA,GAAA,CAAA,QAAA,CAAA,IAAA,CAAA,MANA,EAQA;;AACA,sBAAA,YATA;AAAA,6FASA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,kCAAA,gBADA,mCAEA,WAFA;AAGA,oCAAA,SAAA,EAAA,OAHA,CAGA;;AAHA;AAKA,kCAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,gBAAA;AALA;AAAA,yCAMA,KAAA,CAAA,gBAAA,CANA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBATA;;AAAA,wCASA,YATA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,6BAoBA,mBAAA,CAAA,YAAA,EAAA,YAAA,CApBA;;AAAA;AAoBA,sBAAA,kBApBA;;AAAA,4BAsBA,kBAAA,KAAA,kBAAA,CAAA,IAAA,KAAA,GAAA,IAAA,kBAAA,CAAA,IAAA,KAAA,KAAA,CAtBA;AAAA;AAAA;AAAA;;AAuBA;AACA,sBAAA,MAAA,CAAA,aAAA,CAAA,OAAA,CAAA;AACA,wBAAA,OAAA,EAAA,MADA;AAEA,wBAAA,WAAA,EAAA,mBAFA;AAGA,wBAAA,SAAA,EAAA,UAHA;AAIA,wBAAA,QAAA,EAAA;AAJA,uBAAA,EAxBA,CA+BA;;;AACA,sBAAA,MAhCA,GAgCA,kBAAA,CAAA,MAhCA;AAiCA,sBAAA,QAjCA,GAiCA,MAAA,CAAA,QAjCA;AAkCA,sBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,YAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,sBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,SAAA,EAAA,QAAA,CAAA,QAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,sBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,SAAA,EAAA,QAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,sBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,qBAAA,EAAA,MAAA,CAAA,eAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,EArCA,CAuCA;;AACA,sBAAA,YAxCA,GAwCA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,QAxCA;;AAyCA,0BAAA,YAAA,EAAA;AACA,wBAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,YAAA;AACA,uBAFA,MAEA;AACA,wBAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA;;AA7CA;AAAA;;AAAA;AAAA,4BA+CA,IAAA,KAAA,CAAA,kBAAA,IAAA,kBAAA,CAAA,OAAA,IAAA,QAAA,CA/CA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA,4BAkDA,aAAA,OAAA,KAAA,gBAlDA;AAAA;AAAA;AAAA;;AAmDA;AACA,sBAAA,OAAA,CAAA,GAAA,CAAA,eAAA;;AACA,sBAAA,MAAA,CAAA,qBAAA,GArDA,CAqDA;;;AArDA;;AAAA;AAwDA,sBAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,wBAAA,OAAA,EAAA,MADA;AAEA,wBAAA,WAAA,EAAA,aAAA,OAAA,IAAA,QAFA;AAGA,wBAAA,SAAA,EAAA,UAHA;AAIA,wBAAA,QAAA,EAAA;AAJA,uBAAA;;AAMA,sBAAA,MAAA,CAAA,qBAAA,GA9DA,CA8DA;;;AA9DA;AAAA;AAAA;;AAAA;AAkEA;AACA,sBAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,wBAAA,OAAA,EAAA,MADA;AAEA,wBAAA,WAAA,EAAA,GAAA,CAAA,OAAA,IAAA,iBAFA;AAGA,wBAAA,SAAA,EAAA,UAHA;AAIA,wBAAA,QAAA,EAAA,CAJA;AAKA,wBAAA,KAAA,EAAA;AACA,0BAAA,KAAA,EAAA,OADA;AAEA,0BAAA,SAAA,EAAA,OAFA;AAGA,0BAAA,YAAA,EAAA,KAHA;AAIA,0BAAA,SAAA,EAAA;AAJA;AALA,uBAAA;;AAYA,sBAAA,MAAA,CAAA,qBAAA,GA/EA,CA+EA;;;AA/EA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA3FA;;AAAA;AAAA;AAAA;AAAA;AA6KA;AACA,OAhMA;AAiMA,KAzNA;AA2NA,IAAA,oBA3NA,kCA2NA;AACA,WAAA,aAAA,CAAA,IAAA,CAAA;AACA,QAAA,OAAA,EAAA,MADA;AAEA,QAAA,WAAA,EAAA,qBAFA;AAGA,QAAA,SAAA,EAAA,UAHA;AAIA,QAAA,QAAA,EAAA,CAJA;AAKA,QAAA,KAAA,EAAA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,SAAA,EAAA,OAFA;AAGA,UAAA,YAAA,EAAA,KAHA;AAIA,UAAA,SAAA,EAAA;AAJA;AALA,OAAA,EADA,CAaA;AACA,KAzOA;AA2OA,IAAA,iBA3OA,6BA2OA,IA3OA,EA2OA;AACA,UAAA,OAAA,GAAA;AACA,QAAA,MAAA,EAAA,IADA;AAEA,QAAA,EAAA,EAAA,IAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,WAAA,QAAA,CAAA,IAAA,WAAA,OAAA,CAAA,IAAA,CAAA,oDANA,CAOA;AACA,KAnPA;AAuPA;AACA,IAAA,eAxPA,6BAwPA;AACA;AACA,UAAA,OAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,GAAA;AACA,UAAA,UAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,MAAA;AACA,UAAA,eAAA,GAAA,OAAA,IAAA,UAAA;;AAEA,UAAA,eAAA,EAAA;AACA,aAAA,iBAAA,GAAA,eAAA,CADA,CAEA;;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,eAAA,EAAA,KAAA,EAAA,OAAA,GAAA,OAAA,GAAA,UAAA;AACA;AACA,KAnQA;AAqQA;AACA,IAAA,eAtQA,2BAsQA,IAtQA,EAsQA;AACA,WAAA,SAAA,GAAA,IAAA,CADA,CAGA;;AACA,WAAA,YAAA,GAAA,CAAA;AACA,WAAA,cAAA,GAAA,CAAA;;AAEA,UAAA,IAAA,KAAA,QAAA,EAAA;AACA,aAAA,yBAAA;AACA;AACA,KAhRA;AAkRA;AACA,IAAA,gBAnRA,4BAmRA,CAnRA,EAmRA;AAAA;;AACA,MAAA,CAAA,CAAA,cAAA;AACA,WAAA,cAAA,CAAA,cAAA;AAAA,6EAAA,kBAAA,GAAA,EAAA,MAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,GADA;AAAA;AAAA;AAAA;;AAEA,kBAAA,MAAA,CAAA,iBAAA,GAAA,IAAA;AAFA;AAAA;AAAA,yBAMA,aAAA,CAAA,MAAA,CAAA,KAAA,EAAA,OAAA,CANA;;AAAA;AAMA,kBAAA,aANA;;AAAA,uBAQA,aAAA,CAAA,OARA;AAAA;AAAA;AAAA;;AAAA;AAAA,yBAUA,MAAA,CAAA,oBAAA,CAAA,OAAA,EAAA,MAAA,CAVA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,yBAaA,MAAA,CAAA,gBAAA,CAAA,MAAA,CAbA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAgBA,kBAAA,MAAA,CAAA,iBAAA,GAAA,KAAA;;AACA,kBAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,oBAAA,OAAA,EAAA,MADA;AAEA,oBAAA,WAAA,EAAA,aAAA,OAAA,IAAA,WAFA;AAGA,oBAAA,SAAA,EAAA,UAHA;AAIA,oBAAA,QAAA,EAAA;AAJA,mBAAA;;AAjBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAA;;AAAA;AAAA;AAAA;AAAA;AA0BA,KA/SA;AAiTA;AACA,IAAA,gBAlTA,4BAkTA,CAlTA,EAkTA;AAAA;;AACA,MAAA,CAAA,CAAA,cAAA;AACA,WAAA,cAAA,CAAA,cAAA;AAAA,6EAAA,kBAAA,GAAA,EAAA,MAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,GADA;AAAA;AAAA;AAAA;;AAEA,kBAAA,MAAA,CAAA,iBAAA,GAAA,IAAA;AAFA;AAAA;AAAA,yBAMA,aAAA,CAAA,MAAA,CAAA,KAAA,EAAA,OAAA,CANA;;AAAA;AAMA,kBAAA,aANA;;AAAA,uBAQA,aAAA,CAAA,OARA;AAAA;AAAA;AAAA;;AAAA;AAAA,yBAUA,MAAA,CAAA,oBAAA,CAAA,OAAA,EAAA,MAAA,CAVA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,yBAaA,MAAA,CAAA,kBAAA,CAAA,MAAA,CAbA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAgBA,kBAAA,MAAA,CAAA,iBAAA,GAAA,KAAA;;AACA,kBAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,oBAAA,OAAA,EAAA,MADA;AAEA,oBAAA,WAAA,EAAA,aAAA,OAAA,IAAA,WAFA;AAGA,oBAAA,SAAA,EAAA,UAHA;AAIA,oBAAA,QAAA,EAAA;AAJA,mBAAA;;AAjBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAA;;AAAA;AAAA;AAAA;AAAA;AA0BA,KA9UA;AAgVA;AACA,IAAA,oBAjVA;AAAA,6GAiVA,IAjVA,EAiVA,MAjVA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAmVA;AACA,gBAAA,cApVA,GAoVA,KAAA,sBAAA,EApVA,EAsVA;;AACA,gBAAA,YAvVA;AAwVA,kBAAA,IAAA,EAAA;AAxVA,kDAyVA,IAzVA,EAyVA,MAAA,CAAA,IAAA,CAzVA,gDA0VA,MAAA,CAAA,IAAA,KAAA,OAAA,GAAA,SAAA,GAAA,WAAA,CA1VA,8CA4VA,cA5VA,qDA6VA,cA7VA,gDA8VA,KAAA,iBA9VA,kDA+VA,KAAA,iBAAA,GAAA,MAAA,GAAA,IA/VA;AAkWA,gBAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,YAAA,EAlWA,CAoWA;;AApWA;AAAA,uBAqWA,QAAA,CAAA,YAAA,CArWA;;AAAA;AAqWA,gBAAA,gBArWA;;AAAA,qBAuWA,gBAAA,CAAA,OAvWA;AAAA;AAAA;AAAA;;AAwWA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,gBAAA,CAAA,MAAA,EAzWA,CA2WA;;AA3WA;AAAA,uBA4WA,KAAA,gBAAA,CAAA,IAAA,EAAA,MAAA,EAAA,cAAA,CA5WA;;AAAA;AAAA;AAAA;;AAAA;AAAA,sBA8WA,IAAA,KAAA,CAAA,gBAAA,CAAA,OAAA,IAAA,MAAA,CA9WA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAmXA,qBAAA,iBAAA,GAAA,KAAA;AACA,qBAAA,iBAAA,GAAA,KAAA;AApXA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAwXA;AACA,IAAA,gBAzXA;AAAA,yGAyXA,MAzXA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2XA;AACA,gBAAA,SA5XA,GA4XA;AACA,kBAAA,MAAA,EAAA,MAAA,CAAA,KADA;AAEA,kBAAA,OAAA,EAAA,MAAA,CAAA,OAFA;AAGA,kBAAA,SAAA,EAAA,SAHA,CAGA;;AAHA,iBA5XA;AAkYA,gBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,SAAA,EAlYA,CAoYA;;AApYA;AAAA,uBAqYA,UAAA,CAAA,SAAA,CArYA;;AAAA;AAqYA,gBAAA,aArYA;;AAAA,qBAuYA,aAAA,CAAA,OAvYA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAyYA,KAAA,kBAAA,CAAA,aAAA,CAAA,MAAA,CAzYA;;AAAA;AAAA;AAAA;;AAAA;AAAA,sBA4YA,aAAA,CAAA,IAAA,KAAA,IA5YA;AAAA;AAAA;AAAA;;AA6YA,gBAAA,OAAA,CAAA,GAAA,CAAA,mBAAA;AACA,gBAAA,YA9YA,GA8YA,aAAA,CAAA,MA9YA,EAgZA;;AACA,gBAAA,YAjZA;AAAA,uFAiZA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,4BAAA,cADA,mCAEA,SAFA;AAGA,8BAAA,SAAA,EAAA,OAHA,CAGA;;AAHA;AAKA,4BAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,cAAA;AALA;AAAA,mCAMA,UAAA,CAAA,cAAA,CANA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAjZA;;AAAA,kCAiZA,YAjZA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,uBA4ZA,mBAAA,CAAA,YAAA,EAAA,YAAA,CA5ZA;;AAAA;AA4ZA,gBAAA,kBA5ZA;;AAAA,sBA8ZA,kBAAA,IAAA,kBAAA,CAAA,OA9ZA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAgaA,KAAA,kBAAA,CAAA,kBAAA,CAAA,MAAA,CAhaA;;AAAA;AAAA;AAAA;;AAAA;AAAA,sBAkaA,IAAA,KAAA,CAAA,kBAAA,IAAA,kBAAA,CAAA,OAAA,IAAA,QAAA,CAlaA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA,sBAqaA,aAAA,OAAA,KAAA,gBAraA;AAAA;AAAA;AAAA;;AAsaA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,aAAA;AAvaA;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA,sBA8aA,IAAA,KAAA,CAAA,aAAA,CAAA,OAAA,IAAA,MAAA,CA9aA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAobA,qBAAA,iBAAA,GAAA,KAAA;AApbA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAwbA;AACA,IAAA,kBAzbA;AAAA,4GAybA,MAzbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2bA;AACA,gBAAA,SA5bA,GA4bA;AACA,kBAAA,KAAA,EAAA,MAAA,CAAA,KADA;AAEA,kBAAA,SAAA,EAAA,MAAA,CAAA,SAFA;AAGA,kBAAA,SAAA,EAAA,SAHA,CAGA;;AAHA,iBA5bA;AAkcA,gBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,SAAA,EAlcA,CAocA;;AApcA;AAAA,uBAqcA,UAAA,CAAA,SAAA,CArcA;;AAAA;AAqcA,gBAAA,aArcA;;AAAA,qBAucA,aAAA,CAAA,OAvcA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAycA,KAAA,kBAAA,CAAA,aAAA,CAAA,MAAA,CAzcA;;AAAA;AAAA;AAAA;;AAAA;AAAA,sBA4cA,aAAA,CAAA,IAAA,KAAA,IA5cA;AAAA;AAAA;AAAA;;AA6cA,gBAAA,OAAA,CAAA,GAAA,CAAA,kBAAA;AACA,gBAAA,YA9cA,GA8cA,aAAA,CAAA,MA9cA,EAgdA;;AACA,gBAAA,YAjdA;AAAA,uFAidA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,4BAAA,cADA,mCAEA,SAFA;AAGA,8BAAA,SAAA,EAAA,OAHA,CAGA;;AAHA;AAKA,4BAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,cAAA;AALA;AAAA,mCAMA,UAAA,CAAA,cAAA,CANA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAjdA;;AAAA,kCAidA,YAjdA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,uBA4dA,mBAAA,CAAA,YAAA,EAAA,YAAA,CA5dA;;AAAA;AA4dA,gBAAA,kBA5dA;;AAAA,sBA8dA,kBAAA,IAAA,kBAAA,CAAA,OA9dA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAgeA,KAAA,kBAAA,CAAA,kBAAA,CAAA,MAAA,CAheA;;AAAA;AAAA;AAAA;;AAAA;AAAA,sBAkeA,IAAA,KAAA,CAAA,kBAAA,IAAA,kBAAA,CAAA,OAAA,IAAA,QAAA,CAleA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA,sBAqeA,cAAA,OAAA,KAAA,gBAreA;AAAA;AAAA;AAAA;;AAseA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,YAAA;AAveA;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA,sBA8eA,IAAA,KAAA,CAAA,aAAA,CAAA,OAAA,IAAA,MAAA,CA9eA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAofA,qBAAA,iBAAA,GAAA,KAAA;AApfA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAwfA;AACA,IAAA,kBAzfA;AAAA,4GAyfA,MAzfA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2fA;AACA,gBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,YAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,gBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,SAAA,EAAA,MAAA,CAAA,QAAA,CAAA,QAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,gBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,SAAA,EAAA,MAAA,CAAA,QAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,gBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,qBAAA,EAAA,MAAA,CAAA,eAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,EA/fA,CAigBA;;AACA,qBAAA,aAAA,CAAA,OAAA,CAAA;AACA,kBAAA,OAAA,EAAA,MADA;AAEA,kBAAA,WAAA,0CAAA,MAAA,CAAA,QAAA,CAAA,QAAA,IAAA,MAAA,CAAA,QAAA,CAAA,QAAA,WAFA;AAGA,kBAAA,SAAA,EAAA,UAHA;AAIA,kBAAA,QAAA,EAAA;AAJA,iBAAA,EAlgBA,CAygBA;;AACA,gBAAA,QA1gBA,GA0gBA,KAAA,MAAA,CAAA,KAAA,CAAA,QAAA,IAAA,GA1gBA;AA2gBA,qBAAA,OAAA,CAAA,IAAA,CAAA,QAAA;AA3gBA;AAAA;;AAAA;AAAA;AAAA;AA6gBA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AA7gBA,sBA8gBA,IAAA,KAAA,CAAA,SAAA,CA9gBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAkhBA;AACA,IAAA,gBAnhBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAohBA,gBAAA,KAphBA,GAohBA,KAAA,cAAA,CAAA,aAAA,CAAA,OAAA,CAphBA;;AAAA,oBAqhBA,KArhBA;AAAA;AAAA;AAAA;;AAshBA,qBAAA,QAAA,CAAA,KAAA,CAAA,SAAA;AAthBA;;AAAA;AAAA,oBA0hBA,gBAAA,IAAA,CAAA,KAAA,CA1hBA;AAAA;AAAA;AAAA;;AA2hBA,qBAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AA3hBA;;AAAA;AA+hBA,qBAAA,cAAA,GAAA,IAAA;AA/hBA;AAAA;AAAA,uBAiiBA,WAAA,CAAA,KAAA,EAAA,UAAA,CAjiBA;;AAAA;AAiiBA,gBAAA,QAjiBA;;AAmiBA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,uBAAA,QAAA,CAAA,OAAA,CAAA,eAAA;AACA,uBAAA,iBAAA;AACA,iBAHA,MAGA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,SAAA;AACA;;AAxiBA;AAAA;;AAAA;AAAA;AAAA;AA0iBA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,eAAA;;AA3iBA;AAAA;AA6iBA,qBAAA,cAAA,GAAA,KAAA;AA7iBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAijBA;AACA,IAAA,kBAljBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmjBA,gBAAA,KAnjBA,GAmjBA,KAAA,cAAA,CAAA,aAAA,CAAA,OAAA,CAnjBA;;AAAA,oBAojBA,KApjBA;AAAA;AAAA;AAAA;;AAqjBA,qBAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AArjBA;;AAAA;AAAA,oBAyjBA,6BAAA,IAAA,CAAA,KAAA,CAzjBA;AAAA;AAAA;AAAA;;AA0jBA,qBAAA,QAAA,CAAA,KAAA,CAAA,SAAA;AA1jBA;;AAAA;AA8jBA,qBAAA,gBAAA,GAAA,IAAA;AA9jBA;AAAA;AAAA,uBAgkBA,aAAA,CAAA,KAAA,EAAA,UAAA,CAhkBA;;AAAA;AAgkBA,gBAAA,QAhkBA;;AAkkBA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,uBAAA,QAAA,CAAA,OAAA,CAAA,eAAA;AACA,uBAAA,mBAAA;AACA,iBAHA,MAGA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,SAAA;AACA;;AAvkBA;AAAA;;AAAA;AAAA;AAAA;AAykBA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,eAAA;;AA1kBA;AAAA;AA4kBA,qBAAA,gBAAA,GAAA,KAAA;AA5kBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAglBA;AACA,IAAA,iBAjlBA,+BAilBA;AAAA;;AACA,WAAA,YAAA,GAAA,EAAA;AACA,UAAA,KAAA,GAAA,WAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,YAAA;;AACA,YAAA,MAAA,CAAA,YAAA,IAAA,CAAA,EAAA;AACA,UAAA,aAAA,CAAA,KAAA,CAAA;AACA;AACA,OALA,EAKA,IALA,CAAA;AAMA,KAzlBA;AA2lBA;AACA,IAAA,mBA5lBA,iCA4lBA;AAAA;;AACA,WAAA,cAAA,GAAA,EAAA;AACA,UAAA,KAAA,GAAA,WAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,cAAA;;AACA,YAAA,MAAA,CAAA,cAAA,IAAA,CAAA,EAAA;AACA,UAAA,aAAA,CAAA,KAAA,CAAA;AACA;AACA,OALA,EAKA,IALA,CAAA;AAMA,KApmBA;AAsmBA;AACA,IAAA,yBAvmBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAymBA,oBAAA,CAAA,OAAA,EAAA,KAAA,iBAAA,CAzmBA;;AAAA;AAymBA,gBAAA,QAzmBA;;AA0mBA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,uBAAA,iBAAA,GAAA,QAAA,CAAA,MAAA,CAAA,SAAA;AACA,iBAFA,MAEA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,WAAA;AACA;;AA9mBA;AAAA;;AAAA;AAAA;AAAA;AAgnBA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA;;AAjnBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAqnBA;AACA,IAAA,sBAtnBA,oCAsnBA;AACA,UAAA,OAAA,GAAA,sDAAA;AACA,UAAA,OAAA,GAAA,YAAA;AACA,UAAA,QAAA,GAAA,OAAA,GAAA,OAAA;AAEA,UAAA,QAAA,GAAA,EAAA,CALA,CAOA;;AACA,MAAA,QAAA,IAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,MAAA,KAAA,OAAA,CAAA,MAAA,CAAA,CAAA;AACA,MAAA,QAAA,IAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,MAAA,KAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CATA,CAWA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,QAAA,IAAA,QAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,MAAA,KAAA,QAAA,CAAA,MAAA,CAAA,CAAA;AACA,OAdA,CAgBA;;;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,EAAA,EAAA,IAAA,CAAA;AAAA,eAAA,IAAA,CAAA,MAAA,KAAA,GAAA;AAAA,OAAA,EAAA,IAAA,CAAA,EAAA,CAAA;AACA,KAxoBA;AA0oBA;AACA,IAAA,gBA3oBA;AAAA,0GA2oBA,IA3oBA,EA2oBA,MA3oBA,EA2oBA,QA3oBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6oBA;AACA,qBAAA,qBAAA,GA9oBA,CAgpBA;;AACA,gBAAA,QAjpBA,GAipBA,MAAA,CAAA,IAAA,CAjpBA,EAipBA;;AACA,gBAAA,IAlpBA,GAkpBA,UAAA,CAAA,QAAA,EAAA,KAAA,eAAA,CAAA,GAAA,EAAA,KAAA,eAAA,CAAA,EAAA,CAlpBA;AAmpBA,gBAAA,GAnpBA,GAmpBA,UAAA,CAAA,QAAA,EAAA,KAAA,eAAA,CAAA,GAAA,EAAA,KAAA,eAAA,CAAA,EAAA,CAnpBA;AAqpBA,gBAAA,WArpBA,GAqpBA;AACA,kBAAA,QAAA,EAAA,IADA;AAEA,kBAAA,QAAA,EAAA,GAFA;AAGA,kBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,kBAAA,QAAA,EAAA,KAAA,YAJA;AAKA,kBAAA,WAAA,EAAA,IALA;AAMA,kBAAA,SAAA,EAAA;AANA,iBArpBA;AA8pBA,gBAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA;AAAA,kBAAA,QAAA,EAAA,QAAA;AAAA,kBAAA,SAAA,EAAA,MAAA;AAAA,kBAAA,QAAA,EAAA,KAAA;AAAA,iBAAA;AA9pBA;AAAA,uBAgqBA,KAAA,CAAA,WAAA,CAhqBA;;AAAA;AAgqBA,gBAAA,aAhqBA;;AAAA,sBAkqBA,aAAA,CAAA,IAAA,KAAA,GAAA,IAAA,aAAA,CAAA,IAAA,KAAA,KAlqBA;AAAA;AAAA;AAAA;;AAmqBA;AACA,qBAAA,aAAA,CAAA,OAAA,CAAA;AACA,kBAAA,OAAA,EAAA,aADA;AAEA,kBAAA,WAAA,0LAAA,IAAA,KAAA,OAAA,GAAA,KAAA,GAAA,IAAA,yCAFA;AAGA,kBAAA,SAAA,EAAA,UAHA;AAIA,kBAAA,QAAA,EAAA;AAJA,iBAAA,EApqBA,CA2qBA;;AACA,gBAAA,MA5qBA,GA4qBA,aAAA,CAAA,MA5qBA;AA6qBA,gBAAA,QA7qBA,GA6qBA,MAAA,CAAA,QA7qBA;AA8qBA,gBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,YAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,gBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,SAAA,EAAA,QAAA,CAAA,QAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,gBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,SAAA,EAAA,QAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,gBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,qBAAA,EAAA,MAAA,CAAA,eAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,EAjrBA,CAmrBA;;AACA,gBAAA,UAAA,CAAA,YAAA;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA,iBAFA,EAEA,IAFA,CAAA;AAprBA;AAAA;;AAAA;AAAA,sBAwrBA,IAAA,KAAA,CAAA,aAAA,CAAA,OAAA,IAAA,QAAA,CAxrBA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA2rBA,gBAAA,OAAA,CAAA,KAAA,CAAA,SAAA;AACA,qBAAA,aAAA,CAAA,KAAA,CAAA;AACA,kBAAA,OAAA,EAAA,cADA;AAEA,kBAAA,WAAA,EAAA,YAFA;AAGA,kBAAA,SAAA,EAAA,UAHA;AAIA,kBAAA,QAAA,EAAA;AAJA,iBAAA;;AA5rBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA6sBA;AACA,IAAA,cA9sBA,4BA8sBA;AACA;AACA,UAAA,EAAA,GAAA,IAAA,CAAA,QAAA,EAAA,CAFA,CAIA;;AACA,MAAA,EAAA,CAAA,EAAA,CAAA,KAAA,KAAA,CAAA,SAAA,EAAA;AACA,QAAA,QAAA,EAAA,GADA;AAEA,QAAA,CAAA,EAAA,CAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAAA,EALA,CAYA;;AACA,MAAA,EAAA,CAAA,EAAA,CAAA,KAAA,KAAA,CAAA,cAAA,EAAA;AACA,QAAA,QAAA,EAAA,GADA;AAEA,QAAA,CAAA,EAAA,CAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAAA,EAKA,OALA,EAbA,CAkBA;AAEA;;AACA,MAAA,EAAA,CAAA,EAAA,CAAA,eAAA,EAAA;AACA,QAAA,QAAA,EAAA,GADA;AAEA,QAAA,CAAA,EAAA,CAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,OAAA,EAAA,IAJA;AAIA;AACA,QAAA,IAAA,EAAA;AALA,OAAA,EAMA,OANA,EArBA,CA2BA;AACA;AA1uBA;AAjEA,CAAA", "sourcesContent": ["<template>\n  <div class=\"website-login\">\n    <!-- 动态背景 -->\n    <div class=\"login-background\">\n      <div class=\"bg-animated-grid\"></div>\n      <div class=\"bg-floating-elements\"></div>\n      <div class=\"bg-gradient-overlay\"></div>\n    </div>\n\n    <!-- 复用官网页头组件 -->\n    <WebsiteHeader />\n\n    <!-- 主要内容区域 -->\n    <div class=\"login-main\">\n      <!-- 左侧信息展示 -->\n      <div class=\"login-info\" ref=\"loginInfo\">\n        <div class=\"info-content\">\n          <div class=\"brand-showcase\">\n            <div class=\"brand-logo-large\">\n              <LogoImage\n                size=\"large\"\n                :hover=\"false\"\n                container-class=\"login-logo-container\"\n                image-class=\"login-logo-image\"\n                fallback-class=\"login-logo-fallback\"\n              />\n              <h1 class=\"brand-title\">智界AIGC</h1>\n            </div>\n            <p class=\"brand-slogan\">AI驱动的内容生成平台</p>\n          </div>\n\n          <div class=\"feature-highlights\">\n            <div class=\"feature-item\" v-for=\"(feature, index) in features\" :key=\"index\">\n              <div class=\"feature-icon\">\n                <a-icon :type=\"feature.icon\" />\n              </div>\n              <div class=\"feature-text\">\n                <h3>{{ feature.title }}</h3>\n                <p>{{ feature.description }}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 右侧登录表单 -->\n      <div class=\"login-container\" ref=\"loginContainer\">\n        <div class=\"login-card\">\n          <!-- 登录头部 -->\n          <div class=\"login-header\">\n            <h2 class=\"login-title\">欢迎使用智界AIGC</h2>\n            <p class=\"login-subtitle\">{{ inviteCodeFromUrl ? '您正在通过邀请链接登录' : '选择您的登录方式，开启AI创作之旅' }}</p>\n          </div>\n\n          <!-- 登录方式切换Tab -->\n          <div class=\"auth-tabs\">\n            <div class=\"tab-buttons\">\n              <button\n                :class=\"['tab-btn', { active: loginType === 'phone' }]\"\n                @click=\"switchLoginType('phone')\"\n              >\n                <a-icon type=\"mobile\" />\n                <span class=\"tab-text\">手机号</span>\n              </button>\n              <button\n                :class=\"['tab-btn', { active: loginType === 'email' }]\"\n                @click=\"switchLoginType('email')\"\n              >\n                <a-icon type=\"mail\" />\n                <span class=\"tab-text\">邮箱</span>\n              </button>\n              <!-- 🔐 微信登录（暂时隐藏，待后续配置） -->\n              <!-- <button\n                :class=\"['tab-btn', { active: loginType === 'wechat' }]\"\n                @click=\"switchLoginType('wechat')\"\n              >\n                <a-icon type=\"wechat\" />\n                <span class=\"tab-text\">微信</span>\n              </button> -->\n              <button\n                :class=\"['tab-btn', { active: loginType === 'password' }]\"\n                @click=\"switchLoginType('password')\"\n              >\n                <a-icon type=\"lock\" />\n                <span class=\"tab-text\">密码登录</span>\n              </button>\n            </div>\n          </div>\n\n          <!-- 登录表单 -->\n          <div class=\"login-form\">\n            <!-- 密码登录 -->\n            <div v-if=\"loginType === 'password'\" class=\"login-content\">\n              <a-form :form=\"form\" @submit=\"handleSubmit\" class=\"account-login-form\">\n              <!-- 用户名输入 -->\n              <div class=\"input-group\">\n                <a-form-item>\n                  <a-input\n                    v-decorator=\"['username', { rules: [{ required: true, message: '请输入用户名或邮箱' }] }]\"\n                    size=\"large\"\n                    placeholder=\"用户名或邮箱\"\n                    class=\"clean-input\"\n                  >\n                    <a-icon slot=\"prefix\" type=\"user\" />\n                  </a-input>\n                </a-form-item>\n              </div>\n\n              <!-- 密码输入 -->\n              <div class=\"input-group\">\n                <a-form-item>\n                  <a-input-password\n                    v-decorator=\"['password', { rules: [{ required: true, message: '请输入密码' }] }]\"\n                    size=\"large\"\n                    placeholder=\"密码\"\n                    class=\"clean-input\"\n                  >\n                    <a-icon slot=\"prefix\" type=\"lock\" />\n                  </a-input-password>\n                </a-form-item>\n              </div>\n\n              <!-- 验证码 -->\n              <div class=\"input-group\">\n                <a-form-item>\n                  <div class=\"captcha-row\">\n                    <a-input\n                      v-decorator=\"['inputCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"\n                      size=\"large\"\n                      placeholder=\"验证码\"\n                      class=\"clean-input captcha-input\"\n                    >\n                      <a-icon slot=\"prefix\" type=\"safety-certificate\" />\n                    </a-input>\n                    <div class=\"captcha-image-container\" @click=\"handleChangeCheckCode\">\n                      <img\n                        :src=\"randCodeImage\"\n                        class=\"captcha-image\"\n                        alt=\"验证码\"\n                      />\n                      <div class=\"captcha-refresh-overlay\">\n                        <a-icon type=\"reload\" />\n                      </div>\n                    </div>\n                  </div>\n                </a-form-item>\n              </div>\n\n              <!-- 登录选项 -->\n              <div class=\"login-options\">\n                <a-checkbox v-model=\"rememberMe\" class=\"remember-me\">\n                  记住我\n                </a-checkbox>\n                <a class=\"forgot-link\" @click=\"handleForgotPassword\">\n                  忘记密码？\n                </a>\n              </div>\n\n              <!-- 登录按钮 -->\n              <a-form-item class=\"login-button-item\">\n                <a-button\n                  type=\"primary\"\n                  html-type=\"submit\"\n                  size=\"large\"\n                  :loading=\"loginLoading\"\n                  class=\"login-submit-button\"\n                  block\n                >\n                  <span v-if=\"!loginLoading\">登录</span>\n                  <span v-else>登录中...</span>\n                </a-button>\n              </a-form-item>\n              </a-form>\n            </div>\n\n            <!-- 手机号登录 -->\n            <div v-if=\"loginType === 'phone'\" class=\"login-content\">\n              <a-form :form=\"phoneLoginForm\" @submit=\"handlePhoneLogin\" class=\"phone-login-form\">\n                <!-- 手机号输入 -->\n                <div class=\"input-group\">\n                  <a-form-item>\n                    <a-input\n                      v-decorator=\"['phone', { rules: [\n                        { required: true, message: '请输入手机号' },\n                        { pattern: /^1[3-9]\\d{9}$/, message: '手机号格式不正确' }\n                      ] }]\"\n                      size=\"large\"\n                      placeholder=\"请输入手机号\"\n                      class=\"clean-input\"\n                    >\n                      <a-icon slot=\"prefix\" type=\"mobile\" />\n                    </a-input>\n                  </a-form-item>\n                </div>\n\n                <!-- 短信验证码 -->\n                <div class=\"input-group\">\n                  <a-form-item>\n                    <div class=\"verify-code-row\">\n                      <a-input\n                        v-decorator=\"['smsCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"\n                        size=\"large\"\n                        placeholder=\"请输入短信验证码\"\n                        class=\"clean-input verify-code-input\"\n                      >\n                        <a-icon slot=\"prefix\" type=\"safety-certificate\" />\n                      </a-input>\n                      <a-button\n                        :disabled=\"smsCodeSending || smsCountdown > 0\"\n                        @click=\"sendLoginSmsCode\"\n                        class=\"send-code-btn\"\n                        size=\"large\"\n                      >\n                        {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '发送验证码' }}\n                      </a-button>\n                    </div>\n                  </a-form-item>\n                </div>\n\n                <!-- 登录按钮 -->\n                <a-form-item class=\"login-button-item\">\n                  <a-button\n                    type=\"primary\"\n                    html-type=\"submit\"\n                    size=\"large\"\n                    :loading=\"phoneLoginLoading\"\n                    class=\"login-submit-button\"\n                    block\n                  >\n                    <span v-if=\"!phoneLoginLoading\">登录</span>\n                    <span v-else>登录中...</span>\n                  </a-button>\n                </a-form-item>\n\n                <!-- 提示信息 -->\n                <div class=\"phone-login-tip\">\n                  <a-alert\n                    message=\"手机号登录说明\"\n                    description=\"首次使用手机号登录将自动为您创建账户，无需设置密码\"\n                    type=\"info\"\n                    show-icon\n                  />\n                </div>\n              </a-form>\n            </div>\n\n            <!-- 邮箱登录 -->\n            <div v-if=\"loginType === 'email'\" class=\"login-content\">\n              <a-form :form=\"emailLoginForm\" @submit=\"handleEmailLogin\" class=\"email-login-form\">\n                <!-- 邮箱输入 -->\n                <div class=\"input-group\">\n                  <a-form-item>\n                    <a-input\n                      v-decorator=\"['email', { rules: [\n                        { required: true, message: '请输入邮箱' },\n                        { type: 'email', message: '邮箱格式不正确' }\n                      ] }]\"\n                      size=\"large\"\n                      placeholder=\"请输入邮箱\"\n                      class=\"clean-input\"\n                    >\n                      <a-icon slot=\"prefix\" type=\"mail\" />\n                    </a-input>\n                  </a-form-item>\n                </div>\n\n                <!-- 邮箱验证码 -->\n                <div class=\"input-group\">\n                  <a-form-item>\n                    <div class=\"verify-code-row\">\n                      <a-input\n                        v-decorator=\"['emailCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"\n                        size=\"large\"\n                        placeholder=\"请输入邮箱验证码\"\n                        class=\"clean-input verify-code-input\"\n                      >\n                        <a-icon slot=\"prefix\" type=\"safety-certificate\" />\n                      </a-input>\n                      <a-button\n                        :disabled=\"emailCodeSending || emailCountdown > 0\"\n                        @click=\"sendLoginEmailCode\"\n                        class=\"send-code-btn\"\n                        size=\"large\"\n                      >\n                        {{ emailCountdown > 0 ? `${emailCountdown}s后重发` : '发送验证码' }}\n                      </a-button>\n                    </div>\n                  </a-form-item>\n                </div>\n\n                <!-- 登录按钮 -->\n                <a-form-item class=\"login-button-item\">\n                  <a-button\n                    type=\"primary\"\n                    html-type=\"submit\"\n                    size=\"large\"\n                    :loading=\"emailLoginLoading\"\n                    class=\"login-submit-button\"\n                    block\n                  >\n                    <span v-if=\"!emailLoginLoading\">登录</span>\n                    <span v-else>登录中...</span>\n                  </a-button>\n                </a-form-item>\n\n                <!-- 提示信息 -->\n                <div class=\"email-login-tip\">\n                  <a-alert\n                    message=\"邮箱登录说明\"\n                    description=\"首次使用邮箱登录将自动为您创建账户，无需设置密码\"\n                    type=\"info\"\n                    show-icon\n                  />\n                </div>\n              </a-form>\n            </div>\n\n            <!-- 微信登录 -->\n            <div v-if=\"loginType === 'wechat'\" class=\"login-content\">\n              <div class=\"wechat-login-container\">\n                <div class=\"wechat-qr-section\">\n                  <div class=\"qr-code-container\">\n                    <img :src=\"wechatLoginQrCode\" alt=\"微信登录二维码\" class=\"qr-code-image\" v-if=\"wechatLoginQrCode\" />\n                    <div class=\"qr-loading\" v-else>\n                      <a-spin size=\"large\" />\n                      <p>正在生成二维码...</p>\n                    </div>\n                  </div>\n                  <div class=\"qr-instructions\">\n                    <h4>使用微信扫码登录</h4>\n                    <p>1. 打开微信扫一扫</p>\n                    <p>2. 扫描上方二维码</p>\n                    <p>3. 确认登录</p>\n                    <p v-if=\"inviteCodeFromUrl\" class=\"invite-tip\">* 您正在通过邀请链接登录</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { login, getCaptcha, phoneLogin, emailLogin } from '@/api/login'\nimport { encryption, getEncryptedString } from '@/utils/encryption/aesEncrypt'\nimport { getAction } from '@/api/manage'\nimport { gsap } from 'gsap'\nimport WebsiteHeader from '@/components/website/WebsiteHeader.vue'\nimport LogoImage from '@/components/common/LogoImage.vue'\nimport { ACCESS_TOKEN, USER_NAME, USER_INFO, UI_CACHE_DB_DICT_DATA } from '@/store/mutation-types'\nimport {\n  checkUsername,\n  sendSmsCode,\n  sendEmailCode,\n  register,\n  generateWechatQrCode\n} from '@/api/register'\nimport Vue from 'vue'\nimport { handleLoginConflict } from '@/utils/loginConflictHandler'\n\nexport default {\n  name: 'WebsiteLogin',\n  components: {\n    WebsiteHeader,\n    LogoImage\n  },\n  data() {\n    return {\n      // 登录相关\n      form: this.$form.createForm(this),\n      phoneLoginForm: this.$form.createForm(this),\n      emailLoginForm: this.$form.createForm(this),\n      loginLoading: false,\n      phoneLoginLoading: false,\n      emailLoginLoading: false,\n      rememberMe: false,\n      randCodeImage: '',\n      currdatetime: new Date().getTime(),\n      encryptedString: '',\n\n      // 登录方式切换\n      loginType: 'phone', // password, phone, email, wechat - 默认手机号登录\n\n      // 验证码相关\n      smsCodeSending: false,\n      smsCountdown: 0,\n      emailCodeSending: false,\n      emailCountdown: 0,\n\n      // 邀请码（静默处理）\n      inviteCodeFromUrl: '',\n\n      // 微信登录\n      wechatLoginQrCode: '',\n\n      features: [\n        {\n          icon: 'robot',\n          title: 'AI智能创作',\n          description: '强大的AI算法，助您快速生成高质量内容'\n        },\n        {\n          icon: 'thunderbolt',\n          title: '极速响应',\n          description: '毫秒级响应速度，让创作灵感不再等待'\n        },\n        {\n          icon: 'safety-certificate',\n          title: '安全可靠',\n          description: '企业级安全保障，保护您的创作成果'\n        },\n        {\n          icon: 'global',\n          title: '全球服务',\n          description: '覆盖全球的CDN网络，随时随地畅享服务'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.getEncrypte()\n    this.handleChangeCheckCode()\n    this.initAnimations()\n    this.checkInviteCode()\n  },\n  methods: {\n    // 获取密码加密规则\n    getEncrypte() {\n      getEncryptedString().then((data) => {\n        this.encryptedString = data\n      })\n    },\n\n    // 刷新验证码\n    handleChangeCheckCode() {\n      this.currdatetime = new Date().getTime()\n      getAction(`/sys/randomImage/${this.currdatetime}`).then(res => {\n        if (res.success) {\n          this.randCodeImage = res.result\n        } else {\n          this.$message.error(res.message)\n        }\n      }).catch(() => {\n        this.$message.error('验证码加载失败')\n      })\n    },\n\n    handleSubmit(e) {\n      e.preventDefault()\n      this.form.validateFields((err, values) => {\n        if (!err) {\n          this.loginLoading = true\n          console.log('官网登录信息:', values)\n\n          // 使用真实的登录API\n          let user = encryption(values.username, this.encryptedString.key, this.encryptedString.iv)\n          let pwd = encryption(values.password, this.encryptedString.key, this.encryptedString.iv)\n          let loginParams = {\n            username: user,\n            password: pwd,\n            captcha: values.inputCode,\n            checkKey: this.currdatetime,\n            remember_me: this.rememberMe,\n            loginType: 'website' // 标识为官网用户登录\n          }\n\n          console.log(\"官网登录参数\", loginParams)\n          login(loginParams).then(async (res) => {\n            this.loginLoading = false\n            console.log(\"🔍 登录响应:\", res)\n            console.log(\"🔍 响应code:\", res.code, \"类型:\", typeof res.code)\n            if (res.code === 200 || res.code === '200') {\n              this.$notification.success({\n                message: '登录成功',\n                description: '欢迎回来！正在跳转到个人中心...',\n                placement: 'topRight',\n                duration: 3,\n                style: {\n                  width: '350px',\n                  marginTop: '101px',\n                  borderRadius: '8px',\n                  boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                }\n              })\n\n              // ✅ 存储登录信息\n              const result = res.result\n              const userInfo = result.userInfo\n              Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)\n              Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)\n              Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)\n              Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)\n\n              // ✅ 获取用户角色信息\n              try {\n                const roleRes = await getAction(\"/sys/user/getCurrentUserDeparts\")\n                if (roleRes.success) {\n                  const userRole = roleRes.result.role\n                  const departId = roleRes.result.departId\n\n                  // 存储角色信息\n                  localStorage.setItem('userRole', userRole || '')\n                  localStorage.setItem('departId', departId || '')\n\n                  // 优先处理重定向参数\n                  const redirectPath = this.$route.query.redirect\n                  console.log('🔍 登录成功，检查重定向参数:', redirectPath)\n\n                  if (redirectPath) {\n                    // 有重定向参数，直接跳转到目标页面\n                    console.log('🔄 有重定向参数，跳转到:', redirectPath)\n                    this.$router.push(redirectPath)\n                  } else {\n                    // 没有重定向参数，根据角色决定跳转\n                    if (this.isAdminRole(userRole)) {\n                      // 管理员用户，跳转到后台\n                      console.log('🔄 管理员用户，跳转到后台管理')\n                      this.$router.push('/dashboard/analysis')\n                    } else {\n                      // 普通用户，跳转到个人中心\n                      console.log('🔄 普通用户，跳转到个人中心')\n                      this.$router.push('/usercenter')\n                    }\n                  }\n                } else {\n                  // 获取角色失败，检查重定向参数\n                  const redirectPath = this.$route.query.redirect\n                  if (redirectPath) {\n                    this.$router.push(redirectPath)\n                  } else {\n                    this.$router.push('/usercenter')\n                  }\n                }\n              } catch (error) {\n                console.error('获取角色信息失败:', error)\n                // 出错时也检查重定向参数\n                const redirectPath = this.$route.query.redirect\n                if (redirectPath) {\n                  this.$router.push(redirectPath)\n                } else {\n                  this.$router.push('/usercenter')\n                }\n              }\n            } else {\n              this.$notification.error({\n                message: '登录失败',\n                description: res.message || '用户名或密码错误，请检查后重试',\n                placement: 'topRight',\n                duration: 4,\n                style: {\n                  width: '380px',\n                  marginTop: '101px',\n                  borderRadius: '8px',\n                  boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                }\n              })\n              this.handleChangeCheckCode() // 刷新验证码\n            }\n          }).catch(async (err) => {\n            this.loginLoading = false\n\n            // 检查是否是登录冲突错误\n            if (err.response && err.response.data && err.response.data.code === 4002) {\n              console.log('检测到用户名密码登录冲突，显示确认弹窗')\n              const conflictInfo = err.response.data.result\n\n              // 创建强制登录函数\n              const forceLoginFn = async () => {\n                const forceLoginParams = {\n                  ...loginParams,\n                  loginType: 'force' // 修改登录类型为强制登录\n                }\n                console.log('用户名密码强制登录数据:', forceLoginParams)\n                return await login(forceLoginParams)\n              }\n\n              try {\n                // 显示登录冲突确认弹窗\n                const forceLoginResponse = await handleLoginConflict(conflictInfo, forceLoginFn)\n\n                if (forceLoginResponse && (forceLoginResponse.code === 200 || forceLoginResponse.code === '200')) {\n                  // 强制登录成功，执行登录成功的逻辑\n                  this.$notification.success({\n                    message: '登录成功',\n                    description: '欢迎回来！正在跳转到个人中心...',\n                    placement: 'topRight',\n                    duration: 3\n                  })\n\n                  // 存储登录信息\n                  const result = forceLoginResponse.result\n                  const userInfo = result.userInfo\n                  Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)\n                  Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)\n                  Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)\n                  Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)\n\n                  // 跳转逻辑\n                  const redirectPath = this.$route.query.redirect\n                  if (redirectPath) {\n                    this.$router.push(redirectPath)\n                  } else {\n                    this.$router.push('/usercenter')\n                  }\n                } else {\n                  throw new Error((forceLoginResponse && forceLoginResponse.message) || '强制登录失败')\n                }\n              } catch (conflictError) {\n                if (conflictError.message === 'USER_CANCELLED') {\n                  // 用户取消登录\n                  console.log('用户取消用户名密码强制登录')\n                  this.handleChangeCheckCode() // 刷新验证码\n                  return\n                } else {\n                  this.$notification.error({\n                    message: '登录失败',\n                    description: conflictError.message || '强制登录失败',\n                    placement: 'topRight',\n                    duration: 4\n                  })\n                  this.handleChangeCheckCode() // 刷新验证码\n                }\n              }\n            } else {\n              // 其他错误，显示原有的错误处理\n              this.$notification.error({\n                message: '登录失败',\n                description: err.message || '网络连接异常，请检查网络后重试',\n                placement: 'topRight',\n                duration: 4,\n                style: {\n                  width: '380px',\n                  marginTop: '101px',\n                  borderRadius: '8px',\n                  boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                }\n              })\n              this.handleChangeCheckCode() // 刷新验证码\n            }\n          })\n        }\n      })\n    },\n\n    handleForgotPassword() {\n      this.$notification.info({\n        message: '忘记密码',\n        description: '忘记密码功能正在开发中，敬请期待...',\n        placement: 'topRight',\n        duration: 3,\n        style: {\n          width: '350px',\n          marginTop: '101px',\n          borderRadius: '8px',\n          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n        }\n      })\n      // TODO: 跳转到忘记密码页面\n    },\n\n    handleSocialLogin(type) {\n      const typeMap = {\n        wechat: '微信',\n        qq: 'QQ',\n        alipay: '支付宝'\n      }\n      this.$message.info(`${typeMap[type]}登录功能开发中...`)\n      // TODO: 实现第三方登录\n    },\n\n\n\n    // 检查URL中的邀请码（静默处理）\n    checkInviteCode() {\n      // 支持两种参数格式：ref（推广链接）和 invite（邀请码）\n      const refCode = this.$route.query.ref\n      const inviteCode = this.$route.query.invite\n      const finalInviteCode = refCode || inviteCode\n      \n      if (finalInviteCode) {\n        this.inviteCodeFromUrl = finalInviteCode\n        // 静默记录邀请码，不显示给用户\n        console.log('检测到推荐码:', finalInviteCode, '来源:', refCode ? 'ref参数' : 'invite参数')\n      }\n    },\n\n    // 登录方式切换\n    switchLoginType(type) {\n      this.loginType = type\n\n      // 重置验证码倒计时\n      this.smsCountdown = 0\n      this.emailCountdown = 0\n\n      if (type === 'wechat') {\n        this.generateWechatLoginQrCode()\n      }\n    },\n\n    // 手机号登录（自动注册）\n    handlePhoneLogin(e) {\n      e.preventDefault()\n      this.phoneLoginForm.validateFields(async (err, values) => {\n        if (!err) {\n          this.phoneLoginLoading = true\n\n          try {\n            // 先检查手机号是否已注册\n            const checkResponse = await checkUsername(values.phone, 'phone')\n\n            if (checkResponse.success) {\n              // 手机号未注册，自动注册（无密码账户）\n              await this.autoRegisterAndLogin('phone', values)\n            } else {\n              // 手机号已注册，直接登录\n              await this.loginWithSmsCode(values)\n            }\n          } catch (error) {\n            this.phoneLoginLoading = false\n            this.$notification.error({\n              message: '登录失败',\n              description: error.message || '登录过程中发生错误',\n              placement: 'topRight',\n              duration: 4\n            })\n          }\n        }\n      })\n    },\n\n    // 邮箱登录（自动注册）\n    handleEmailLogin(e) {\n      e.preventDefault()\n      this.emailLoginForm.validateFields(async (err, values) => {\n        if (!err) {\n          this.emailLoginLoading = true\n\n          try {\n            // 先检查邮箱是否已注册\n            const checkResponse = await checkUsername(values.email, 'email')\n\n            if (checkResponse.success) {\n              // 邮箱未注册，自动注册（无密码账户）\n              await this.autoRegisterAndLogin('email', values)\n            } else {\n              // 邮箱已注册，直接登录\n              await this.loginWithEmailCode(values)\n            }\n          } catch (error) {\n            this.emailLoginLoading = false\n            this.$notification.error({\n              message: '登录失败',\n              description: error.message || '登录过程中发生错误',\n              placement: 'topRight',\n              duration: 4\n            })\n          }\n        }\n      })\n    },\n\n    // 自动注册并登录（无密码账户）\n    async autoRegisterAndLogin(type, values) {\n      try {\n        // 为无密码账户生成符合要求的随机密码\n        const randomPassword = this.generateSecurePassword()\n\n        // 构建注册数据\n        const registerData = {\n          type: type,\n          [type]: values[type], // phone 或 email\n          verifyCode: values[type === 'phone' ? 'smsCode' : 'emailCode'],\n          // 生成随机密码（用户不需要知道）\n          password: randomPassword,\n          confirmPassword: randomPassword,\n          inviteCode: this.inviteCodeFromUrl, // 静默携带邀请码\n          inviteSource: this.inviteCodeFromUrl ? 'link' : null\n        }\n\n        console.log('自动注册数据:', registerData)\n\n        // 调用注册接口\n        const registerResponse = await register(registerData)\n\n        if (registerResponse.success) {\n          // 注册成功，现在需要自动登录获取token\n          console.log('注册成功，用户ID:', registerResponse.result)\n\n          // 使用生成的密码进行自动登录\n          await this.performAutoLogin(type, values, randomPassword)\n        } else {\n          throw new Error(registerResponse.message || '注册失败')\n        }\n      } catch (error) {\n        throw error\n      } finally {\n        this.phoneLoginLoading = false\n        this.emailLoginLoading = false\n      }\n    },\n\n    // 使用短信验证码登录\n    async loginWithSmsCode(values) {\n      try {\n        // 构建登录数据\n        const loginData = {\n          mobile: values.phone,\n          captcha: values.smsCode,\n          loginType: 'website' // 标识为官网用户登录\n        }\n\n        console.log('短信验证码登录:', loginData)\n\n        // 调用短信验证码登录接口\n        const loginResponse = await phoneLogin(loginData)\n\n        if (loginResponse.success) {\n          // 登录成功，处理token和用户信息\n          await this.handleLoginSuccess(loginResponse.result)\n        } else {\n          // 检查是否是登录冲突错误\n          if (loginResponse.code === 4002) {\n            console.log('检测到手机号登录冲突，显示确认弹窗')\n            const conflictInfo = loginResponse.result\n\n            // 创建强制登录函数\n            const forceLoginFn = async () => {\n              const forceLoginData = {\n                ...loginData,\n                loginType: 'force' // 修改登录类型为强制登录\n              }\n              console.log('手机号强制登录数据:', forceLoginData)\n              return await phoneLogin(forceLoginData)\n            }\n\n            try {\n              // 显示登录冲突确认弹窗\n              const forceLoginResponse = await handleLoginConflict(conflictInfo, forceLoginFn)\n\n              if (forceLoginResponse && forceLoginResponse.success) {\n                // 强制登录成功\n                await this.handleLoginSuccess(forceLoginResponse.result)\n              } else {\n                throw new Error((forceLoginResponse && forceLoginResponse.message) || '强制登录失败')\n              }\n            } catch (conflictError) {\n              if (conflictError.message === 'USER_CANCELLED') {\n                // 用户取消登录\n                console.log('用户取消手机号强制登录')\n                return\n              } else {\n                throw conflictError\n              }\n            }\n          } else {\n            throw new Error(loginResponse.message || '登录失败')\n          }\n        }\n      } catch (error) {\n        throw error\n      } finally {\n        this.phoneLoginLoading = false\n      }\n    },\n\n    // 使用邮箱验证码登录\n    async loginWithEmailCode(values) {\n      try {\n        // 构建登录数据\n        const loginData = {\n          email: values.email,\n          emailCode: values.emailCode,\n          loginType: 'website' // 标识为官网用户登录\n        }\n\n        console.log('邮箱验证码登录:', loginData)\n\n        // 调用邮箱验证码登录接口\n        const loginResponse = await emailLogin(loginData)\n\n        if (loginResponse.success) {\n          // 登录成功，处理token和用户信息\n          await this.handleLoginSuccess(loginResponse.result)\n        } else {\n          // 检查是否是登录冲突错误\n          if (loginResponse.code === 4002) {\n            console.log('检测到邮箱登录冲突，显示确认弹窗')\n            const conflictInfo = loginResponse.result\n\n            // 创建强制登录函数\n            const forceLoginFn = async () => {\n              const forceLoginData = {\n                ...loginData,\n                loginType: 'force' // 修改登录类型为强制登录\n              }\n              console.log('邮箱强制登录数据:', forceLoginData)\n              return await emailLogin(forceLoginData)\n            }\n\n            try {\n              // 显示登录冲突确认弹窗\n              const forceLoginResponse = await handleLoginConflict(conflictInfo, forceLoginFn)\n\n              if (forceLoginResponse && forceLoginResponse.success) {\n                // 强制登录成功\n                await this.handleLoginSuccess(forceLoginResponse.result)\n              } else {\n                throw new Error((forceLoginResponse && forceLoginResponse.message) || '强制登录失败')\n              }\n            } catch (conflictError) {\n              if (conflictError.message === 'USER_CANCELLED') {\n                // 用户取消登录\n                console.log('用户取消邮箱强制登录')\n                return\n              } else {\n                throw conflictError\n              }\n            }\n          } else {\n            throw new Error(loginResponse.message || '登录失败')\n          }\n        }\n      } catch (error) {\n        throw error\n      } finally {\n        this.emailLoginLoading = false\n      }\n    },\n\n    // 处理登录成功\n    async handleLoginSuccess(result) {\n      try {\n        // 存储token和用户信息\n        Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)\n        Vue.ls.set(USER_NAME, result.userInfo.username, 7 * 24 * 60 * 60 * 1000)\n        Vue.ls.set(USER_INFO, result.userInfo, 7 * 24 * 60 * 60 * 1000)\n        Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)\n\n        // 显示登录成功消息\n        this.$notification.success({\n          message: '登录成功',\n          description: `欢迎回来，${result.userInfo.realname || result.userInfo.username}！`,\n          placement: 'topRight',\n          duration: 3\n        })\n\n        // 跳转到目标页面\n        const redirect = this.$route.query.redirect || '/'\n        this.$router.push(redirect)\n      } catch (error) {\n        console.error('处理登录成功失败:', error)\n        throw new Error('登录后处理失败')\n      }\n    },\n\n    // 发送登录短信验证码\n    async sendLoginSmsCode() {\n      const phone = this.phoneLoginForm.getFieldValue('phone')\n      if (!phone) {\n        this.$message.error('请先输入手机号')\n        return\n      }\n\n      if (!/^1[3-9]\\d{9}$/.test(phone)) {\n        this.$message.error('手机号格式不正确')\n        return\n      }\n\n      this.smsCodeSending = true\n      try {\n        const response = await sendSmsCode(phone, 'register')\n\n        if (response.success) {\n          this.$message.success('验证码发送成功，请查收短信')\n          this.startSmsCountdown()\n        } else {\n          this.$message.error(response.message || '验证码发送失败')\n        }\n      } catch (error) {\n        console.error('发送短信验证码失败:', error)\n        this.$message.error('验证码发送失败，请稍后重试')\n      } finally {\n        this.smsCodeSending = false\n      }\n    },\n\n    // 发送登录邮箱验证码\n    async sendLoginEmailCode() {\n      const email = this.emailLoginForm.getFieldValue('email')\n      if (!email) {\n        this.$message.error('请先输入邮箱')\n        return\n      }\n\n      if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n        this.$message.error('邮箱格式不正确')\n        return\n      }\n\n      this.emailCodeSending = true\n      try {\n        const response = await sendEmailCode(email, 'register')\n\n        if (response.success) {\n          this.$message.success('验证码发送成功，请查收邮件')\n          this.startEmailCountdown()\n        } else {\n          this.$message.error(response.message || '验证码发送失败')\n        }\n      } catch (error) {\n        console.error('发送邮箱验证码失败:', error)\n        this.$message.error('验证码发送失败，请稍后重试')\n      } finally {\n        this.emailCodeSending = false\n      }\n    },\n\n    // 短信验证码倒计时\n    startSmsCountdown() {\n      this.smsCountdown = 60\n      const timer = setInterval(() => {\n        this.smsCountdown--\n        if (this.smsCountdown <= 0) {\n          clearInterval(timer)\n        }\n      }, 1000)\n    },\n\n    // 邮箱验证码倒计时\n    startEmailCountdown() {\n      this.emailCountdown = 60\n      const timer = setInterval(() => {\n        this.emailCountdown--\n        if (this.emailCountdown <= 0) {\n          clearInterval(timer)\n        }\n      }, 1000)\n    },\n\n    // 生成微信登录二维码\n    async generateWechatLoginQrCode() {\n      try {\n        const response = await generateWechatQrCode('login', this.inviteCodeFromUrl)\n        if (response.success) {\n          this.wechatLoginQrCode = response.result.qrCodeUrl\n        } else {\n          this.$message.error('生成微信二维码失败')\n        }\n      } catch (error) {\n        console.error('生成微信二维码失败:', error)\n        this.$message.error('生成微信二维码失败')\n      }\n    },\n\n    // 生成符合要求的安全密码（至少8位，包含字母和数字）\n    generateSecurePassword() {\n      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'\n      const numbers = '0123456789'\n      const allChars = letters + numbers\n\n      let password = ''\n\n      // 确保至少包含一个字母和一个数字\n      password += letters.charAt(Math.floor(Math.random() * letters.length))\n      password += numbers.charAt(Math.floor(Math.random() * numbers.length))\n\n      // 生成剩余的6位字符\n      for (let i = 0; i < 10; i++) {\n        password += allChars.charAt(Math.floor(Math.random() * allChars.length))\n      }\n\n      // 打乱字符顺序\n      return password.split('').sort(() => Math.random() - 0.5).join('')\n    },\n\n    // 注册成功后自动登录\n    async performAutoLogin(type, values, password) {\n      try {\n        // 先获取验证码图片\n        this.handleChangeCheckCode()\n\n        // 构建登录参数 - 完全按照正常登录的格式\n        const username = values[type] // phone 或 email\n        const user = encryption(username, this.encryptedString.key, this.encryptedString.iv)\n        const pwd = encryption(password, this.encryptedString.key, this.encryptedString.iv)\n\n        const loginParams = {\n          username: user,\n          password: pwd,\n          captcha: 'AUTO_LOGIN_2025', // 使用特殊验证码绕过验证\n          checkKey: this.currdatetime,\n          remember_me: true,\n          loginType: 'website'\n        }\n\n        console.log('自动登录参数:', { username, loginType: 'auto', checkKey: this.currdatetime })\n\n        const loginResponse = await login(loginParams)\n\n        if (loginResponse.code === 200 || loginResponse.code === '200') {\n          // 登录成功提示\n          this.$notification.success({\n            message: '欢迎加入智界AIGC！',\n            description: `您已成功注册并登录，账户已创建为无密码模式，今后可直接使用${type === 'phone' ? '手机号' : '邮箱'}验证码登录！`,\n            placement: 'topRight',\n            duration: 6\n          })\n\n          // 存储登录信息\n          const result = loginResponse.result\n          const userInfo = result.userInfo\n          Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)\n          Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)\n          Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)\n          Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)\n\n          // 延迟跳转\n          setTimeout(() => {\n            this.$router.push('/usercenter')\n          }, 1500)\n        } else {\n          throw new Error(loginResponse.message || '自动登录失败')\n        }\n      } catch (error) {\n        console.error('自动登录失败:', error)\n        this.$notification.error({\n          message: '注册成功，但自动登录失败',\n          description: '请手动使用验证码登录',\n          placement: 'topRight',\n          duration: 4\n        })\n      }\n    },\n\n\n\n\n\n\n\n\n\n    // 初始化页面动画\n    initAnimations() {\n      // ✅ 创建主时间线，确保动画流畅连贯\n      const tl = gsap.timeline()\n\n      // ✅ 左侧信息区域动画 - 从初始状态开始\n      tl.to(this.$refs.loginInfo, {\n        duration: 0.8,\n        x: 0,\n        opacity: 1,\n        ease: \"power3.out\"\n      })\n\n      // ✅ 右侧登录表单动画 - 与左侧稍微重叠\n      tl.to(this.$refs.loginContainer, {\n        duration: 0.8,\n        x: 0,\n        opacity: 1,\n        ease: \"power3.out\"\n      }, \"-=0.6\") // 提前0.6秒开始，创造重叠效果\n\n      // ✅ 特性列表依次出现 - 更流畅的时序\n      tl.to(\".feature-item\", {\n        duration: 0.5,\n        y: 0,\n        opacity: 1,\n        stagger: 0.08, // 减少间隔，更流畅\n        ease: \"power2.out\"\n      }, \"-=0.4\") // 与右侧动画重叠\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 主容器 */\n.website-login {\n  min-height: 100vh;\n  position: relative;\n  overflow: hidden;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n}\n\n/* 动态背景 */\n.login-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 0;\n}\n\n.bg-animated-grid {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image:\n    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),\n    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);\n  background-size: 60px 60px;\n  animation: gridMove 30s linear infinite;\n}\n\n@keyframes gridMove {\n  0% { transform: translate(0, 0); }\n  100% { transform: translate(60px, 60px); }\n}\n\n.bg-floating-elements {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image:\n    radial-gradient(circle at 15% 25%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),\n    radial-gradient(circle at 85% 75%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),\n    radial-gradient(circle at 45% 55%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);\n  animation: float 12s ease-in-out infinite;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px) rotate(0deg); }\n  50% { transform: translateY(-20px) rotate(90deg); }\n}\n\n.bg-gradient-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(255, 255, 255, 0.3) 0%,\n    rgba(59, 130, 246, 0.05) 25%,\n    rgba(139, 92, 246, 0.05) 50%,\n    rgba(16, 185, 129, 0.05) 75%,\n    rgba(255, 255, 255, 0.2) 100%\n  );\n}\n\n\n\n/* 主要内容区域 */\n.login-main {\n  display: flex;\n  min-height: 100vh;\n  position: relative;\n  z-index: 1;\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 8rem 2rem 2rem; /* ✅ 增加顶部间距到8rem，给login-info更多距离页头的空间 */\n}\n\n/* 左侧信息展示 */\n.login-info {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 3rem 2rem; /* ✅ 增加内边距，让内容与容器边缘有更多距离 */\n  background: rgba(255, 255, 255, 0.6);\n  backdrop-filter: blur(20px);\n  border-radius: 24px;\n  margin: 1rem 1rem 2rem; /* ✅ 增加顶部margin，与页头保持更好的距离 */\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  /* ✅ 初始状态设置为不可见，避免闪烁 */\n  opacity: 0;\n  transform: translateX(-50px);\n}\n\n.info-content {\n  max-width: 600px;\n  color: #374151;\n}\n\n.brand-showcase {\n  text-align: center;\n  margin-bottom: 4rem;\n}\n\n.brand-logo-large {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n/* Login页面Logo容器样式 */\n.login-logo-container {\n  width: 80px;\n  height: 80px;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.4);\n  animation: logoFloat 3s ease-in-out infinite;\n}\n\n.login-logo-image {\n  width: 100% !important;\n  height: 100% !important;\n  object-fit: cover;\n  border-radius: 20px;\n}\n\n/* Login页面Fallback样式 */\n.login-logo-fallback {\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 2.5rem;\n}\n\n@keyframes logoFloat {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n.brand-title {\n  font-size: 3rem;\n  font-weight: 800;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #10b981 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin: 0;\n}\n\n.brand-slogan {\n  font-size: 1.2rem;\n  color: #6b7280;\n  margin: 0;\n}\n\n/* 特性展示 */\n.feature-highlights {\n  display: grid;\n  gap: 2rem;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  padding: 1.5rem;\n  background: rgba(255, 255, 255, 0.8);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  border-radius: 16px;\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  /* ✅ 初始状态设置为不可见，避免闪烁 */\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.feature-item:hover {\n  background: rgba(255, 255, 255, 0.95);\n  border-color: rgba(59, 130, 246, 0.2);\n  transform: translateX(10px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.feature-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.2rem;\n  flex-shrink: 0;\n}\n\n.feature-text h3 {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0 0 0.5rem 0;\n}\n\n.feature-text p {\n  font-size: 0.9rem;\n  color: #6b7280;\n  margin: 0;\n  line-height: 1.5;\n}\n\n/* 右侧登录容器 */\n.login-container {\n  flex: 1;\n  max-width: 600px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n  margin: 0 1rem 2rem; /* 移除顶部margin，只保留底部和左右 */\n  /* ✅ 初始状态设置为不可见，避免闪烁 */\n  opacity: 0;\n  transform: translateX(50px);\n}\n\n.login-card {\n  width: 100%;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 24px;\n  padding: 3rem;\n  box-shadow:\n    0 20px 40px rgba(0, 0, 0, 0.1),\n    0 0 0 1px rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n}\n\n/* 登录头部 */\n.login-header {\n  text-align: center;\n  margin-bottom: 2.5rem;\n}\n\n.login-title {\n  font-size: 2rem;\n  font-weight: 800;\n  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin: 0 0 1rem 0;\n}\n\n.login-subtitle {\n  color: #64748b;\n  font-size: 1rem;\n  line-height: 1.6;\n  margin: 0;\n}\n\n/* 登录方式切换Tab */\n.auth-tabs {\n  margin-bottom: 2rem;\n}\n\n.tab-buttons {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 0.5rem;\n  background: #f8fafc;\n  padding: 0.25rem;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n}\n\n.tab-btn {\n  padding: 0.75rem 0.5rem;\n  border: none;\n  background: transparent;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.85rem;\n  font-weight: 500;\n  color: #64748b;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  min-width: 0;\n  flex: 1;\n}\n\n.tab-text {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 100%;\n}\n\n.tab-btn.active {\n  background: white;\n  color: #3b82f6;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  font-weight: 600;\n}\n\n.tab-btn:hover {\n  color: #3b82f6;\n}\n\n.tab-btn .anticon {\n  font-size: 1rem;\n}\n\n/* 登录表单 */\n.login-form {\n  margin-top: 0;\n}\n\n.login-content {\n  animation: fadeInUp 0.3s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 快速登录顶部 */\n.quick-login-top {\n  margin-bottom: 2rem;\n}\n\n.quick-title {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 1rem;\n  text-align: center;\n}\n\n.social-buttons-horizontal {\n  display: flex;\n  gap: 1rem;\n}\n\n.social-btn-large {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.75rem;\n  padding: 1rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  background: #ffffff;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n.social-btn-large:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.social-btn-large.wechat:hover {\n  border-color: #07c160;\n  color: #07c160;\n  background: rgba(7, 193, 96, 0.05);\n}\n\n.social-btn-large.qq:hover {\n  border-color: #12b7f5;\n  color: #12b7f5;\n  background: rgba(18, 183, 245, 0.05);\n}\n\n.social-btn-large .anticon {\n  font-size: 1.2rem;\n}\n\n/* 分割线 */\n.divider-with-text {\n  text-align: center;\n  margin: 2rem 0;\n  position: relative;\n}\n\n.divider-with-text::before {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, #e5e7eb, transparent);\n}\n\n.divider-with-text span {\n  background: #ffffff;\n  padding: 0 1.5rem;\n  color: #9ca3af;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n/* 输入组 */\n.input-group {\n  margin-bottom: 1.5rem;\n}\n\n.clean-input {\n  border-radius: 12px !important;\n  border: 2px solid #e5e7eb !important;\n  transition: all 0.3s ease !important;\n  background: #ffffff !important;\n}\n\n.clean-input:focus {\n  border-color: #3b82f6 !important;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;\n}\n\n.clean-input:hover {\n  border-color: #9ca3af !important;\n}\n\n/* 验证码行 */\n.captcha-row {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n.captcha-input {\n  flex: 1;\n}\n\n.captcha-image-container {\n  position: relative;\n  cursor: pointer;\n  border-radius: 12px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.captcha-image-container:hover {\n  transform: scale(1.02);\n}\n\n.captcha-image {\n  width: 120px;\n  height: 48px;\n  border-radius: 12px;\n  border: 2px solid #e5e7eb;\n  transition: all 0.3s ease;\n  display: block;\n}\n\n.captcha-refresh-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(59, 130, 246, 0.8);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  opacity: 0;\n  transition: all 0.3s ease;\n  border-radius: 12px;\n}\n\n.captcha-image-container:hover .captcha-refresh-overlay {\n  opacity: 1;\n}\n\n/* 验证码行样式 */\n.verify-code-row {\n  display: flex;\n  gap: 0.75rem;\n  align-items: center;\n}\n\n.verify-code-input {\n  flex: 1;\n}\n\n.send-code-btn {\n  border-radius: 8px;\n  white-space: nowrap;\n  min-width: 120px;\n}\n\n.send-code-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* 微信登录样式 */\n.wechat-login-container {\n  text-align: center;\n  padding: 2rem 0;\n}\n\n.wechat-qr-section {\n  margin-bottom: 2rem;\n}\n\n.qr-code-container {\n  margin-bottom: 1.5rem;\n}\n\n.qr-code-image {\n  width: 200px;\n  height: 200px;\n  border-radius: 12px;\n  border: 2px solid #e2e8f0;\n}\n\n.qr-loading {\n  padding: 3rem;\n  color: #64748b;\n}\n\n.qr-loading p {\n  margin-top: 1rem;\n  margin-bottom: 0;\n}\n\n.qr-instructions h4 {\n  margin: 0 0 1rem 0;\n  color: #1e293b;\n  font-weight: 600;\n}\n\n.qr-instructions p {\n  margin: 0.5rem 0;\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n.invite-tip {\n  color: #3b82f6 !important;\n  font-weight: 500;\n}\n\n/* 提示信息样式 */\n.phone-login-tip,\n.email-login-tip {\n  margin-top: 1rem;\n}\n\n.phone-login-tip .ant-alert,\n.email-login-tip .ant-alert {\n  border-radius: 8px;\n}\n\n/* 登录选项 */\n.login-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 1.5rem 0;\n}\n\n.remember-me {\n  color: #6b7280;\n  font-size: 0.9rem;\n}\n\n.forgot-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.forgot-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 登录按钮 */\n.login-button-item {\n  margin-bottom: 0;\n}\n\n.login-submit-button {\n  height: 52px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.login-submit-button::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.login-submit-button:hover::before {\n  left: 100%;\n}\n\n.login-submit-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);\n}\n\n/* 注册部分 */\n.register-section {\n  text-align: center;\n  margin-top: 2rem;\n  padding-top: 2rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n.register-text {\n  color: #6b7280;\n  font-size: 0.9rem;\n  margin-right: 0.5rem;\n}\n\n.register-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.register-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 登录/注册切换Tab */\n.auth-tabs {\n  margin-bottom: 2rem;\n}\n\n.tab-buttons {\n  display: flex;\n  background: rgba(243, 244, 246, 0.8);\n  border-radius: 12px;\n  padding: 4px;\n  gap: 4px;\n}\n\n.tab-btn {\n  flex: 1;\n  padding: 12px 24px;\n  border: none;\n  background: transparent;\n  border-radius: 8px;\n  font-size: 0.95rem;\n  font-weight: 600;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.tab-btn.active {\n  background: white;\n  color: #3b82f6;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.tab-btn:hover:not(.active) {\n  color: #374151;\n  background: rgba(255, 255, 255, 0.5);\n}\n\n/* 注册表单 */\n.register-form {\n  margin-top: 0;\n}\n\n/* 注册方式切换 */\n.register-type-tabs {\n  margin-bottom: 2rem;\n}\n\n.type-tab-buttons {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.type-tab-btn {\n  flex: 1;\n  min-width: 120px;\n  padding: 12px 16px;\n  border: 2px solid rgba(59, 130, 246, 0.2);\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 12px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n}\n\n.type-tab-btn.active {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  border-color: transparent;\n  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);\n}\n\n.type-tab-btn:hover:not(.active) {\n  border-color: rgba(59, 130, 246, 0.4);\n  background: rgba(255, 255, 255, 0.95);\n  color: #374151;\n}\n\n/* 注册内容区域 */\n.register-content {\n  margin-top: 1.5rem;\n}\n\n.register-form-content {\n  margin: 0;\n}\n\n/* 验证码输入行 */\n.verify-code-row {\n  display: flex;\n  gap: 12px;\n  align-items: center;\n}\n\n.verify-code-input {\n  flex: 1;\n}\n\n.send-code-btn {\n  flex-shrink: 0;\n  min-width: 120px;\n  border-radius: 8px;\n  border: 2px solid #3b82f6;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  font-weight: 600;\n  transition: all 0.3s ease;\n}\n\n.send-code-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);\n  border-color: #2563eb;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.send-code-btn:disabled {\n  background: #e5e7eb;\n  border-color: #e5e7eb;\n  color: #9ca3af;\n  cursor: not-allowed;\n}\n\n/* 用户协议 */\n.agreement-section {\n  margin: 1.5rem 0;\n  padding: 1rem;\n  background: rgba(59, 130, 246, 0.05);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  border-radius: 12px;\n}\n\n.agreement-checkbox {\n  color: #374151;\n  font-size: 0.9rem;\n  line-height: 1.6;\n}\n\n.agreement-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 600;\n  transition: color 0.3s ease;\n}\n\n.agreement-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 注册按钮 */\n.submit-section {\n  margin: 2rem 0 1.5rem;\n}\n\n.register-submit-btn {\n  width: 100%;\n  height: 48px;\n  border-radius: 12px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border: none;\n  font-size: 1rem;\n  font-weight: 700;\n  letter-spacing: 0.5px;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);\n}\n\n.register-submit-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);\n}\n\n.register-submit-btn:disabled {\n  background: #e5e7eb;\n  color: #9ca3af;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n/* 登录提示 */\n.login-prompt {\n  text-align: center;\n  margin-top: 1.5rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid rgba(229, 231, 235, 0.8);\n}\n\n.login-text {\n  color: #6b7280;\n  font-size: 0.9rem;\n  margin-right: 0.5rem;\n}\n\n.login-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.login-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 微信注册 */\n.wechat-register-container {\n  text-align: center;\n  padding: 2rem 0;\n}\n\n.wechat-qr-section {\n  margin-bottom: 2rem;\n}\n\n.qr-code-container {\n  display: inline-block;\n  padding: 20px;\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n  margin-bottom: 1.5rem;\n}\n\n.qr-code-image {\n  width: 200px;\n  height: 200px;\n  border-radius: 8px;\n}\n\n.qr-loading {\n  width: 200px;\n  height: 200px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #6b7280;\n}\n\n.qr-instructions h4 {\n  color: #374151;\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin: 0 0 1rem 0;\n}\n\n.qr-instructions p {\n  color: #6b7280;\n  font-size: 0.9rem;\n  margin: 0.5rem 0;\n  line-height: 1.5;\n}\n\n.invite-info {\n  margin: 1.5rem 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .login-main {\n    max-width: 1200px;\n    padding: 0 1.5rem;\n  }\n}\n\n@media (max-width: 1024px) {\n  .login-main {\n    flex-direction: column;\n    max-width: 800px;\n    padding: 6rem 1rem 2rem; /* 平板端保持顶部padding */\n  }\n\n  .login-info {\n    margin: 0 0 1rem; /* 减少margin */\n    padding: 2rem;\n  }\n\n  .login-container {\n    margin: 0 0 1rem; /* 减少margin */\n    max-width: none;\n  }\n\n  .brand-title {\n    font-size: 2.5rem;\n  }\n\n  .feature-highlights {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 1rem;\n  }\n\n  .tab-buttons {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 768px) {\n\n  .login-main {\n    padding: 5rem 0.5rem 2rem; /* 移动端减少顶部padding */\n  }\n\n  .login-info {\n    margin: 0 0 1rem; /* 移动端减少margin */\n    padding: 1.5rem;\n  }\n\n  .login-container {\n    margin: 0 0 1rem; /* 移动端减少margin */\n    padding: 1rem;\n  }\n\n  .login-card {\n    padding: 2rem 1.5rem;\n  }\n\n  .brand-title {\n    font-size: 2rem;\n  }\n\n  .login-title {\n    font-size: 1.5rem;\n  }\n\n  .feature-highlights {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .feature-item {\n    padding: 1rem;\n  }\n\n  .feature-item:hover {\n    transform: translateY(-2px);\n  }\n\n  .social-buttons-horizontal {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .captcha-row {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .captcha-image-container {\n    align-self: stretch;\n  }\n\n  .captcha-image {\n    width: 100%;\n  }\n\n  .tab-buttons {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .tab-btn {\n    font-size: 0.8rem;\n    padding: 0.5rem;\n  }\n\n  .verify-code-row {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .send-code-btn {\n    width: 100%;\n  }\n}\n\n@media (max-width: 480px) {\n\n  .login-card {\n    padding: 1.5rem 1rem;\n    border-radius: 16px;\n  }\n\n  .brand-title {\n    font-size: 1.8rem;\n  }\n\n  .login-title {\n    font-size: 1.3rem;\n  }\n\n  .input-group {\n    margin-bottom: 1rem;\n  }\n\n  .login-submit-button {\n    height: 48px;\n    font-size: 1rem;\n  }\n\n  .social-btn-large {\n    padding: 0.75rem;\n    font-size: 0.8rem;\n  }\n\n  .social-btn-large .anticon {\n    font-size: 1rem;\n  }\n\n  .tab-buttons {\n    grid-template-columns: 1fr;\n    gap: 0.25rem;\n  }\n\n  .tab-btn {\n    flex-direction: row;\n    justify-content: center;\n    gap: 0.5rem;\n    font-size: 0.8rem;\n  }\n\n  .qr-code-image {\n    width: 150px;\n    height: 150px;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/auth"}]}