{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue?vue&type=style&index=0&id=42c31eb0&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue", "mtime": 1753512620053}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 主容器 */\n.website-login {\n  min-height: 100vh;\n  position: relative;\n  overflow: hidden;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n}\n\n/* 动态背景 */\n.login-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 0;\n}\n\n.bg-animated-grid {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image:\n    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),\n    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);\n  background-size: 60px 60px;\n  animation: gridMove 30s linear infinite;\n}\n\n@keyframes gridMove {\n  0% { transform: translate(0, 0); }\n  100% { transform: translate(60px, 60px); }\n}\n\n.bg-floating-elements {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image:\n    radial-gradient(circle at 15% 25%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),\n    radial-gradient(circle at 85% 75%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),\n    radial-gradient(circle at 45% 55%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);\n  animation: float 12s ease-in-out infinite;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px) rotate(0deg); }\n  50% { transform: translateY(-20px) rotate(90deg); }\n}\n\n.bg-gradient-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(255, 255, 255, 0.3) 0%,\n    rgba(59, 130, 246, 0.05) 25%,\n    rgba(139, 92, 246, 0.05) 50%,\n    rgba(16, 185, 129, 0.05) 75%,\n    rgba(255, 255, 255, 0.2) 100%\n  );\n}\n\n\n\n/* 主要内容区域 */\n.login-main {\n  display: flex;\n  min-height: 100vh;\n  position: relative;\n  z-index: 1;\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 8rem 2rem 2rem; /* ✅ 增加顶部间距到8rem，给login-info更多距离页头的空间 */\n}\n\n/* 左侧信息展示 */\n.login-info {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 3rem 2rem; /* ✅ 增加内边距，让内容与容器边缘有更多距离 */\n  background: rgba(255, 255, 255, 0.6);\n  backdrop-filter: blur(20px);\n  border-radius: 24px;\n  margin: 1rem 1rem 2rem; /* ✅ 增加顶部margin，与页头保持更好的距离 */\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  /* ✅ 初始状态设置为不可见，避免闪烁 */\n  opacity: 0;\n  transform: translateX(-50px);\n}\n\n.info-content {\n  max-width: 600px;\n  color: #374151;\n}\n\n.brand-showcase {\n  text-align: center;\n  margin-bottom: 4rem;\n}\n\n.brand-logo-large {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n/* Login页面Logo容器样式 */\n.login-logo-container {\n  width: 80px;\n  height: 80px;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.4);\n  animation: logoFloat 3s ease-in-out infinite;\n}\n\n.login-logo-image {\n  width: 100% !important;\n  height: 100% !important;\n  object-fit: cover;\n  border-radius: 20px;\n}\n\n/* Login页面Fallback样式 */\n.login-logo-fallback {\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 2.5rem;\n}\n\n@keyframes logoFloat {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n.brand-title {\n  font-size: 3rem;\n  font-weight: 800;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #10b981 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin: 0;\n}\n\n.brand-slogan {\n  font-size: 1.2rem;\n  color: #6b7280;\n  margin: 0;\n}\n\n/* 特性展示 */\n.feature-highlights {\n  display: grid;\n  gap: 2rem;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  padding: 1.5rem;\n  background: rgba(255, 255, 255, 0.8);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  border-radius: 16px;\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  /* ✅ 初始状态设置为不可见，避免闪烁 */\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.feature-item:hover {\n  background: rgba(255, 255, 255, 0.95);\n  border-color: rgba(59, 130, 246, 0.2);\n  transform: translateX(10px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.feature-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.2rem;\n  flex-shrink: 0;\n}\n\n.feature-text h3 {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0 0 0.5rem 0;\n}\n\n.feature-text p {\n  font-size: 0.9rem;\n  color: #6b7280;\n  margin: 0;\n  line-height: 1.5;\n}\n\n/* 右侧登录容器 */\n.login-container {\n  flex: 1;\n  max-width: 600px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n  margin: 0 1rem 2rem; /* 移除顶部margin，只保留底部和左右 */\n  /* ✅ 初始状态设置为不可见，避免闪烁 */\n  opacity: 0;\n  transform: translateX(50px);\n}\n\n.login-card {\n  width: 100%;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 24px;\n  padding: 3rem;\n  box-shadow:\n    0 20px 40px rgba(0, 0, 0, 0.1),\n    0 0 0 1px rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n}\n\n/* 登录头部 */\n.login-header {\n  text-align: center;\n  margin-bottom: 2.5rem;\n}\n\n.login-title {\n  font-size: 2rem;\n  font-weight: 800;\n  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin: 0 0 1rem 0;\n}\n\n.login-subtitle {\n  color: #64748b;\n  font-size: 1rem;\n  line-height: 1.6;\n  margin: 0;\n}\n\n/* 登录方式切换Tab */\n.auth-tabs {\n  margin-bottom: 2rem;\n}\n\n.tab-buttons {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 0.5rem;\n  background: #f8fafc;\n  padding: 0.25rem;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n}\n\n.tab-btn {\n  padding: 0.75rem 0.5rem;\n  border: none;\n  background: transparent;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.85rem;\n  font-weight: 500;\n  color: #64748b;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  min-width: 0;\n  flex: 1;\n}\n\n.tab-text {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 100%;\n}\n\n.tab-btn.active {\n  background: white;\n  color: #3b82f6;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  font-weight: 600;\n}\n\n.tab-btn:hover {\n  color: #3b82f6;\n}\n\n.tab-btn .anticon {\n  font-size: 1rem;\n}\n\n/* 登录表单 */\n.login-form {\n  margin-top: 0;\n}\n\n.login-content {\n  animation: fadeInUp 0.3s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 快速登录顶部 */\n.quick-login-top {\n  margin-bottom: 2rem;\n}\n\n.quick-title {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 1rem;\n  text-align: center;\n}\n\n.social-buttons-horizontal {\n  display: flex;\n  gap: 1rem;\n}\n\n.social-btn-large {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.75rem;\n  padding: 1rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  background: #ffffff;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n.social-btn-large:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.social-btn-large.wechat:hover {\n  border-color: #07c160;\n  color: #07c160;\n  background: rgba(7, 193, 96, 0.05);\n}\n\n.social-btn-large.qq:hover {\n  border-color: #12b7f5;\n  color: #12b7f5;\n  background: rgba(18, 183, 245, 0.05);\n}\n\n.social-btn-large .anticon {\n  font-size: 1.2rem;\n}\n\n/* 分割线 */\n.divider-with-text {\n  text-align: center;\n  margin: 2rem 0;\n  position: relative;\n}\n\n.divider-with-text::before {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, #e5e7eb, transparent);\n}\n\n.divider-with-text span {\n  background: #ffffff;\n  padding: 0 1.5rem;\n  color: #9ca3af;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n/* 输入组 */\n.input-group {\n  margin-bottom: 1.5rem;\n}\n\n.clean-input {\n  border-radius: 12px !important;\n  border: 2px solid #e5e7eb !important;\n  transition: all 0.3s ease !important;\n  background: #ffffff !important;\n}\n\n.clean-input:focus {\n  border-color: #3b82f6 !important;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;\n}\n\n.clean-input:hover {\n  border-color: #9ca3af !important;\n}\n\n/* 验证码行 */\n.captcha-row {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n.captcha-input {\n  flex: 1;\n}\n\n.captcha-image-container {\n  position: relative;\n  cursor: pointer;\n  border-radius: 12px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.captcha-image-container:hover {\n  transform: scale(1.02);\n}\n\n.captcha-image {\n  width: 120px;\n  height: 48px;\n  border-radius: 12px;\n  border: 2px solid #e5e7eb;\n  transition: all 0.3s ease;\n  display: block;\n}\n\n.captcha-refresh-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(59, 130, 246, 0.8);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  opacity: 0;\n  transition: all 0.3s ease;\n  border-radius: 12px;\n}\n\n.captcha-image-container:hover .captcha-refresh-overlay {\n  opacity: 1;\n}\n\n/* 验证码行样式 */\n.verify-code-row {\n  display: flex;\n  gap: 0.75rem;\n  align-items: center;\n}\n\n.verify-code-input {\n  flex: 1;\n}\n\n.send-code-btn {\n  border-radius: 8px;\n  white-space: nowrap;\n  min-width: 120px;\n}\n\n.send-code-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* 微信登录样式 */\n.wechat-login-container {\n  text-align: center;\n  padding: 2rem 0;\n}\n\n.wechat-qr-section {\n  margin-bottom: 2rem;\n}\n\n.qr-code-container {\n  margin-bottom: 1.5rem;\n}\n\n.qr-code-image {\n  width: 200px;\n  height: 200px;\n  border-radius: 12px;\n  border: 2px solid #e2e8f0;\n}\n\n.qr-loading {\n  padding: 3rem;\n  color: #64748b;\n}\n\n.qr-loading p {\n  margin-top: 1rem;\n  margin-bottom: 0;\n}\n\n.qr-instructions h4 {\n  margin: 0 0 1rem 0;\n  color: #1e293b;\n  font-weight: 600;\n}\n\n.qr-instructions p {\n  margin: 0.5rem 0;\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n.invite-tip {\n  color: #3b82f6 !important;\n  font-weight: 500;\n}\n\n/* 提示信息样式 */\n.phone-login-tip,\n.email-login-tip {\n  margin-top: 1rem;\n}\n\n.phone-login-tip .ant-alert,\n.email-login-tip .ant-alert {\n  border-radius: 8px;\n}\n\n/* 登录选项 */\n.login-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 1.5rem 0;\n}\n\n.remember-me {\n  color: #6b7280;\n  font-size: 0.9rem;\n}\n\n.forgot-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.forgot-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 登录按钮 */\n.login-button-item {\n  margin-bottom: 0;\n}\n\n.login-submit-button {\n  height: 52px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.login-submit-button::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.login-submit-button:hover::before {\n  left: 100%;\n}\n\n.login-submit-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);\n}\n\n/* 注册部分 */\n.register-section {\n  text-align: center;\n  margin-top: 2rem;\n  padding-top: 2rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n.register-text {\n  color: #6b7280;\n  font-size: 0.9rem;\n  margin-right: 0.5rem;\n}\n\n.register-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.register-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 登录/注册切换Tab */\n.auth-tabs {\n  margin-bottom: 2rem;\n}\n\n.tab-buttons {\n  display: flex;\n  background: rgba(243, 244, 246, 0.8);\n  border-radius: 12px;\n  padding: 4px;\n  gap: 4px;\n}\n\n.tab-btn {\n  flex: 1;\n  padding: 12px 24px;\n  border: none;\n  background: transparent;\n  border-radius: 8px;\n  font-size: 0.95rem;\n  font-weight: 600;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.tab-btn.active {\n  background: white;\n  color: #3b82f6;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.tab-btn:hover:not(.active) {\n  color: #374151;\n  background: rgba(255, 255, 255, 0.5);\n}\n\n/* 注册表单 */\n.register-form {\n  margin-top: 0;\n}\n\n/* 注册方式切换 */\n.register-type-tabs {\n  margin-bottom: 2rem;\n}\n\n.type-tab-buttons {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.type-tab-btn {\n  flex: 1;\n  min-width: 120px;\n  padding: 12px 16px;\n  border: 2px solid rgba(59, 130, 246, 0.2);\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 12px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n}\n\n.type-tab-btn.active {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  border-color: transparent;\n  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);\n}\n\n.type-tab-btn:hover:not(.active) {\n  border-color: rgba(59, 130, 246, 0.4);\n  background: rgba(255, 255, 255, 0.95);\n  color: #374151;\n}\n\n/* 注册内容区域 */\n.register-content {\n  margin-top: 1.5rem;\n}\n\n.register-form-content {\n  margin: 0;\n}\n\n/* 验证码输入行 */\n.verify-code-row {\n  display: flex;\n  gap: 12px;\n  align-items: center;\n}\n\n.verify-code-input {\n  flex: 1;\n}\n\n.send-code-btn {\n  flex-shrink: 0;\n  min-width: 120px;\n  border-radius: 8px;\n  border: 2px solid #3b82f6;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  font-weight: 600;\n  transition: all 0.3s ease;\n}\n\n.send-code-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);\n  border-color: #2563eb;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.send-code-btn:disabled {\n  background: #e5e7eb;\n  border-color: #e5e7eb;\n  color: #9ca3af;\n  cursor: not-allowed;\n}\n\n/* 用户协议 */\n.agreement-section {\n  margin: 1.5rem 0;\n  padding: 1rem;\n  background: rgba(59, 130, 246, 0.05);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  border-radius: 12px;\n}\n\n.agreement-checkbox {\n  color: #374151;\n  font-size: 0.9rem;\n  line-height: 1.6;\n}\n\n.agreement-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 600;\n  transition: color 0.3s ease;\n}\n\n.agreement-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 注册按钮 */\n.submit-section {\n  margin: 2rem 0 1.5rem;\n}\n\n.register-submit-btn {\n  width: 100%;\n  height: 48px;\n  border-radius: 12px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border: none;\n  font-size: 1rem;\n  font-weight: 700;\n  letter-spacing: 0.5px;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);\n}\n\n.register-submit-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);\n}\n\n.register-submit-btn:disabled {\n  background: #e5e7eb;\n  color: #9ca3af;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n/* 登录提示 */\n.login-prompt {\n  text-align: center;\n  margin-top: 1.5rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid rgba(229, 231, 235, 0.8);\n}\n\n.login-text {\n  color: #6b7280;\n  font-size: 0.9rem;\n  margin-right: 0.5rem;\n}\n\n.login-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.login-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 微信注册 */\n.wechat-register-container {\n  text-align: center;\n  padding: 2rem 0;\n}\n\n.wechat-qr-section {\n  margin-bottom: 2rem;\n}\n\n.qr-code-container {\n  display: inline-block;\n  padding: 20px;\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n  margin-bottom: 1.5rem;\n}\n\n.qr-code-image {\n  width: 200px;\n  height: 200px;\n  border-radius: 8px;\n}\n\n.qr-loading {\n  width: 200px;\n  height: 200px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #6b7280;\n}\n\n.qr-instructions h4 {\n  color: #374151;\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin: 0 0 1rem 0;\n}\n\n.qr-instructions p {\n  color: #6b7280;\n  font-size: 0.9rem;\n  margin: 0.5rem 0;\n  line-height: 1.5;\n}\n\n.invite-info {\n  margin: 1.5rem 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .login-main {\n    max-width: 1200px;\n    padding: 0 1.5rem;\n  }\n}\n\n@media (max-width: 1024px) {\n  .login-main {\n    flex-direction: column;\n    max-width: 800px;\n    padding: 6rem 1rem 2rem; /* 平板端保持顶部padding */\n  }\n\n  .login-info {\n    margin: 0 0 1rem; /* 减少margin */\n    padding: 2rem;\n  }\n\n  .login-container {\n    margin: 0 0 1rem; /* 减少margin */\n    max-width: none;\n  }\n\n  .brand-title {\n    font-size: 2.5rem;\n  }\n\n  .feature-highlights {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 1rem;\n  }\n\n  .tab-buttons {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 768px) {\n\n  .login-main {\n    padding: 5rem 0.5rem 2rem; /* 移动端减少顶部padding */\n  }\n\n  .login-info {\n    margin: 0 0 1rem; /* 移动端减少margin */\n    padding: 1.5rem;\n  }\n\n  .login-container {\n    margin: 0 0 1rem; /* 移动端减少margin */\n    padding: 1rem;\n  }\n\n  .login-card {\n    padding: 2rem 1.5rem;\n  }\n\n  .brand-title {\n    font-size: 2rem;\n  }\n\n  .login-title {\n    font-size: 1.5rem;\n  }\n\n  .feature-highlights {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .feature-item {\n    padding: 1rem;\n  }\n\n  .feature-item:hover {\n    transform: translateY(-2px);\n  }\n\n  .social-buttons-horizontal {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .captcha-row {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .captcha-image-container {\n    align-self: stretch;\n  }\n\n  .captcha-image {\n    width: 100%;\n  }\n\n  .tab-buttons {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .tab-btn {\n    font-size: 0.8rem;\n    padding: 0.5rem;\n  }\n\n  .verify-code-row {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .send-code-btn {\n    width: 100%;\n  }\n}\n\n@media (max-width: 480px) {\n\n  .login-card {\n    padding: 1.5rem 1rem;\n    border-radius: 16px;\n  }\n\n  .brand-title {\n    font-size: 1.8rem;\n  }\n\n  .login-title {\n    font-size: 1.3rem;\n  }\n\n  .input-group {\n    margin-bottom: 1rem;\n  }\n\n  .login-submit-button {\n    height: 48px;\n    font-size: 1rem;\n  }\n\n  .social-btn-large {\n    padding: 0.75rem;\n    font-size: 0.8rem;\n  }\n\n  .social-btn-large .anticon {\n    font-size: 1rem;\n  }\n\n  .tab-buttons {\n    grid-template-columns: 1fr;\n    gap: 0.25rem;\n  }\n\n  .tab-btn {\n    flex-direction: row;\n    justify-content: center;\n    gap: 0.5rem;\n    font-size: 0.8rem;\n  }\n\n  .qr-code-image {\n    width: 150px;\n    height: 150px;\n  }\n}\n", {"version": 3, "sources": ["Login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4p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file": "Login.vue", "sourceRoot": "src/views/website/auth", "sourcesContent": ["<template>\n  <div class=\"website-login\">\n    <!-- 动态背景 -->\n    <div class=\"login-background\">\n      <div class=\"bg-animated-grid\"></div>\n      <div class=\"bg-floating-elements\"></div>\n      <div class=\"bg-gradient-overlay\"></div>\n    </div>\n\n    <!-- 复用官网页头组件 -->\n    <WebsiteHeader />\n\n    <!-- 主要内容区域 -->\n    <div class=\"login-main\">\n      <!-- 左侧信息展示 -->\n      <div class=\"login-info\" ref=\"loginInfo\">\n        <div class=\"info-content\">\n          <div class=\"brand-showcase\">\n            <div class=\"brand-logo-large\">\n              <LogoImage\n                size=\"large\"\n                :hover=\"false\"\n                container-class=\"login-logo-container\"\n                image-class=\"login-logo-image\"\n                fallback-class=\"login-logo-fallback\"\n              />\n              <h1 class=\"brand-title\">智界AIGC</h1>\n            </div>\n            <p class=\"brand-slogan\">AI驱动的内容生成平台</p>\n          </div>\n\n          <div class=\"feature-highlights\">\n            <div class=\"feature-item\" v-for=\"(feature, index) in features\" :key=\"index\">\n              <div class=\"feature-icon\">\n                <a-icon :type=\"feature.icon\" />\n              </div>\n              <div class=\"feature-text\">\n                <h3>{{ feature.title }}</h3>\n                <p>{{ feature.description }}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 右侧登录表单 -->\n      <div class=\"login-container\" ref=\"loginContainer\">\n        <div class=\"login-card\">\n          <!-- 登录头部 -->\n          <div class=\"login-header\">\n            <h2 class=\"login-title\">欢迎使用智界AIGC</h2>\n            <p class=\"login-subtitle\">{{ inviteCodeFromUrl ? '您正在通过邀请链接登录' : '选择您的登录方式，开启AI创作之旅' }}</p>\n          </div>\n\n          <!-- 登录方式切换Tab -->\n          <div class=\"auth-tabs\">\n            <div class=\"tab-buttons\">\n              <button\n                :class=\"['tab-btn', { active: loginType === 'phone' }]\"\n                @click=\"switchLoginType('phone')\"\n              >\n                <a-icon type=\"mobile\" />\n                <span class=\"tab-text\">手机号</span>\n              </button>\n              <button\n                :class=\"['tab-btn', { active: loginType === 'email' }]\"\n                @click=\"switchLoginType('email')\"\n              >\n                <a-icon type=\"mail\" />\n                <span class=\"tab-text\">邮箱</span>\n              </button>\n              <!-- 🔐 微信登录（暂时隐藏，待后续配置） -->\n              <!-- <button\n                :class=\"['tab-btn', { active: loginType === 'wechat' }]\"\n                @click=\"switchLoginType('wechat')\"\n              >\n                <a-icon type=\"wechat\" />\n                <span class=\"tab-text\">微信</span>\n              </button> -->\n              <button\n                :class=\"['tab-btn', { active: loginType === 'password' }]\"\n                @click=\"switchLoginType('password')\"\n              >\n                <a-icon type=\"lock\" />\n                <span class=\"tab-text\">密码登录</span>\n              </button>\n            </div>\n          </div>\n\n          <!-- 登录表单 -->\n          <div class=\"login-form\">\n            <!-- 密码登录 -->\n            <div v-if=\"loginType === 'password'\" class=\"login-content\">\n              <a-form :form=\"form\" @submit=\"handleSubmit\" class=\"account-login-form\">\n              <!-- 用户名输入 -->\n              <div class=\"input-group\">\n                <a-form-item>\n                  <a-input\n                    v-decorator=\"['username', { rules: [{ required: true, message: '请输入用户名或邮箱' }] }]\"\n                    size=\"large\"\n                    placeholder=\"用户名或邮箱\"\n                    class=\"clean-input\"\n                  >\n                    <a-icon slot=\"prefix\" type=\"user\" />\n                  </a-input>\n                </a-form-item>\n              </div>\n\n              <!-- 密码输入 -->\n              <div class=\"input-group\">\n                <a-form-item>\n                  <a-input-password\n                    v-decorator=\"['password', { rules: [{ required: true, message: '请输入密码' }] }]\"\n                    size=\"large\"\n                    placeholder=\"密码\"\n                    class=\"clean-input\"\n                  >\n                    <a-icon slot=\"prefix\" type=\"lock\" />\n                  </a-input-password>\n                </a-form-item>\n              </div>\n\n              <!-- 验证码 -->\n              <div class=\"input-group\">\n                <a-form-item>\n                  <div class=\"captcha-row\">\n                    <a-input\n                      v-decorator=\"['inputCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"\n                      size=\"large\"\n                      placeholder=\"验证码\"\n                      class=\"clean-input captcha-input\"\n                    >\n                      <a-icon slot=\"prefix\" type=\"safety-certificate\" />\n                    </a-input>\n                    <div class=\"captcha-image-container\" @click=\"handleChangeCheckCode\">\n                      <img\n                        :src=\"randCodeImage\"\n                        class=\"captcha-image\"\n                        alt=\"验证码\"\n                      />\n                      <div class=\"captcha-refresh-overlay\">\n                        <a-icon type=\"reload\" />\n                      </div>\n                    </div>\n                  </div>\n                </a-form-item>\n              </div>\n\n              <!-- 登录选项 -->\n              <div class=\"login-options\">\n                <a-checkbox v-model=\"rememberMe\" class=\"remember-me\">\n                  记住我\n                </a-checkbox>\n                <a class=\"forgot-link\" @click=\"handleForgotPassword\">\n                  忘记密码？\n                </a>\n              </div>\n\n              <!-- 登录按钮 -->\n              <a-form-item class=\"login-button-item\">\n                <a-button\n                  type=\"primary\"\n                  html-type=\"submit\"\n                  size=\"large\"\n                  :loading=\"loginLoading\"\n                  class=\"login-submit-button\"\n                  block\n                >\n                  <span v-if=\"!loginLoading\">登录</span>\n                  <span v-else>登录中...</span>\n                </a-button>\n              </a-form-item>\n              </a-form>\n            </div>\n\n            <!-- 手机号登录 -->\n            <div v-if=\"loginType === 'phone'\" class=\"login-content\">\n              <a-form :form=\"phoneLoginForm\" @submit=\"handlePhoneLogin\" class=\"phone-login-form\">\n                <!-- 手机号输入 -->\n                <div class=\"input-group\">\n                  <a-form-item>\n                    <a-input\n                      v-decorator=\"['phone', { rules: [\n                        { required: true, message: '请输入手机号' },\n                        { pattern: /^1[3-9]\\d{9}$/, message: '手机号格式不正确' }\n                      ] }]\"\n                      size=\"large\"\n                      placeholder=\"请输入手机号\"\n                      class=\"clean-input\"\n                    >\n                      <a-icon slot=\"prefix\" type=\"mobile\" />\n                    </a-input>\n                  </a-form-item>\n                </div>\n\n                <!-- 短信验证码 -->\n                <div class=\"input-group\">\n                  <a-form-item>\n                    <div class=\"verify-code-row\">\n                      <a-input\n                        v-decorator=\"['smsCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"\n                        size=\"large\"\n                        placeholder=\"请输入短信验证码\"\n                        class=\"clean-input verify-code-input\"\n                      >\n                        <a-icon slot=\"prefix\" type=\"safety-certificate\" />\n                      </a-input>\n                      <a-button\n                        :disabled=\"smsCodeSending || smsCountdown > 0\"\n                        @click=\"sendLoginSmsCode\"\n                        class=\"send-code-btn\"\n                        size=\"large\"\n                      >\n                        {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '发送验证码' }}\n                      </a-button>\n                    </div>\n                  </a-form-item>\n                </div>\n\n                <!-- 登录按钮 -->\n                <a-form-item class=\"login-button-item\">\n                  <a-button\n                    type=\"primary\"\n                    html-type=\"submit\"\n                    size=\"large\"\n                    :loading=\"phoneLoginLoading\"\n                    class=\"login-submit-button\"\n                    block\n                  >\n                    <span v-if=\"!phoneLoginLoading\">登录</span>\n                    <span v-else>登录中...</span>\n                  </a-button>\n                </a-form-item>\n\n                <!-- 提示信息 -->\n                <div class=\"phone-login-tip\">\n                  <a-alert\n                    message=\"手机号登录说明\"\n                    description=\"首次使用手机号登录将自动为您创建账户，无需设置密码\"\n                    type=\"info\"\n                    show-icon\n                  />\n                </div>\n              </a-form>\n            </div>\n\n            <!-- 邮箱登录 -->\n            <div v-if=\"loginType === 'email'\" class=\"login-content\">\n              <a-form :form=\"emailLoginForm\" @submit=\"handleEmailLogin\" class=\"email-login-form\">\n                <!-- 邮箱输入 -->\n                <div class=\"input-group\">\n                  <a-form-item>\n                    <a-input\n                      v-decorator=\"['email', { rules: [\n                        { required: true, message: '请输入邮箱' },\n                        { type: 'email', message: '邮箱格式不正确' }\n                      ] }]\"\n                      size=\"large\"\n                      placeholder=\"请输入邮箱\"\n                      class=\"clean-input\"\n                    >\n                      <a-icon slot=\"prefix\" type=\"mail\" />\n                    </a-input>\n                  </a-form-item>\n                </div>\n\n                <!-- 邮箱验证码 -->\n                <div class=\"input-group\">\n                  <a-form-item>\n                    <div class=\"verify-code-row\">\n                      <a-input\n                        v-decorator=\"['emailCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"\n                        size=\"large\"\n                        placeholder=\"请输入邮箱验证码\"\n                        class=\"clean-input verify-code-input\"\n                      >\n                        <a-icon slot=\"prefix\" type=\"safety-certificate\" />\n                      </a-input>\n                      <a-button\n                        :disabled=\"emailCodeSending || emailCountdown > 0\"\n                        @click=\"sendLoginEmailCode\"\n                        class=\"send-code-btn\"\n                        size=\"large\"\n                      >\n                        {{ emailCountdown > 0 ? `${emailCountdown}s后重发` : '发送验证码' }}\n                      </a-button>\n                    </div>\n                  </a-form-item>\n                </div>\n\n                <!-- 登录按钮 -->\n                <a-form-item class=\"login-button-item\">\n                  <a-button\n                    type=\"primary\"\n                    html-type=\"submit\"\n                    size=\"large\"\n                    :loading=\"emailLoginLoading\"\n                    class=\"login-submit-button\"\n                    block\n                  >\n                    <span v-if=\"!emailLoginLoading\">登录</span>\n                    <span v-else>登录中...</span>\n                  </a-button>\n                </a-form-item>\n\n                <!-- 提示信息 -->\n                <div class=\"email-login-tip\">\n                  <a-alert\n                    message=\"邮箱登录说明\"\n                    description=\"首次使用邮箱登录将自动为您创建账户，无需设置密码\"\n                    type=\"info\"\n                    show-icon\n                  />\n                </div>\n              </a-form>\n            </div>\n\n            <!-- 微信登录 -->\n            <div v-if=\"loginType === 'wechat'\" class=\"login-content\">\n              <div class=\"wechat-login-container\">\n                <div class=\"wechat-qr-section\">\n                  <div class=\"qr-code-container\">\n                    <img :src=\"wechatLoginQrCode\" alt=\"微信登录二维码\" class=\"qr-code-image\" v-if=\"wechatLoginQrCode\" />\n                    <div class=\"qr-loading\" v-else>\n                      <a-spin size=\"large\" />\n                      <p>正在生成二维码...</p>\n                    </div>\n                  </div>\n                  <div class=\"qr-instructions\">\n                    <h4>使用微信扫码登录</h4>\n                    <p>1. 打开微信扫一扫</p>\n                    <p>2. 扫描上方二维码</p>\n                    <p>3. 确认登录</p>\n                    <p v-if=\"inviteCodeFromUrl\" class=\"invite-tip\">* 您正在通过邀请链接登录</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { login, getCaptcha, phoneLogin, emailLogin } from '@/api/login'\nimport { encryption, getEncryptedString } from '@/utils/encryption/aesEncrypt'\nimport { getAction } from '@/api/manage'\nimport { gsap } from 'gsap'\nimport WebsiteHeader from '@/components/website/WebsiteHeader.vue'\nimport LogoImage from '@/components/common/LogoImage.vue'\nimport { ACCESS_TOKEN, USER_NAME, USER_INFO, UI_CACHE_DB_DICT_DATA } from '@/store/mutation-types'\nimport {\n  checkUsername,\n  sendSmsCode,\n  sendEmailCode,\n  register,\n  generateWechatQrCode\n} from '@/api/register'\nimport Vue from 'vue'\nimport { handleLoginConflict } from '@/utils/loginConflictHandler'\n\nexport default {\n  name: 'WebsiteLogin',\n  components: {\n    WebsiteHeader,\n    LogoImage\n  },\n  data() {\n    return {\n      // 登录相关\n      form: this.$form.createForm(this),\n      phoneLoginForm: this.$form.createForm(this),\n      emailLoginForm: this.$form.createForm(this),\n      loginLoading: false,\n      phoneLoginLoading: false,\n      emailLoginLoading: false,\n      rememberMe: false,\n      randCodeImage: '',\n      currdatetime: new Date().getTime(),\n      encryptedString: '',\n\n      // 登录方式切换\n      loginType: 'phone', // password, phone, email, wechat - 默认手机号登录\n\n      // 验证码相关\n      smsCodeSending: false,\n      smsCountdown: 0,\n      emailCodeSending: false,\n      emailCountdown: 0,\n\n      // 邀请码（静默处理）\n      inviteCodeFromUrl: '',\n\n      // 微信登录\n      wechatLoginQrCode: '',\n\n      features: [\n        {\n          icon: 'robot',\n          title: 'AI智能创作',\n          description: '强大的AI算法，助您快速生成高质量内容'\n        },\n        {\n          icon: 'thunderbolt',\n          title: '极速响应',\n          description: '毫秒级响应速度，让创作灵感不再等待'\n        },\n        {\n          icon: 'safety-certificate',\n          title: '安全可靠',\n          description: '企业级安全保障，保护您的创作成果'\n        },\n        {\n          icon: 'global',\n          title: '全球服务',\n          description: '覆盖全球的CDN网络，随时随地畅享服务'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.getEncrypte()\n    this.handleChangeCheckCode()\n    this.initAnimations()\n    this.checkInviteCode()\n  },\n  methods: {\n    // 获取密码加密规则\n    getEncrypte() {\n      getEncryptedString().then((data) => {\n        this.encryptedString = data\n      })\n    },\n\n    // 刷新验证码\n    handleChangeCheckCode() {\n      this.currdatetime = new Date().getTime()\n      getAction(`/sys/randomImage/${this.currdatetime}`).then(res => {\n        if (res.success) {\n          this.randCodeImage = res.result\n        } else {\n          this.$message.error(res.message)\n        }\n      }).catch(() => {\n        this.$message.error('验证码加载失败')\n      })\n    },\n\n    handleSubmit(e) {\n      e.preventDefault()\n      this.form.validateFields((err, values) => {\n        if (!err) {\n          this.loginLoading = true\n          console.log('官网登录信息:', values)\n\n          // 使用真实的登录API\n          let user = encryption(values.username, this.encryptedString.key, this.encryptedString.iv)\n          let pwd = encryption(values.password, this.encryptedString.key, this.encryptedString.iv)\n          let loginParams = {\n            username: user,\n            password: pwd,\n            captcha: values.inputCode,\n            checkKey: this.currdatetime,\n            remember_me: this.rememberMe,\n            loginType: 'website' // 标识为官网用户登录\n          }\n\n          console.log(\"官网登录参数\", loginParams)\n          login(loginParams).then(async (res) => {\n            this.loginLoading = false\n            console.log(\"🔍 登录响应:\", res)\n            console.log(\"🔍 响应code:\", res.code, \"类型:\", typeof res.code)\n            if (res.code === 200 || res.code === '200') {\n              this.$notification.success({\n                message: '登录成功',\n                description: '欢迎回来！正在跳转到个人中心...',\n                placement: 'topRight',\n                duration: 3,\n                style: {\n                  width: '350px',\n                  marginTop: '101px',\n                  borderRadius: '8px',\n                  boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                }\n              })\n\n              // ✅ 存储登录信息\n              const result = res.result\n              const userInfo = result.userInfo\n              Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)\n              Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)\n              Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)\n              Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)\n\n              // ✅ 获取用户角色信息\n              try {\n                const roleRes = await getAction(\"/sys/user/getCurrentUserDeparts\")\n                if (roleRes.success) {\n                  const userRole = roleRes.result.role\n                  const departId = roleRes.result.departId\n\n                  // 存储角色信息\n                  localStorage.setItem('userRole', userRole || '')\n                  localStorage.setItem('departId', departId || '')\n\n                  // 优先处理重定向参数\n                  const redirectPath = this.$route.query.redirect\n                  console.log('🔍 登录成功，检查重定向参数:', redirectPath)\n\n                  if (redirectPath) {\n                    // 有重定向参数，直接跳转到目标页面\n                    console.log('🔄 有重定向参数，跳转到:', redirectPath)\n                    this.$router.push(redirectPath)\n                  } else {\n                    // 没有重定向参数，根据角色决定跳转\n                    if (this.isAdminRole(userRole)) {\n                      // 管理员用户，跳转到后台\n                      console.log('🔄 管理员用户，跳转到后台管理')\n                      this.$router.push('/dashboard/analysis')\n                    } else {\n                      // 普通用户，跳转到个人中心\n                      console.log('🔄 普通用户，跳转到个人中心')\n                      this.$router.push('/usercenter')\n                    }\n                  }\n                } else {\n                  // 获取角色失败，检查重定向参数\n                  const redirectPath = this.$route.query.redirect\n                  if (redirectPath) {\n                    this.$router.push(redirectPath)\n                  } else {\n                    this.$router.push('/usercenter')\n                  }\n                }\n              } catch (error) {\n                console.error('获取角色信息失败:', error)\n                // 出错时也检查重定向参数\n                const redirectPath = this.$route.query.redirect\n                if (redirectPath) {\n                  this.$router.push(redirectPath)\n                } else {\n                  this.$router.push('/usercenter')\n                }\n              }\n            } else {\n              this.$notification.error({\n                message: '登录失败',\n                description: res.message || '用户名或密码错误，请检查后重试',\n                placement: 'topRight',\n                duration: 4,\n                style: {\n                  width: '380px',\n                  marginTop: '101px',\n                  borderRadius: '8px',\n                  boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                }\n              })\n              this.handleChangeCheckCode() // 刷新验证码\n            }\n          }).catch(async (err) => {\n            this.loginLoading = false\n\n            // 检查是否是登录冲突错误\n            if (err.response && err.response.data && err.response.data.code === 4002) {\n              console.log('检测到用户名密码登录冲突，显示确认弹窗')\n              const conflictInfo = err.response.data.result\n\n              // 创建强制登录函数\n              const forceLoginFn = async () => {\n                const forceLoginParams = {\n                  ...loginParams,\n                  loginType: 'force' // 修改登录类型为强制登录\n                }\n                console.log('用户名密码强制登录数据:', forceLoginParams)\n                return await login(forceLoginParams)\n              }\n\n              try {\n                // 显示登录冲突确认弹窗\n                const forceLoginResponse = await handleLoginConflict(conflictInfo, forceLoginFn)\n\n                if (forceLoginResponse && (forceLoginResponse.code === 200 || forceLoginResponse.code === '200')) {\n                  // 强制登录成功，执行登录成功的逻辑\n                  this.$notification.success({\n                    message: '登录成功',\n                    description: '欢迎回来！正在跳转到个人中心...',\n                    placement: 'topRight',\n                    duration: 3\n                  })\n\n                  // 存储登录信息\n                  const result = forceLoginResponse.result\n                  const userInfo = result.userInfo\n                  Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)\n                  Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)\n                  Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)\n                  Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)\n\n                  // 跳转逻辑\n                  const redirectPath = this.$route.query.redirect\n                  if (redirectPath) {\n                    this.$router.push(redirectPath)\n                  } else {\n                    this.$router.push('/usercenter')\n                  }\n                } else {\n                  throw new Error((forceLoginResponse && forceLoginResponse.message) || '强制登录失败')\n                }\n              } catch (conflictError) {\n                if (conflictError.message === 'USER_CANCELLED') {\n                  // 用户取消登录\n                  console.log('用户取消用户名密码强制登录')\n                  this.handleChangeCheckCode() // 刷新验证码\n                  return\n                } else {\n                  this.$notification.error({\n                    message: '登录失败',\n                    description: conflictError.message || '强制登录失败',\n                    placement: 'topRight',\n                    duration: 4\n                  })\n                  this.handleChangeCheckCode() // 刷新验证码\n                }\n              }\n            } else {\n              // 其他错误，显示原有的错误处理\n              this.$notification.error({\n                message: '登录失败',\n                description: err.message || '网络连接异常，请检查网络后重试',\n                placement: 'topRight',\n                duration: 4,\n                style: {\n                  width: '380px',\n                  marginTop: '101px',\n                  borderRadius: '8px',\n                  boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                }\n              })\n              this.handleChangeCheckCode() // 刷新验证码\n            }\n          })\n        }\n      })\n    },\n\n    handleForgotPassword() {\n      this.$notification.info({\n        message: '忘记密码',\n        description: '忘记密码功能正在开发中，敬请期待...',\n        placement: 'topRight',\n        duration: 3,\n        style: {\n          width: '350px',\n          marginTop: '101px',\n          borderRadius: '8px',\n          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n        }\n      })\n      // TODO: 跳转到忘记密码页面\n    },\n\n    handleSocialLogin(type) {\n      const typeMap = {\n        wechat: '微信',\n        qq: 'QQ',\n        alipay: '支付宝'\n      }\n      this.$message.info(`${typeMap[type]}登录功能开发中...`)\n      // TODO: 实现第三方登录\n    },\n\n\n\n    // 检查URL中的邀请码（静默处理）\n    checkInviteCode() {\n      // 支持两种参数格式：ref（推广链接）和 invite（邀请码）\n      const refCode = this.$route.query.ref\n      const inviteCode = this.$route.query.invite\n      const finalInviteCode = refCode || inviteCode\n      \n      if (finalInviteCode) {\n        this.inviteCodeFromUrl = finalInviteCode\n        // 静默记录邀请码，不显示给用户\n        console.log('检测到推荐码:', finalInviteCode, '来源:', refCode ? 'ref参数' : 'invite参数')\n      }\n    },\n\n    // 登录方式切换\n    switchLoginType(type) {\n      this.loginType = type\n\n      // 重置验证码倒计时\n      this.smsCountdown = 0\n      this.emailCountdown = 0\n\n      if (type === 'wechat') {\n        this.generateWechatLoginQrCode()\n      }\n    },\n\n    // 手机号登录（自动注册）\n    handlePhoneLogin(e) {\n      e.preventDefault()\n      this.phoneLoginForm.validateFields(async (err, values) => {\n        if (!err) {\n          this.phoneLoginLoading = true\n\n          try {\n            // 先检查手机号是否已注册\n            const checkResponse = await checkUsername(values.phone, 'phone')\n\n            if (checkResponse.success) {\n              // 手机号未注册，自动注册（无密码账户）\n              await this.autoRegisterAndLogin('phone', values)\n            } else {\n              // 手机号已注册，直接登录\n              await this.loginWithSmsCode(values)\n            }\n          } catch (error) {\n            this.phoneLoginLoading = false\n            this.$notification.error({\n              message: '登录失败',\n              description: error.message || '登录过程中发生错误',\n              placement: 'topRight',\n              duration: 4\n            })\n          }\n        }\n      })\n    },\n\n    // 邮箱登录（自动注册）\n    handleEmailLogin(e) {\n      e.preventDefault()\n      this.emailLoginForm.validateFields(async (err, values) => {\n        if (!err) {\n          this.emailLoginLoading = true\n\n          try {\n            // 先检查邮箱是否已注册\n            const checkResponse = await checkUsername(values.email, 'email')\n\n            if (checkResponse.success) {\n              // 邮箱未注册，自动注册（无密码账户）\n              await this.autoRegisterAndLogin('email', values)\n            } else {\n              // 邮箱已注册，直接登录\n              await this.loginWithEmailCode(values)\n            }\n          } catch (error) {\n            this.emailLoginLoading = false\n            this.$notification.error({\n              message: '登录失败',\n              description: error.message || '登录过程中发生错误',\n              placement: 'topRight',\n              duration: 4\n            })\n          }\n        }\n      })\n    },\n\n    // 自动注册并登录（无密码账户）\n    async autoRegisterAndLogin(type, values) {\n      try {\n        // 为无密码账户生成符合要求的随机密码\n        const randomPassword = this.generateSecurePassword()\n\n        // 构建注册数据\n        const registerData = {\n          type: type,\n          [type]: values[type], // phone 或 email\n          verifyCode: values[type === 'phone' ? 'smsCode' : 'emailCode'],\n          // 生成随机密码（用户不需要知道）\n          password: randomPassword,\n          confirmPassword: randomPassword,\n          inviteCode: this.inviteCodeFromUrl, // 静默携带邀请码\n          inviteSource: this.inviteCodeFromUrl ? 'link' : null\n        }\n\n        console.log('自动注册数据:', registerData)\n\n        // 调用注册接口\n        const registerResponse = await register(registerData)\n\n        if (registerResponse.success) {\n          // 注册成功，现在需要自动登录获取token\n          console.log('注册成功，用户ID:', registerResponse.result)\n\n          // 使用生成的密码进行自动登录\n          await this.performAutoLogin(type, values, randomPassword)\n        } else {\n          throw new Error(registerResponse.message || '注册失败')\n        }\n      } catch (error) {\n        throw error\n      } finally {\n        this.phoneLoginLoading = false\n        this.emailLoginLoading = false\n      }\n    },\n\n    // 使用短信验证码登录\n    async loginWithSmsCode(values) {\n      try {\n        // 构建登录数据\n        const loginData = {\n          mobile: values.phone,\n          captcha: values.smsCode,\n          loginType: 'website' // 标识为官网用户登录\n        }\n\n        console.log('短信验证码登录:', loginData)\n\n        // 调用短信验证码登录接口\n        const loginResponse = await phoneLogin(loginData)\n\n        if (loginResponse.success) {\n          // 登录成功，处理token和用户信息\n          await this.handleLoginSuccess(loginResponse.result)\n        } else {\n          // 检查是否是登录冲突错误\n          if (loginResponse.code === 4002) {\n            console.log('检测到手机号登录冲突，显示确认弹窗')\n            const conflictInfo = loginResponse.result\n\n            // 创建强制登录函数\n            const forceLoginFn = async () => {\n              const forceLoginData = {\n                ...loginData,\n                loginType: 'force' // 修改登录类型为强制登录\n              }\n              console.log('手机号强制登录数据:', forceLoginData)\n              return await phoneLogin(forceLoginData)\n            }\n\n            try {\n              // 显示登录冲突确认弹窗\n              const forceLoginResponse = await handleLoginConflict(conflictInfo, forceLoginFn)\n\n              if (forceLoginResponse && forceLoginResponse.success) {\n                // 强制登录成功\n                await this.handleLoginSuccess(forceLoginResponse.result)\n              } else {\n                throw new Error((forceLoginResponse && forceLoginResponse.message) || '强制登录失败')\n              }\n            } catch (conflictError) {\n              if (conflictError.message === 'USER_CANCELLED') {\n                // 用户取消登录\n                console.log('用户取消手机号强制登录')\n                return\n              } else {\n                throw conflictError\n              }\n            }\n          } else {\n            throw new Error(loginResponse.message || '登录失败')\n          }\n        }\n      } catch (error) {\n        throw error\n      } finally {\n        this.phoneLoginLoading = false\n      }\n    },\n\n    // 使用邮箱验证码登录\n    async loginWithEmailCode(values) {\n      try {\n        // 构建登录数据\n        const loginData = {\n          email: values.email,\n          emailCode: values.emailCode,\n          loginType: 'website' // 标识为官网用户登录\n        }\n\n        console.log('邮箱验证码登录:', loginData)\n\n        // 调用邮箱验证码登录接口\n        const loginResponse = await emailLogin(loginData)\n\n        if (loginResponse.success) {\n          // 登录成功，处理token和用户信息\n          await this.handleLoginSuccess(loginResponse.result)\n        } else {\n          // 检查是否是登录冲突错误\n          if (loginResponse.code === 4002) {\n            console.log('检测到邮箱登录冲突，显示确认弹窗')\n            const conflictInfo = loginResponse.result\n\n            // 创建强制登录函数\n            const forceLoginFn = async () => {\n              const forceLoginData = {\n                ...loginData,\n                loginType: 'force' // 修改登录类型为强制登录\n              }\n              console.log('邮箱强制登录数据:', forceLoginData)\n              return await emailLogin(forceLoginData)\n            }\n\n            try {\n              // 显示登录冲突确认弹窗\n              const forceLoginResponse = await handleLoginConflict(conflictInfo, forceLoginFn)\n\n              if (forceLoginResponse && forceLoginResponse.success) {\n                // 强制登录成功\n                await this.handleLoginSuccess(forceLoginResponse.result)\n              } else {\n                throw new Error((forceLoginResponse && forceLoginResponse.message) || '强制登录失败')\n              }\n            } catch (conflictError) {\n              if (conflictError.message === 'USER_CANCELLED') {\n                // 用户取消登录\n                console.log('用户取消邮箱强制登录')\n                return\n              } else {\n                throw conflictError\n              }\n            }\n          } else {\n            throw new Error(loginResponse.message || '登录失败')\n          }\n        }\n      } catch (error) {\n        throw error\n      } finally {\n        this.emailLoginLoading = false\n      }\n    },\n\n    // 处理登录成功\n    async handleLoginSuccess(result) {\n      try {\n        // 存储token和用户信息\n        Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)\n        Vue.ls.set(USER_NAME, result.userInfo.username, 7 * 24 * 60 * 60 * 1000)\n        Vue.ls.set(USER_INFO, result.userInfo, 7 * 24 * 60 * 60 * 1000)\n        Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)\n\n        // 显示登录成功消息\n        this.$notification.success({\n          message: '登录成功',\n          description: `欢迎回来，${result.userInfo.realname || result.userInfo.username}！`,\n          placement: 'topRight',\n          duration: 3\n        })\n\n        // 跳转到目标页面\n        const redirect = this.$route.query.redirect || '/'\n        this.$router.push(redirect)\n      } catch (error) {\n        console.error('处理登录成功失败:', error)\n        throw new Error('登录后处理失败')\n      }\n    },\n\n    // 发送登录短信验证码\n    async sendLoginSmsCode() {\n      const phone = this.phoneLoginForm.getFieldValue('phone')\n      if (!phone) {\n        this.$message.error('请先输入手机号')\n        return\n      }\n\n      if (!/^1[3-9]\\d{9}$/.test(phone)) {\n        this.$message.error('手机号格式不正确')\n        return\n      }\n\n      this.smsCodeSending = true\n      try {\n        const response = await sendSmsCode(phone, 'register')\n\n        if (response.success) {\n          this.$message.success('验证码发送成功，请查收短信')\n          this.startSmsCountdown()\n        } else {\n          this.$message.error(response.message || '验证码发送失败')\n        }\n      } catch (error) {\n        console.error('发送短信验证码失败:', error)\n        this.$message.error('验证码发送失败，请稍后重试')\n      } finally {\n        this.smsCodeSending = false\n      }\n    },\n\n    // 发送登录邮箱验证码\n    async sendLoginEmailCode() {\n      const email = this.emailLoginForm.getFieldValue('email')\n      if (!email) {\n        this.$message.error('请先输入邮箱')\n        return\n      }\n\n      if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n        this.$message.error('邮箱格式不正确')\n        return\n      }\n\n      this.emailCodeSending = true\n      try {\n        const response = await sendEmailCode(email, 'register')\n\n        if (response.success) {\n          this.$message.success('验证码发送成功，请查收邮件')\n          this.startEmailCountdown()\n        } else {\n          this.$message.error(response.message || '验证码发送失败')\n        }\n      } catch (error) {\n        console.error('发送邮箱验证码失败:', error)\n        this.$message.error('验证码发送失败，请稍后重试')\n      } finally {\n        this.emailCodeSending = false\n      }\n    },\n\n    // 短信验证码倒计时\n    startSmsCountdown() {\n      this.smsCountdown = 60\n      const timer = setInterval(() => {\n        this.smsCountdown--\n        if (this.smsCountdown <= 0) {\n          clearInterval(timer)\n        }\n      }, 1000)\n    },\n\n    // 邮箱验证码倒计时\n    startEmailCountdown() {\n      this.emailCountdown = 60\n      const timer = setInterval(() => {\n        this.emailCountdown--\n        if (this.emailCountdown <= 0) {\n          clearInterval(timer)\n        }\n      }, 1000)\n    },\n\n    // 生成微信登录二维码\n    async generateWechatLoginQrCode() {\n      try {\n        const response = await generateWechatQrCode('login', this.inviteCodeFromUrl)\n        if (response.success) {\n          this.wechatLoginQrCode = response.result.qrCodeUrl\n        } else {\n          this.$message.error('生成微信二维码失败')\n        }\n      } catch (error) {\n        console.error('生成微信二维码失败:', error)\n        this.$message.error('生成微信二维码失败')\n      }\n    },\n\n    // 生成符合要求的安全密码（至少8位，包含字母和数字）\n    generateSecurePassword() {\n      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'\n      const numbers = '0123456789'\n      const allChars = letters + numbers\n\n      let password = ''\n\n      // 确保至少包含一个字母和一个数字\n      password += letters.charAt(Math.floor(Math.random() * letters.length))\n      password += numbers.charAt(Math.floor(Math.random() * numbers.length))\n\n      // 生成剩余的6位字符\n      for (let i = 0; i < 10; i++) {\n        password += allChars.charAt(Math.floor(Math.random() * allChars.length))\n      }\n\n      // 打乱字符顺序\n      return password.split('').sort(() => Math.random() - 0.5).join('')\n    },\n\n    // 注册成功后自动登录\n    async performAutoLogin(type, values, password) {\n      try {\n        // 先获取验证码图片\n        this.handleChangeCheckCode()\n\n        // 构建登录参数 - 完全按照正常登录的格式\n        const username = values[type] // phone 或 email\n        const user = encryption(username, this.encryptedString.key, this.encryptedString.iv)\n        const pwd = encryption(password, this.encryptedString.key, this.encryptedString.iv)\n\n        const loginParams = {\n          username: user,\n          password: pwd,\n          captcha: 'AUTO_LOGIN_2025', // 使用特殊验证码绕过验证\n          checkKey: this.currdatetime,\n          remember_me: true,\n          loginType: 'website'\n        }\n\n        console.log('自动登录参数:', { username, loginType: 'auto', checkKey: this.currdatetime })\n\n        const loginResponse = await login(loginParams)\n\n        if (loginResponse.code === 200 || loginResponse.code === '200') {\n          // 登录成功提示\n          this.$notification.success({\n            message: '欢迎加入智界AIGC！',\n            description: `您已成功注册并登录，账户已创建为无密码模式，今后可直接使用${type === 'phone' ? '手机号' : '邮箱'}验证码登录！`,\n            placement: 'topRight',\n            duration: 6\n          })\n\n          // 存储登录信息\n          const result = loginResponse.result\n          const userInfo = result.userInfo\n          Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)\n          Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)\n          Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)\n          Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)\n\n          // 延迟跳转\n          setTimeout(() => {\n            this.$router.push('/usercenter')\n          }, 1500)\n        } else {\n          throw new Error(loginResponse.message || '自动登录失败')\n        }\n      } catch (error) {\n        console.error('自动登录失败:', error)\n        this.$notification.error({\n          message: '注册成功，但自动登录失败',\n          description: '请手动使用验证码登录',\n          placement: 'topRight',\n          duration: 4\n        })\n      }\n    },\n\n\n\n\n\n\n\n\n\n    // 初始化页面动画\n    initAnimations() {\n      // ✅ 创建主时间线，确保动画流畅连贯\n      const tl = gsap.timeline()\n\n      // ✅ 左侧信息区域动画 - 从初始状态开始\n      tl.to(this.$refs.loginInfo, {\n        duration: 0.8,\n        x: 0,\n        opacity: 1,\n        ease: \"power3.out\"\n      })\n\n      // ✅ 右侧登录表单动画 - 与左侧稍微重叠\n      tl.to(this.$refs.loginContainer, {\n        duration: 0.8,\n        x: 0,\n        opacity: 1,\n        ease: \"power3.out\"\n      }, \"-=0.6\") // 提前0.6秒开始，创造重叠效果\n\n      // ✅ 特性列表依次出现 - 更流畅的时序\n      tl.to(\".feature-item\", {\n        duration: 0.5,\n        y: 0,\n        opacity: 1,\n        stagger: 0.08, // 减少间隔，更流畅\n        ease: \"power2.out\"\n      }, \"-=0.4\") // 与右侧动画重叠\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 主容器 */\n.website-login {\n  min-height: 100vh;\n  position: relative;\n  overflow: hidden;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n}\n\n/* 动态背景 */\n.login-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 0;\n}\n\n.bg-animated-grid {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image:\n    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),\n    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);\n  background-size: 60px 60px;\n  animation: gridMove 30s linear infinite;\n}\n\n@keyframes gridMove {\n  0% { transform: translate(0, 0); }\n  100% { transform: translate(60px, 60px); }\n}\n\n.bg-floating-elements {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image:\n    radial-gradient(circle at 15% 25%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),\n    radial-gradient(circle at 85% 75%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),\n    radial-gradient(circle at 45% 55%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);\n  animation: float 12s ease-in-out infinite;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px) rotate(0deg); }\n  50% { transform: translateY(-20px) rotate(90deg); }\n}\n\n.bg-gradient-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(255, 255, 255, 0.3) 0%,\n    rgba(59, 130, 246, 0.05) 25%,\n    rgba(139, 92, 246, 0.05) 50%,\n    rgba(16, 185, 129, 0.05) 75%,\n    rgba(255, 255, 255, 0.2) 100%\n  );\n}\n\n\n\n/* 主要内容区域 */\n.login-main {\n  display: flex;\n  min-height: 100vh;\n  position: relative;\n  z-index: 1;\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 8rem 2rem 2rem; /* ✅ 增加顶部间距到8rem，给login-info更多距离页头的空间 */\n}\n\n/* 左侧信息展示 */\n.login-info {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 3rem 2rem; /* ✅ 增加内边距，让内容与容器边缘有更多距离 */\n  background: rgba(255, 255, 255, 0.6);\n  backdrop-filter: blur(20px);\n  border-radius: 24px;\n  margin: 1rem 1rem 2rem; /* ✅ 增加顶部margin，与页头保持更好的距离 */\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  /* ✅ 初始状态设置为不可见，避免闪烁 */\n  opacity: 0;\n  transform: translateX(-50px);\n}\n\n.info-content {\n  max-width: 600px;\n  color: #374151;\n}\n\n.brand-showcase {\n  text-align: center;\n  margin-bottom: 4rem;\n}\n\n.brand-logo-large {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n/* Login页面Logo容器样式 */\n.login-logo-container {\n  width: 80px;\n  height: 80px;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.4);\n  animation: logoFloat 3s ease-in-out infinite;\n}\n\n.login-logo-image {\n  width: 100% !important;\n  height: 100% !important;\n  object-fit: cover;\n  border-radius: 20px;\n}\n\n/* Login页面Fallback样式 */\n.login-logo-fallback {\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 2.5rem;\n}\n\n@keyframes logoFloat {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n.brand-title {\n  font-size: 3rem;\n  font-weight: 800;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #10b981 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin: 0;\n}\n\n.brand-slogan {\n  font-size: 1.2rem;\n  color: #6b7280;\n  margin: 0;\n}\n\n/* 特性展示 */\n.feature-highlights {\n  display: grid;\n  gap: 2rem;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  padding: 1.5rem;\n  background: rgba(255, 255, 255, 0.8);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  border-radius: 16px;\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  /* ✅ 初始状态设置为不可见，避免闪烁 */\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.feature-item:hover {\n  background: rgba(255, 255, 255, 0.95);\n  border-color: rgba(59, 130, 246, 0.2);\n  transform: translateX(10px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.feature-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.2rem;\n  flex-shrink: 0;\n}\n\n.feature-text h3 {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0 0 0.5rem 0;\n}\n\n.feature-text p {\n  font-size: 0.9rem;\n  color: #6b7280;\n  margin: 0;\n  line-height: 1.5;\n}\n\n/* 右侧登录容器 */\n.login-container {\n  flex: 1;\n  max-width: 600px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n  margin: 0 1rem 2rem; /* 移除顶部margin，只保留底部和左右 */\n  /* ✅ 初始状态设置为不可见，避免闪烁 */\n  opacity: 0;\n  transform: translateX(50px);\n}\n\n.login-card {\n  width: 100%;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 24px;\n  padding: 3rem;\n  box-shadow:\n    0 20px 40px rgba(0, 0, 0, 0.1),\n    0 0 0 1px rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n}\n\n/* 登录头部 */\n.login-header {\n  text-align: center;\n  margin-bottom: 2.5rem;\n}\n\n.login-title {\n  font-size: 2rem;\n  font-weight: 800;\n  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin: 0 0 1rem 0;\n}\n\n.login-subtitle {\n  color: #64748b;\n  font-size: 1rem;\n  line-height: 1.6;\n  margin: 0;\n}\n\n/* 登录方式切换Tab */\n.auth-tabs {\n  margin-bottom: 2rem;\n}\n\n.tab-buttons {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 0.5rem;\n  background: #f8fafc;\n  padding: 0.25rem;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n}\n\n.tab-btn {\n  padding: 0.75rem 0.5rem;\n  border: none;\n  background: transparent;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.85rem;\n  font-weight: 500;\n  color: #64748b;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  min-width: 0;\n  flex: 1;\n}\n\n.tab-text {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 100%;\n}\n\n.tab-btn.active {\n  background: white;\n  color: #3b82f6;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  font-weight: 600;\n}\n\n.tab-btn:hover {\n  color: #3b82f6;\n}\n\n.tab-btn .anticon {\n  font-size: 1rem;\n}\n\n/* 登录表单 */\n.login-form {\n  margin-top: 0;\n}\n\n.login-content {\n  animation: fadeInUp 0.3s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 快速登录顶部 */\n.quick-login-top {\n  margin-bottom: 2rem;\n}\n\n.quick-title {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 1rem;\n  text-align: center;\n}\n\n.social-buttons-horizontal {\n  display: flex;\n  gap: 1rem;\n}\n\n.social-btn-large {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.75rem;\n  padding: 1rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  background: #ffffff;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n.social-btn-large:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.social-btn-large.wechat:hover {\n  border-color: #07c160;\n  color: #07c160;\n  background: rgba(7, 193, 96, 0.05);\n}\n\n.social-btn-large.qq:hover {\n  border-color: #12b7f5;\n  color: #12b7f5;\n  background: rgba(18, 183, 245, 0.05);\n}\n\n.social-btn-large .anticon {\n  font-size: 1.2rem;\n}\n\n/* 分割线 */\n.divider-with-text {\n  text-align: center;\n  margin: 2rem 0;\n  position: relative;\n}\n\n.divider-with-text::before {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, #e5e7eb, transparent);\n}\n\n.divider-with-text span {\n  background: #ffffff;\n  padding: 0 1.5rem;\n  color: #9ca3af;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n/* 输入组 */\n.input-group {\n  margin-bottom: 1.5rem;\n}\n\n.clean-input {\n  border-radius: 12px !important;\n  border: 2px solid #e5e7eb !important;\n  transition: all 0.3s ease !important;\n  background: #ffffff !important;\n}\n\n.clean-input:focus {\n  border-color: #3b82f6 !important;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;\n}\n\n.clean-input:hover {\n  border-color: #9ca3af !important;\n}\n\n/* 验证码行 */\n.captcha-row {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n.captcha-input {\n  flex: 1;\n}\n\n.captcha-image-container {\n  position: relative;\n  cursor: pointer;\n  border-radius: 12px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.captcha-image-container:hover {\n  transform: scale(1.02);\n}\n\n.captcha-image {\n  width: 120px;\n  height: 48px;\n  border-radius: 12px;\n  border: 2px solid #e5e7eb;\n  transition: all 0.3s ease;\n  display: block;\n}\n\n.captcha-refresh-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(59, 130, 246, 0.8);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  opacity: 0;\n  transition: all 0.3s ease;\n  border-radius: 12px;\n}\n\n.captcha-image-container:hover .captcha-refresh-overlay {\n  opacity: 1;\n}\n\n/* 验证码行样式 */\n.verify-code-row {\n  display: flex;\n  gap: 0.75rem;\n  align-items: center;\n}\n\n.verify-code-input {\n  flex: 1;\n}\n\n.send-code-btn {\n  border-radius: 8px;\n  white-space: nowrap;\n  min-width: 120px;\n}\n\n.send-code-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* 微信登录样式 */\n.wechat-login-container {\n  text-align: center;\n  padding: 2rem 0;\n}\n\n.wechat-qr-section {\n  margin-bottom: 2rem;\n}\n\n.qr-code-container {\n  margin-bottom: 1.5rem;\n}\n\n.qr-code-image {\n  width: 200px;\n  height: 200px;\n  border-radius: 12px;\n  border: 2px solid #e2e8f0;\n}\n\n.qr-loading {\n  padding: 3rem;\n  color: #64748b;\n}\n\n.qr-loading p {\n  margin-top: 1rem;\n  margin-bottom: 0;\n}\n\n.qr-instructions h4 {\n  margin: 0 0 1rem 0;\n  color: #1e293b;\n  font-weight: 600;\n}\n\n.qr-instructions p {\n  margin: 0.5rem 0;\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n.invite-tip {\n  color: #3b82f6 !important;\n  font-weight: 500;\n}\n\n/* 提示信息样式 */\n.phone-login-tip,\n.email-login-tip {\n  margin-top: 1rem;\n}\n\n.phone-login-tip .ant-alert,\n.email-login-tip .ant-alert {\n  border-radius: 8px;\n}\n\n/* 登录选项 */\n.login-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 1.5rem 0;\n}\n\n.remember-me {\n  color: #6b7280;\n  font-size: 0.9rem;\n}\n\n.forgot-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.forgot-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 登录按钮 */\n.login-button-item {\n  margin-bottom: 0;\n}\n\n.login-submit-button {\n  height: 52px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.login-submit-button::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.login-submit-button:hover::before {\n  left: 100%;\n}\n\n.login-submit-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);\n}\n\n/* 注册部分 */\n.register-section {\n  text-align: center;\n  margin-top: 2rem;\n  padding-top: 2rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n.register-text {\n  color: #6b7280;\n  font-size: 0.9rem;\n  margin-right: 0.5rem;\n}\n\n.register-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.register-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 登录/注册切换Tab */\n.auth-tabs {\n  margin-bottom: 2rem;\n}\n\n.tab-buttons {\n  display: flex;\n  background: rgba(243, 244, 246, 0.8);\n  border-radius: 12px;\n  padding: 4px;\n  gap: 4px;\n}\n\n.tab-btn {\n  flex: 1;\n  padding: 12px 24px;\n  border: none;\n  background: transparent;\n  border-radius: 8px;\n  font-size: 0.95rem;\n  font-weight: 600;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.tab-btn.active {\n  background: white;\n  color: #3b82f6;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.tab-btn:hover:not(.active) {\n  color: #374151;\n  background: rgba(255, 255, 255, 0.5);\n}\n\n/* 注册表单 */\n.register-form {\n  margin-top: 0;\n}\n\n/* 注册方式切换 */\n.register-type-tabs {\n  margin-bottom: 2rem;\n}\n\n.type-tab-buttons {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.type-tab-btn {\n  flex: 1;\n  min-width: 120px;\n  padding: 12px 16px;\n  border: 2px solid rgba(59, 130, 246, 0.2);\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 12px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n}\n\n.type-tab-btn.active {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  border-color: transparent;\n  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);\n}\n\n.type-tab-btn:hover:not(.active) {\n  border-color: rgba(59, 130, 246, 0.4);\n  background: rgba(255, 255, 255, 0.95);\n  color: #374151;\n}\n\n/* 注册内容区域 */\n.register-content {\n  margin-top: 1.5rem;\n}\n\n.register-form-content {\n  margin: 0;\n}\n\n/* 验证码输入行 */\n.verify-code-row {\n  display: flex;\n  gap: 12px;\n  align-items: center;\n}\n\n.verify-code-input {\n  flex: 1;\n}\n\n.send-code-btn {\n  flex-shrink: 0;\n  min-width: 120px;\n  border-radius: 8px;\n  border: 2px solid #3b82f6;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  font-weight: 600;\n  transition: all 0.3s ease;\n}\n\n.send-code-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);\n  border-color: #2563eb;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.send-code-btn:disabled {\n  background: #e5e7eb;\n  border-color: #e5e7eb;\n  color: #9ca3af;\n  cursor: not-allowed;\n}\n\n/* 用户协议 */\n.agreement-section {\n  margin: 1.5rem 0;\n  padding: 1rem;\n  background: rgba(59, 130, 246, 0.05);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  border-radius: 12px;\n}\n\n.agreement-checkbox {\n  color: #374151;\n  font-size: 0.9rem;\n  line-height: 1.6;\n}\n\n.agreement-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 600;\n  transition: color 0.3s ease;\n}\n\n.agreement-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 注册按钮 */\n.submit-section {\n  margin: 2rem 0 1.5rem;\n}\n\n.register-submit-btn {\n  width: 100%;\n  height: 48px;\n  border-radius: 12px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border: none;\n  font-size: 1rem;\n  font-weight: 700;\n  letter-spacing: 0.5px;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);\n}\n\n.register-submit-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);\n}\n\n.register-submit-btn:disabled {\n  background: #e5e7eb;\n  color: #9ca3af;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n/* 登录提示 */\n.login-prompt {\n  text-align: center;\n  margin-top: 1.5rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid rgba(229, 231, 235, 0.8);\n}\n\n.login-text {\n  color: #6b7280;\n  font-size: 0.9rem;\n  margin-right: 0.5rem;\n}\n\n.login-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.login-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 微信注册 */\n.wechat-register-container {\n  text-align: center;\n  padding: 2rem 0;\n}\n\n.wechat-qr-section {\n  margin-bottom: 2rem;\n}\n\n.qr-code-container {\n  display: inline-block;\n  padding: 20px;\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n  margin-bottom: 1.5rem;\n}\n\n.qr-code-image {\n  width: 200px;\n  height: 200px;\n  border-radius: 8px;\n}\n\n.qr-loading {\n  width: 200px;\n  height: 200px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #6b7280;\n}\n\n.qr-instructions h4 {\n  color: #374151;\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin: 0 0 1rem 0;\n}\n\n.qr-instructions p {\n  color: #6b7280;\n  font-size: 0.9rem;\n  margin: 0.5rem 0;\n  line-height: 1.5;\n}\n\n.invite-info {\n  margin: 1.5rem 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .login-main {\n    max-width: 1200px;\n    padding: 0 1.5rem;\n  }\n}\n\n@media (max-width: 1024px) {\n  .login-main {\n    flex-direction: column;\n    max-width: 800px;\n    padding: 6rem 1rem 2rem; /* 平板端保持顶部padding */\n  }\n\n  .login-info {\n    margin: 0 0 1rem; /* 减少margin */\n    padding: 2rem;\n  }\n\n  .login-container {\n    margin: 0 0 1rem; /* 减少margin */\n    max-width: none;\n  }\n\n  .brand-title {\n    font-size: 2.5rem;\n  }\n\n  .feature-highlights {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 1rem;\n  }\n\n  .tab-buttons {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 768px) {\n\n  .login-main {\n    padding: 5rem 0.5rem 2rem; /* 移动端减少顶部padding */\n  }\n\n  .login-info {\n    margin: 0 0 1rem; /* 移动端减少margin */\n    padding: 1.5rem;\n  }\n\n  .login-container {\n    margin: 0 0 1rem; /* 移动端减少margin */\n    padding: 1rem;\n  }\n\n  .login-card {\n    padding: 2rem 1.5rem;\n  }\n\n  .brand-title {\n    font-size: 2rem;\n  }\n\n  .login-title {\n    font-size: 1.5rem;\n  }\n\n  .feature-highlights {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .feature-item {\n    padding: 1rem;\n  }\n\n  .feature-item:hover {\n    transform: translateY(-2px);\n  }\n\n  .social-buttons-horizontal {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .captcha-row {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .captcha-image-container {\n    align-self: stretch;\n  }\n\n  .captcha-image {\n    width: 100%;\n  }\n\n  .tab-buttons {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .tab-btn {\n    font-size: 0.8rem;\n    padding: 0.5rem;\n  }\n\n  .verify-code-row {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .send-code-btn {\n    width: 100%;\n  }\n}\n\n@media (max-width: 480px) {\n\n  .login-card {\n    padding: 1.5rem 1rem;\n    border-radius: 16px;\n  }\n\n  .brand-title {\n    font-size: 1.8rem;\n  }\n\n  .login-title {\n    font-size: 1.3rem;\n  }\n\n  .input-group {\n    margin-bottom: 1rem;\n  }\n\n  .login-submit-button {\n    height: 48px;\n    font-size: 1rem;\n  }\n\n  .social-btn-large {\n    padding: 0.75rem;\n    font-size: 0.8rem;\n  }\n\n  .social-btn-large .anticon {\n    font-size: 1rem;\n  }\n\n  .tab-buttons {\n    grid-template-columns: 1fr;\n    gap: 0.25rem;\n  }\n\n  .tab-btn {\n    flex-direction: row;\n    justify-content: center;\n    gap: 0.5rem;\n    font-size: 0.8rem;\n  }\n\n  .qr-code-image {\n    width: 150px;\n    height: 150px;\n  }\n}\n</style>\n"]}]}