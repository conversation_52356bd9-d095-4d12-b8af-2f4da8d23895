<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Thu Jul 17 19:50:45 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-17 19:50:57,954</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-17 19:51:05,008</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-17 19:51:12,061</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-17 19:51:19,127</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-17 19:51:26,182</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Fri Jul 18 19:01:53 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-18 19:04:28,001</td>
<td class="Message">TOS客户端异常: jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json</td>
<td class="MethodOfCaller">downloadDraftFile</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">463</td>
</tr>
<tr><td class="Exception" colspan="6">com.volcengine.tos.TosClientException: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.TosObjectRequestHandler.getObject(TosObjectRequestHandler.java:250)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.TOSV2Client.getObject(TOSV2Client.java:519)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:441)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadDraftViaTosSDK(CozeApiService.java:1910)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadAndParseDraft(CozeApiService.java:857)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService.addCaptions(JianyingAssistantService.java:1743)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$FastClassBySpringCGLIB$$b93b33ed.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$EnhancerBySpringCGLIB$$9398e2cb.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController.addCaptions(JianyingToolboxController.java:284)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$FastClassBySpringCGLIB$$48a06f70.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessKeyAspect.validateAccessKey(JianyingAccessKeyAspect.java:91)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessAspect.validateAccess(JianyingAccessAspect.java:73)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$EnhancerBySpringCGLIB$$98f2fd9b.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestTransport.roundTrip(RequestTransport.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 138 common frames omitted
<br />Caused by: java.io.EOFException: SSL peer shut down incorrectly
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.InputRecord.read(InputRecord.java:505)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 160 common frames omitted
</td></tr>
<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-18 19:04:28,004</td>
<td class="Message">TOS SDK下载草稿失败: /jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json</td>
<td class="MethodOfCaller">downloadDraftViaTosSDK</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">1921</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.RuntimeException: TOS下载失败: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:464)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadDraftViaTosSDK(CozeApiService.java:1910)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadAndParseDraft(CozeApiService.java:857)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService.addCaptions(JianyingAssistantService.java:1743)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$FastClassBySpringCGLIB$$b93b33ed.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$EnhancerBySpringCGLIB$$9398e2cb.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController.addCaptions(JianyingToolboxController.java:284)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$FastClassBySpringCGLIB$$48a06f70.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessKeyAspect.validateAccessKey(JianyingAccessKeyAspect.java:91)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessAspect.validateAccess(JianyingAccessAspect.java:73)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$EnhancerBySpringCGLIB$$98f2fd9b.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: com.volcengine.tos.TosClientException: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.TosObjectRequestHandler.getObject(TosObjectRequestHandler.java:250)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.TOSV2Client.getObject(TOSV2Client.java:519)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:441)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 134 common frames omitted
<br />Caused by: javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestTransport.roundTrip(RequestTransport.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 138 common frames omitted
<br />Caused by: java.io.EOFException: SSL peer shut down incorrectly
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.InputRecord.read(InputRecord.java:505)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 160 common frames omitted
</td></tr>
<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-18 19:04:28,006</td>
<td class="Message">下载并解析草稿失败: https://aigcview.cn/jeecg-boot/sys/common/jianying-file/jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json</td>
<td class="MethodOfCaller">downloadAndParseDraft</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">893</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.RuntimeException: TOS SDK下载草稿失败: TOS下载失败: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadDraftViaTosSDK(CozeApiService.java:1922)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadAndParseDraft(CozeApiService.java:857)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService.addCaptions(JianyingAssistantService.java:1743)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$FastClassBySpringCGLIB$$b93b33ed.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$EnhancerBySpringCGLIB$$9398e2cb.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController.addCaptions(JianyingToolboxController.java:284)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$FastClassBySpringCGLIB$$48a06f70.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessKeyAspect.validateAccessKey(JianyingAccessKeyAspect.java:91)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessAspect.validateAccess(JianyingAccessAspect.java:73)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$EnhancerBySpringCGLIB$$98f2fd9b.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.RuntimeException: TOS下载失败: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:464)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadDraftViaTosSDK(CozeApiService.java:1910)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 133 common frames omitted
<br />Caused by: com.volcengine.tos.TosClientException: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.TosObjectRequestHandler.getObject(TosObjectRequestHandler.java:250)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.TOSV2Client.getObject(TOSV2Client.java:519)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:441)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 134 common frames omitted
<br />Caused by: javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestTransport.roundTrip(RequestTransport.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 138 common frames omitted
<br />Caused by: java.io.EOFException: SSL peer shut down incorrectly
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.InputRecord.read(InputRecord.java:505)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 160 common frames omitted
</td></tr>
<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-18 19:04:28,009</td>
<td class="Message">批量添加字幕失败</td>
<td class="MethodOfCaller">addCaptions</td>
<td class="FileOfCaller">JianyingAssistantService.java</td>
<td class="LineOfCaller">1791</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.RuntimeException: 下载并解析草稿失败: TOS SDK下载草稿失败: TOS下载失败: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadAndParseDraft(CozeApiService.java:894)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService.addCaptions(JianyingAssistantService.java:1743)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$FastClassBySpringCGLIB$$b93b33ed.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$EnhancerBySpringCGLIB$$9398e2cb.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController.addCaptions(JianyingToolboxController.java:284)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$FastClassBySpringCGLIB$$48a06f70.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessKeyAspect.validateAccessKey(JianyingAccessKeyAspect.java:91)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessAspect.validateAccess(JianyingAccessAspect.java:73)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$EnhancerBySpringCGLIB$$98f2fd9b.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.RuntimeException: TOS SDK下载草稿失败: TOS下载失败: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadDraftViaTosSDK(CozeApiService.java:1922)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadAndParseDraft(CozeApiService.java:857)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 132 common frames omitted
<br />Caused by: java.lang.RuntimeException: TOS下载失败: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:464)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadDraftViaTosSDK(CozeApiService.java:1910)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 133 common frames omitted
<br />Caused by: com.volcengine.tos.TosClientException: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.TosObjectRequestHandler.getObject(TosObjectRequestHandler.java:250)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.TOSV2Client.getObject(TOSV2Client.java:519)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:441)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 134 common frames omitted
<br />Caused by: javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestTransport.roundTrip(RequestTransport.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 138 common frames omitted
<br />Caused by: java.io.EOFException: SSL peer shut down incorrectly
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.InputRecord.read(InputRecord.java:505)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 160 common frames omitted
</td></tr><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Fri Jul 18 19:12:01 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Jul 19 16:29:18 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-19 18:36:15,144</td>
<td class="Message">【websocket消息】连接错误，用户ID: e9ca23d68d884d4ebb19d07889727dae, 错误信息: null</td>
<td class="MethodOfCaller">onError</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">135</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Mon Jul 21 20:50:56 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-21 20:51:10,578</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-21 20:51:17,633</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-21 20:51:24,704</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Tue Jul 22 03:22:07 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:22:34,746</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:22:39,760</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:22:44,768</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:22:49,786</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:22:54,798</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:22:59,813</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:23:04,822</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:23:09,839</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:23:14,847</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:23:19,858</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:23:24,876</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:23:29,883</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:23:34,896</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:23:39,908</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:23:44,922</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:23:49,934</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:23:54,946</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:23:59,961</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:24:04,983</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:24:10,000</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:24:15,015</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:24:20,029</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:24:25,031</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:24:30,042</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:24:35,050</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:24:40,059</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:24:45,065</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:24:50,079</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:24:55,085</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:25:00,096</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:25:05,110</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:25:10,120</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:25:15,129</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:25:20,132</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:25:25,137</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:25:30,143</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:25:35,159</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:25:40,163</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:25:45,169</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:25:50,174</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:25:55,185</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:26:00,195</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:26:05,206</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:26:10,210</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:26:15,219</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:26:20,227</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:26:25,231</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:26:30,242</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:26:35,255</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:26:40,258</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:26:45,274</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:26:50,281</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:26:55,290</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:27:00,304</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:27:05,311</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:27:10,322</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:27:15,337</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:27:20,346</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:27:25,353</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:27:30,368</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:27:35,382</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:27:40,389</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:27:45,397</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:27:50,404</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:27:55,416</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:28:00,433</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:28:05,436</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:28:10,450</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:28:15,458</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:28:20,465</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:28:25,474</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:28:30,486</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:28:35,500</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:28:40,514</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:28:45,519</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:28:50,533</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:28:55,545</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:29:00,563</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:29:05,575</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:29:10,586</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:29:15,595</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:29:20,610</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:29:25,614</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:29:30,620</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:29:35,626</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:29:40,628</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:29:45,638</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:29:50,653</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:29:55,660</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:30:00,672</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:30:05,676</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:30:10,682</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:30:15,693</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:30:20,699</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:30:25,707</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:30:30,728</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:30:35,744</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:30:40,748</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:30:45,752</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:30:50,757</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:30:55,764</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:31:00,770</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:31:05,776</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:31:10,781</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:31:15,784</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:31:20,794</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:31:25,805</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:31:30,816</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:31:35,820</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:31:40,831</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:31:45,833</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:31:50,847</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:31:55,861</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:32:00,878</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:32:05,885</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:32:10,897</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:32:15,899</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:32:20,914</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:32:25,917</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:32:30,931</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:32:35,945</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:32:40,954</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:32:45,966</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:32:50,971</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:32:55,974</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:33:00,988</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:33:06,002</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:33:11,010</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:33:16,026</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:33:21,031</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:33:26,047</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:33:31,061</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:33:36,077</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:33:41,083</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:33:46,097</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:33:51,108</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:33:56,114</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:34:01,123</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:34:06,129</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:34:11,147</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:34:16,158</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:34:21,174</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:34:26,186</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:34:31,194</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:34:36,212</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:34:41,223</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:34:46,235</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:34:51,240</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:34:56,255</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:01,272</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:06,278</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:11,283</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:16,302</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:21,315</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:26,329</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:31,339</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Tue Jul 22 03:35:31 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:36,350</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:41,358</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:45,030</td>
<td class="Message">

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that&#39;s listening on port 8080 or configure this application to listen on another port.
</td>
<td class="MethodOfCaller">report</td>
<td class="FileOfCaller">LoggingFailureAnalysisReporter.java</td>
<td class="LineOfCaller">40</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:46,386</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:51,392</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:35:56,399</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:36:01,411</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:36:06,415</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:36:11,440</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:36:16,450</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:36:21,455</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:36:26,465</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:36:31,467</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:36:36,475</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:36:41,481</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:36:46,499</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:36:51,504</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-22 03:36:56,512</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Wed Jul 23 10:49:33 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:49:48,351</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:49:55,410</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:50:02,469</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:50:09,523</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:50:16,580</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:50:23,629</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:50:30,684</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:50:37,751</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:50:44,808</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:50:51,863</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:50:58,932</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:51:06,002</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:51:13,054</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:51:20,096</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:51:27,167</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:51:34,224</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:51:41,267</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:51:48,330</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:51:55,374</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:52:02,436</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:52:09,492</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:52:16,549</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:52:23,609</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:52:30,658</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:52:37,708</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-23 10:52:44,770</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Thu Jul 24 00:47:23 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:47:40,848</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:47:47,903</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:47:54,944</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:48:02,001</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:48:09,040</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:48:16,094</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:48:23,144</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:48:30,195</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:48:37,242</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:48:44,308</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:48:51,368</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:48:58,427</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:49:05,489</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:49:12,537</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:49:19,603</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:49:26,658</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-24 00:49:33,707</td>
<td class="Message">Connection failure occurred. Restarting subscription task after 5000 ms</td>
<td class="MethodOfCaller">handleSubscriptionException</td>
<td class="FileOfCaller">RedisMessageListenerContainer.java</td>
<td class="LineOfCaller">651</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Jul 26 12:02:26 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Jul 26 14:31:10 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-26 14:31:22,003</td>
<td class="Message">

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that&#39;s listening on port 8080 or configure this application to listen on another port.
</td>
<td class="MethodOfCaller">report</td>
<td class="FileOfCaller">LoggingFailureAnalysisReporter.java</td>
<td class="LineOfCaller">40</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Jul 26 14:33:54 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-26 14:34:07,573</td>
<td class="Message">Error while adding the mapper &#39;interface org.jeecg.modules.system.mapper.SysUserRoleMapper&#39; to configuration.</td>
<td class="MethodOfCaller">checkDaoConfig</td>
<td class="FileOfCaller">MapperFactoryBean.java</td>
<td class="LineOfCaller">82</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:117)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.visitor.Reifier.reifyTypeArguments(Reifier.java:68)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.repository.ClassRepository.getSuperInterfaces(ClassRepository.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.getGenericInterfaces(Class.java:913)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.ResolvableType.getInterfaces(ResolvableType.java:502)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.ResolvableType.as(ResolvableType.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.GenericTypeResolver.resolveTypeArguments(GenericTypeResolver.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.toolkit.ReflectionKit.getSuperClassGenericType(ReflectionKit.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.injector.AbstractSqlInjector.inspectInject(AbstractSqlInjector.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parserInjector(MybatisMapperAnnotationBuilder.java:131)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:80)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.DaoSupport.afterPropertiesSet(DaoSupport.java:44)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:453)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:239)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:405)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.SpringApplication.run(SpringApplication.java:1237)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.JeecgSystemApplication.main(JeecgSystemApplication.java:34)
<br />Caused by: java.lang.ClassNotFoundException: org.jeecg.modules.system.entity.SysUserRole
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.net.URLClassLoader.findClass(URLClassLoader.java:382)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:349)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.forName0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.forName(Class.java:348)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:114)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 64 common frames omitted
</td></tr>
<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-26 14:34:07,690</td>
<td class="Message">Application run failed</td>
<td class="MethodOfCaller">reportFailure</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">837</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name &#39;jimuReportTokenService&#39;: Unsatisfied dependency expressed through field &#39;sysBaseAPI&#39;; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name &#39;sysBaseApiImpl&#39;: Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name &#39;sysUserRoleMapper&#39; defined in file [D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes\org\jeecg\modules\system\mapper\SysUserRoleMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:405)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.SpringApplication.run(SpringApplication.java:1237)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.JeecgSystemApplication.main(JeecgSystemApplication.java:34)
<br />Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name &#39;sysBaseApiImpl&#39;: Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name &#39;sysUserRoleMapper&#39; defined in file [D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes\org\jeecg\modules\system\mapper\SysUserRoleMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:321)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 20 common frames omitted
<br />Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name &#39;sysUserRoleMapper&#39; defined in file [D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes\org\jeecg\modules\system\mapper\SysUserRoleMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:453)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:239)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 31 common frames omitted
<br />Caused by: java.lang.IllegalArgumentException: java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.DaoSupport.afterPropertiesSet(DaoSupport.java:44)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 44 common frames omitted
<br />Caused by: java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:117)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.visitor.Reifier.reifyTypeArguments(Reifier.java:68)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.repository.ClassRepository.getSuperInterfaces(ClassRepository.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.getGenericInterfaces(Class.java:913)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.ResolvableType.getInterfaces(ResolvableType.java:502)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.ResolvableType.as(ResolvableType.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.GenericTypeResolver.resolveTypeArguments(GenericTypeResolver.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.toolkit.ReflectionKit.getSuperClassGenericType(ReflectionKit.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.injector.AbstractSqlInjector.inspectInject(AbstractSqlInjector.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parserInjector(MybatisMapperAnnotationBuilder.java:131)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:80)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 47 common frames omitted
<br />Caused by: java.lang.ClassNotFoundException: org.jeecg.modules.system.entity.SysUserRole
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.net.URLClassLoader.findClass(URLClassLoader.java:382)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:349)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.forName0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.forName(Class.java:348)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:114)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 64 common frames omitted
</td></tr><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Jul 26 14:36:14 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Jul 26 14:43:27 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Jul 26 14:51:31 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Jul 26 15:06:04 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Mon Jul 28 06:10:29 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-28 08:16:52,619</td>
<td class="Message">Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object</td>
<td class="MethodOfCaller">handleException</td>
<td class="FileOfCaller">JeecgBootExceptionHandler.java</td>
<td class="LineOfCaller">80</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.IllegalStateException: Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:242)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:225)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:718)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:401)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:107)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.getReqestParams(AutoLogAspect.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.saveSysLog(AutoLogAspect.java:98)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.demo.usercenter.controller.UserCenterDataController$$EnhancerBySpringCGLIB$$6ed65131.getReferralStats(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.NoSuchMethodException: org.jeecg.modules.demo.usercenter.controller.UserCenterDataController.generateInviteCodeFallback(java.lang.String)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.getDeclaredMethod(Class.java:2130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:239)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 108 common frames omitted
</td></tr>
<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-28 08:30:18,756</td>
<td class="Message">Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object</td>
<td class="MethodOfCaller">handleException</td>
<td class="FileOfCaller">JeecgBootExceptionHandler.java</td>
<td class="LineOfCaller">80</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.IllegalStateException: Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:242)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:225)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:718)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:401)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:107)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.getReqestParams(AutoLogAspect.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.saveSysLog(AutoLogAspect.java:98)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.demo.usercenter.controller.UserCenterDataController$$EnhancerBySpringCGLIB$$6ed65131.getReferralStats(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.NoSuchMethodException: org.jeecg.modules.demo.usercenter.controller.UserCenterDataController.generateInviteCodeFallback(java.lang.String)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.getDeclaredMethod(Class.java:2130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:239)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 108 common frames omitted
</td></tr>
<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-28 08:30:34,044</td>
<td class="Message">Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object</td>
<td class="MethodOfCaller">handleException</td>
<td class="FileOfCaller">JeecgBootExceptionHandler.java</td>
<td class="LineOfCaller">80</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.IllegalStateException: Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:242)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:225)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:718)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:401)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:107)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.getReqestParams(AutoLogAspect.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.saveSysLog(AutoLogAspect.java:98)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.demo.usercenter.controller.UserCenterDataController$$EnhancerBySpringCGLIB$$6ed65131.getReferralStats(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.NoSuchMethodException: org.jeecg.modules.demo.usercenter.controller.UserCenterDataController.generateInviteCodeFallback(java.lang.String)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.getDeclaredMethod(Class.java:2130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:239)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 108 common frames omitted
</td></tr>
<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-28 08:32:04,551</td>
<td class="Message">Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object</td>
<td class="MethodOfCaller">handleException</td>
<td class="FileOfCaller">JeecgBootExceptionHandler.java</td>
<td class="LineOfCaller">80</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.IllegalStateException: Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:242)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:225)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:718)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:401)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:107)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.getReqestParams(AutoLogAspect.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.saveSysLog(AutoLogAspect.java:98)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.demo.usercenter.controller.UserCenterDataController$$EnhancerBySpringCGLIB$$6ed65131.getReferralStats(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.NoSuchMethodException: org.jeecg.modules.demo.usercenter.controller.UserCenterDataController.generateInviteCodeFallback(java.lang.String)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.getDeclaredMethod(Class.java:2130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:239)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 108 common frames omitted
</td></tr>
<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-28 08:33:00,173</td>
<td class="Message">Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object</td>
<td class="MethodOfCaller">handleException</td>
<td class="FileOfCaller">JeecgBootExceptionHandler.java</td>
<td class="LineOfCaller">80</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.IllegalStateException: Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:242)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:225)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:718)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:401)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:107)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.getReqestParams(AutoLogAspect.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.saveSysLog(AutoLogAspect.java:98)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.demo.usercenter.controller.UserCenterDataController$$EnhancerBySpringCGLIB$$6ed65131.getReferralStats(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.NoSuchMethodException: org.jeecg.modules.demo.usercenter.controller.UserCenterDataController.generateInviteCodeFallback(java.lang.String)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.getDeclaredMethod(Class.java:2130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:239)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 108 common frames omitted
</td></tr>
<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-28 08:34:37,235</td>
<td class="Message">Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object</td>
<td class="MethodOfCaller">handleException</td>
<td class="FileOfCaller">JeecgBootExceptionHandler.java</td>
<td class="LineOfCaller">80</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.IllegalStateException: Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:242)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:225)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:718)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:401)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:107)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.getReqestParams(AutoLogAspect.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.saveSysLog(AutoLogAspect.java:98)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.demo.usercenter.controller.UserCenterDataController$$EnhancerBySpringCGLIB$$6ed65131.getReferralStats(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.NoSuchMethodException: org.jeecg.modules.demo.usercenter.controller.UserCenterDataController.generateInviteCodeFallback(java.lang.String)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.getDeclaredMethod(Class.java:2130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:239)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 108 common frames omitted
</td></tr>
<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-28 08:35:15,036</td>
<td class="Message">Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object</td>
<td class="MethodOfCaller">handleException</td>
<td class="FileOfCaller">JeecgBootExceptionHandler.java</td>
<td class="LineOfCaller">80</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.IllegalStateException: Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:242)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:225)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:718)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:401)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:107)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.getReqestParams(AutoLogAspect.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.saveSysLog(AutoLogAspect.java:98)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.demo.usercenter.controller.UserCenterDataController$$EnhancerBySpringCGLIB$$6ed65131.getReferralStats(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.NoSuchMethodException: org.jeecg.modules.demo.usercenter.controller.UserCenterDataController.generateInviteCodeFallback(java.lang.String)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.getDeclaredMethod(Class.java:2130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:239)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 108 common frames omitted
</td></tr>
<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-28 08:36:28,267</td>
<td class="Message">Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object</td>
<td class="MethodOfCaller">handleException</td>
<td class="FileOfCaller">JeecgBootExceptionHandler.java</td>
<td class="LineOfCaller">80</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.IllegalStateException: Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:242)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:225)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:718)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:401)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:107)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.getReqestParams(AutoLogAspect.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.saveSysLog(AutoLogAspect.java:98)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.GeneratedMethodAccessor322.invoke(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.demo.usercenter.controller.UserCenterDataController$$EnhancerBySpringCGLIB$$6ed65131.getReferralStats(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.NoSuchMethodException: org.jeecg.modules.demo.usercenter.controller.UserCenterDataController.generateInviteCodeFallback(java.lang.String)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.getDeclaredMethod(Class.java:2130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:239)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 107 common frames omitted
</td></tr>
<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-28 08:40:56,244</td>
<td class="Message">Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object</td>
<td class="MethodOfCaller">handleException</td>
<td class="FileOfCaller">JeecgBootExceptionHandler.java</td>
<td class="LineOfCaller">80</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.IllegalStateException: Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:242)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:225)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:718)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:401)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:107)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.getReqestParams(AutoLogAspect.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.saveSysLog(AutoLogAspect.java:98)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.GeneratedMethodAccessor322.invoke(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.demo.usercenter.controller.UserCenterDataController$$EnhancerBySpringCGLIB$$6ed65131.getReferralStats(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.NoSuchMethodException: org.jeecg.modules.demo.usercenter.controller.UserCenterDataController.generateInviteCodeFallback(java.lang.String)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.getDeclaredMethod(Class.java:2130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:239)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 107 common frames omitted
</td></tr>
<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-28 08:49:50,687</td>
<td class="Message">Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object</td>
<td class="MethodOfCaller">handleException</td>
<td class="FileOfCaller">JeecgBootExceptionHandler.java</td>
<td class="LineOfCaller">80</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.IllegalStateException: Method [generateInviteCodeFallback] was discovered in the .class file but cannot be resolved in the class object
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:242)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:225)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:718)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.asm.ClassReader.accept(ClassReader.java:401)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:107)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.getReqestParams(AutoLogAspect.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.saveSysLog(AutoLogAspect.java:98)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.GeneratedMethodAccessor322.invoke(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.demo.usercenter.controller.UserCenterDataController$$EnhancerBySpringCGLIB$$6ed65131.getReferralStats(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.NoSuchMethodException: org.jeecg.modules.demo.usercenter.controller.UserCenterDataController.generateInviteCodeFallback(java.lang.String)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Class.getDeclaredMethod(Class.java:2130)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:239)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 107 common frames omitted
</td></tr><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Mon Jul 28 08:50:45 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>

