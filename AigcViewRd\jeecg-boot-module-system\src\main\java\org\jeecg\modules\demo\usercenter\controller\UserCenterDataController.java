package org.jeecg.modules.demo.usercenter.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.math.BigDecimal;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import com.alibaba.fastjson.JSON;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import org.jeecg.modules.api.util.QRCodeUtil;
import org.jeecg.modules.jianying.service.TosService;
import java.io.ByteArrayOutputStream;
import java.io.ByteArrayInputStream;
import java.util.UUID;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.jeecg.modules.demo.membershiphistory.entity.AicgUserMembershipHistory;
import org.jeecg.modules.demo.membershiphistory.service.IAicgUserMembershipHistoryService;

import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.demo.userrecord.entity.AicgUserRecord;
import org.jeecg.modules.demo.userrecord.service.IAicgUserRecordService;
import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import org.jeecg.modules.demo.plubshop.service.IAigcPlubShopService;
import org.jeecg.modules.api.mapper.AicgApiLogMapper;
import org.jeecg.modules.api.entity.AicgApiLog;
import org.jeecg.modules.jianying.service.TosService;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.ISysDictService;
import org.jeecg.modules.system.service.ISensitiveWordService;
import org.jeecg.modules.system.service.IInviteCodeGeneratorService;
import org.jeecg.modules.system.config.RegisterConfig;
import java.util.Random;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.jdbc.core.JdbcTemplate;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 用户中心数据统一接口
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Api(tags="用户中心数据统一接口")
@RestController
@RequestMapping("/api/usercenter")
@Slf4j
public class UserCenterDataController {
    
    @Autowired
    private IAicgUserProfileService userProfileService;

    @Autowired
    private IAicgUserRecordService userRecordService;

    @Autowired
    private IAicgUserMembershipHistoryService membershipHistoryService;



    @Autowired
    private AicgApiLogMapper apiLogMapper;

    @Autowired
    private IAigcPlubShopService plubShopService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysDictService sysDictService;

    @Autowired
    private ISensitiveWordService sensitiveWordService;

    @Autowired
    private TosService tosService;

    @Autowired
    private RegisterConfig registerConfig;

    @Autowired
    private IInviteCodeGeneratorService inviteCodeGeneratorService;

    /**
     * 测试接口 - 验证Controller是否正常工作
     */
    @AutoLog(value = "测试接口")
    @ApiOperation(value="测试接口", notes="测试接口")
    @GetMapping(value = "/test")
    public Result<?> test() {
        return Result.OK("UserCenterDataController is working!");
    }

    /**
     * 获取用户中心概览数据
     * 包含用户信息、统计数据、最近活动等
     */
    @AutoLog(value = "获取用户中心概览数据")
    @ApiOperation(value="获取用户中心概览数据", notes="获取用户中心概览数据")
    @GetMapping(value = "/overview")
    public Result<?> getUserCenterOverview(HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            log.info("🔍 getUserCenterOverview - 接收到的TOKEN: {}", token);

            String username = JwtUtil.getUsername(token);
            log.info("🔍 getUserCenterOverview - 解析出的用户名: {}", username);

            // 获取用户扩展信息
            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            log.info("🔍 getUserCenterOverview - 查询到的用户扩展信息: {}", userProfile);

            if (userProfile == null) {
                log.warn("🔍 getUserCenterOverview - 用户扩展信息不存在，用户名: {}", username);
                return Result.error("用户信息不存在");
            }

            // 获取API调用统计数据
            String userId = userProfile.getUserId();
            int apiCallsToday = getApiCallsToday(userId);
            int apiCallsMonth = getApiCallsMonth(userId);

            // 计算会员剩余天数
            int memberDaysLeft = calculateMemberDaysLeft(userProfile.getMemberExpireTime());

            // 返回扁平的数据结构，与前端期望一致
            Map<String, Object> overview = new HashMap<>();
            overview.put("accountBalance", userProfile.getAccountBalance());
            overview.put("totalConsumption", userProfile.getTotalConsumption());
            overview.put("totalRecharge", userProfile.getTotalRecharge());

            overview.put("memberDaysLeft", memberDaysLeft);
            overview.put("apiCallsToday", apiCallsToday);
            overview.put("apiCallsMonth", apiCallsMonth);

            log.info("🔍 getUserCenterOverview - 返回的概览数据: {}", overview);
            return Result.OK(overview);
        } catch (Exception e) {
            log.error("获取用户中心概览数据失败", e);
            return Result.error("获取数据失败");
        }
    }
    
    /**
     * 获取用户完整信息（联合查询 sys_user 和 aicg_user_profile）
     */
    @AutoLog(value = "获取用户完整信息")
    @ApiOperation(value="获取用户完整信息", notes="获取用户完整信息")
    @GetMapping(value = "/userFullInfo")
    public Result<?> getUserFullInfo(HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            log.info("🔍 getUserFullInfo - 接收到的TOKEN: {}", token);

            String username = JwtUtil.getUsername(token);
            log.info("🔍 getUserFullInfo - 解析出的用户名: {}", username);

            // 获取用户完整信息（包含sys_user和aicg_user_profile的数据）
            Map<String, Object> userFullInfo = userProfileService.getUserFullInfo(username);
            log.info("🔍 getUserFullInfo - 查询到的用户信息: {}", userFullInfo);

            return Result.OK(userFullInfo);
        } catch (Exception e) {
            log.error("获取用户完整信息失败", e);
            return Result.error("获取用户信息失败");
        }
    }
    
    /**
     * 获取API调用统计
     */
    @AutoLog(value = "获取API调用统计")
    @ApiOperation(value="获取API调用统计", notes="获取API调用统计")
    @GetMapping(value = "/apiUsageStats")
    public Result<?> getApiUsageStats(@RequestParam(name="timeRange", defaultValue="all") String timeRange,
                                     HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            // 实现API使用统计查询
            String userId = userProfile.getUserId();
            Map<String, Object> stats = new HashMap<>();

            // 查询API调用统计
            int todayCalls = getApiCallsToday(userId);
            int monthCalls = getApiCallsMonth(userId);
            int totalCalls = getApiCallsAll(userId);

            // 查询成功率
            double successRate = getApiSuccessRate(userId);

            stats.put("todayCalls", todayCalls);
            stats.put("monthCalls", monthCalls);
            stats.put("totalCalls", totalCalls);
            stats.put("successRate", successRate);

            return Result.OK(stats);
        } catch (Exception e) {
            log.error("获取API调用统计失败", e);
            return Result.error("获取统计数据失败");
        }
    }

    /**
     * 获取用户使用记录列表（聚合数据）
     */
    @AutoLog(value = "获取用户使用记录列表")
    @ApiOperation(value="获取用户使用记录列表", notes="获取用户使用记录列表")
    @GetMapping(value = "/usageList")
    public Result<?> getUserUsageList(@RequestParam(name="category", required=false) String category,
                                     @RequestParam(name="pluginKeyword", required=false) String pluginKeyword,
                                     @RequestParam(name="startDate", required=false) String startDate,
                                     @RequestParam(name="endDate", required=false) String endDate,
                                     @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                     @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                     HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            log.info("🎯 getUserUsageList - 用户: {}, 分类: {}, 关键词: {}", username, category, pluginKeyword);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            String userId = userProfile.getUserId();

            // 构建聚合查询SQL - 从API使用记录表按插件分组统计
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT ");
            sqlBuilder.append("plugin_key, ");
            sqlBuilder.append("plugin_name, ");
            sqlBuilder.append("api_endpoint, ");
            sqlBuilder.append("COUNT(*) as call_count, ");
            sqlBuilder.append("SUM(cost_amount) as total_cost, ");
            sqlBuilder.append("MAX(call_time) as last_used_time ");
            sqlBuilder.append("FROM aicg_user_api_usage ");
            sqlBuilder.append("WHERE user_id = ? AND response_status = 200 AND plugin_key IS NOT NULL "); // 只统计成功且有插件信息的记录

            List<Object> params = new ArrayList<>();
            params.add(userId);

            // 添加时间范围查询
            if (startDate != null && !startDate.isEmpty()) {
                sqlBuilder.append("AND call_time >= ? ");
                params.add(startDate + " 00:00:00");
            }
            if (endDate != null && !endDate.isEmpty()) {
                sqlBuilder.append("AND call_time <= ? ");
                params.add(endDate + " 23:59:59");
            }

            sqlBuilder.append("GROUP BY plugin_key, plugin_name, api_endpoint ");
            sqlBuilder.append("ORDER BY last_used_time DESC ");

            // 查询总数SQL
            StringBuilder countSqlBuilder = new StringBuilder();
            countSqlBuilder.append("SELECT COUNT(DISTINCT plugin_key) ");
            countSqlBuilder.append("FROM aicg_user_api_usage ");
            countSqlBuilder.append("WHERE user_id = ? AND response_status = 200 AND plugin_key IS NOT NULL ");

            List<Object> countParams = new ArrayList<>();
            countParams.add(userId);

            if (startDate != null && !startDate.isEmpty()) {
                countSqlBuilder.append("AND call_time >= ? ");
                countParams.add(startDate + " 00:00:00");
            }
            if (endDate != null && !endDate.isEmpty()) {
                countSqlBuilder.append("AND call_time <= ? ");
                countParams.add(endDate + " 23:59:59");
            }

            // 查询总数
            List<Map<String, Object>> countResult = jdbcTemplate.queryForList(countSqlBuilder.toString(), countParams.toArray());
            long total = countResult.isEmpty() ? 0 : Long.parseLong(countResult.get(0).values().iterator().next().toString());

            // 查询分页数据
            int offset = (pageNo - 1) * pageSize;
            params.add(offset);
            params.add(pageSize);
            sqlBuilder.append("LIMIT ?, ?");
            List<Map<String, Object>> rawRecords = jdbcTemplate.queryForList(sqlBuilder.toString(), params.toArray());

            // 处理查询结果，插件信息已经在SQL中查询出来了
            List<Map<String, Object>> records = new ArrayList<>();
            for (Map<String, Object> record : rawRecords) {
                Map<String, Object> enrichedRecord = new HashMap<>(record);

                // 插件信息已经在SQL中查询出来，直接使用
                String pluginKey = (String) record.get("plugin_key");
                String pluginName = (String) record.get("plugin_name");

                // 如果插件名称为空，使用API端点作为显示名称
                if (pluginName == null || pluginName.isEmpty()) {
                    String apiEndpoint = (String) record.get("api_endpoint");
                    enrichedRecord.put("plugin_name", apiEndpoint);
                    enrichedRecord.put("category", inferCategoryFromEndpoint(apiEndpoint));
                } else {
                    // 查询插件详细信息以获取分类
                    try {
                        AigcPlubShop plugin = plubShopService.getByPluginKey(pluginKey);
                        if (plugin != null) {
                            enrichedRecord.put("category", plugin.getPlubCategory());
                            enrichedRecord.put("author", plugin.getPlubwrite());
                            enrichedRecord.put("price_per_call", plugin.getNeednum());
                        } else {
                            enrichedRecord.put("category", "未知分类");
                        }
                    } catch (Exception e) {
                        log.warn("查询插件详细信息失败 - pluginKey: {}", pluginKey);
                        enrichedRecord.put("category", "未知分类");
                    }
                }

                // 应用筛选条件
                if (shouldIncludeRecord(enrichedRecord, category, pluginKeyword)) {
                    records.add(enrichedRecord);
                }
            }

            Page<Map<String, Object>> page = new Page<>(pageNo, pageSize);
            page.setTotal(total);
            page.setRecords(records);

            log.info("🎯 getUserUsageList - 查询结果: 总数={}, 当前页数据量={}", total, records.size());
            return Result.OK(page);

        } catch (Exception e) {
            log.error("🎯 getUserUsageList - 获取使用记录失败", e);
            return Result.error("获取使用记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取插件使用历史
     */
    @AutoLog(value = "获取插件使用历史")
    @ApiOperation(value="获取插件使用历史", notes="获取插件使用历史")
    @GetMapping(value = "/pluginUsageHistory")
    public Result<?> getPluginUsageHistory(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                          @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                          HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            
            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }
            
            // 实现插件使用历史查询
            String userId = userProfile.getUserId();

            // 构建查询SQL - 简化查询，在Java中关联插件信息
            String sql = "SELECT id, api_endpoint, request_params, " +
                        "1 as usage_type, response_time as usage_duration, " +
                        "tokens_used, cost_amount, " +
                        "CASE WHEN response_status = 200 THEN 1 ELSE 2 END as status, " +
                        "call_time as start_time, call_time as end_time " +
                        "FROM aicg_user_api_usage " +
                        "WHERE user_id = ? ORDER BY call_time DESC LIMIT ?, ?";

            // 查询总数
            String countSql = "SELECT COUNT(*) FROM aicg_user_api_usage WHERE user_id = ?";
            List<Map<String, Object>> countResult = jdbcTemplate.queryForList(countSql, userId);
            long total = countResult.isEmpty() ? 0 : Long.parseLong(countResult.get(0).get("COUNT(*)").toString());

            // 查询分页数据
            int offset = (pageNo - 1) * pageSize;
            List<Map<String, Object>> rawRecords = jdbcTemplate.queryForList(sql, userId, offset, pageSize);

            // 在Java中关联插件信息
            List<Map<String, Object>> records = new ArrayList<>();
            for (Map<String, Object> record : rawRecords) {
                Map<String, Object> enrichedRecord = new HashMap<>(record);

                String apiEndpoint = (String) record.get("api_endpoint");
                String requestParams = (String) record.get("request_params");

                // 尝试从request_params中提取pluginKey
                String pluginKey = extractPluginKey(requestParams);
                AigcPlubShop plugin = null;

                if (pluginKey != null && !pluginKey.isEmpty()) {
                    plugin = plubShopService.getByPluginKey(pluginKey);
                }

                // 设置插件信息
                if (plugin != null) {
                    enrichedRecord.put("plugin_name", plugin.getPlubname());
                    enrichedRecord.put("plugin_category", plugin.getPlubCategory());
                    enrichedRecord.put("author", plugin.getPlubwrite());
                    enrichedRecord.put("price_per_call", plugin.getNeednum());
                } else {
                    enrichedRecord.put("plugin_name", apiEndpoint);
                    enrichedRecord.put("plugin_category", inferCategoryFromEndpoint(apiEndpoint));
                    enrichedRecord.put("author", null);
                    enrichedRecord.put("price_per_call", null);
                }

                records.add(enrichedRecord);
            }

            Page<Map<String, Object>> page = new Page<>(pageNo, pageSize);
            page.setTotal(total);
            page.setRecords(records);

            return Result.OK(page);
        } catch (Exception e) {
            log.error("获取插件使用历史失败", e);
            return Result.error("获取历史数据失败");
        }
    }
    
    /**
     * 获取订单记录列表
     */
    @AutoLog(value = "获取订单记录列表")
    @ApiOperation(value="获取订单记录列表", notes="获取订单记录列表")
    @GetMapping(value = "/orders")
    public Result<?> getUserOrderList(@RequestParam(name="orderType", required=false) String orderType,
                                     @RequestParam(name="status", required=false) String status,
                                     @RequestParam(name="startDate", required=false) String startDate,
                                     @RequestParam(name="endDate", required=false) String endDate,
                                     @RequestParam(name="orderKeyword", required=false) String orderKeyword,
                                     @RequestParam(name="productKeyword", required=false) String productKeyword,
                                     @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                     @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                     HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            log.info("🎯 getUserOrderList - 用户: {}, 订单类型: {}, 状态: {}", username, orderType, status);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            // 实现订单查询 - 使用交易记录作为订单数据，支持新字段
            String userId = userProfile.getUserId();

            // 🔥 修复：查询交易记录表，包含订单信息，过滤重复记录
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT id, transaction_type, amount, description, related_order_id, transaction_time, ");
            sqlBuilder.append("order_status, order_type, product_info, create_time, ");
            sqlBuilder.append("plugin_id, plugin_key, plugin_name, balance_before, balance_after ");
            sqlBuilder.append("FROM aicg_user_transaction WHERE user_id = ? ");
            // 🔧 过滤掉无效的重复记录：排除所有 order_status=1 (未付款) 的记录
            sqlBuilder.append("AND order_status != 1");

            List<Object> params = new ArrayList<>();
            params.add(userId);

            // 添加筛选条件
            if (orderType != null && !orderType.isEmpty()) {
                sqlBuilder.append(" AND order_type = ?");
                params.add(orderType);
            }

            if (status != null && !status.isEmpty()) {
                sqlBuilder.append(" AND order_status = ?");
                params.add(Integer.parseInt(status));
            }

            // 添加时间范围查询
            if (startDate != null && !startDate.isEmpty()) {
                sqlBuilder.append(" AND create_time >= ?");
                params.add(startDate + " 00:00:00");
            }
            if (endDate != null && !endDate.isEmpty()) {
                sqlBuilder.append(" AND create_time <= ?");
                params.add(endDate + " 23:59:59");
            }

            // 添加订单号搜索
            if (orderKeyword != null && !orderKeyword.isEmpty()) {
                sqlBuilder.append(" AND (related_order_id LIKE ? OR id LIKE ?)");
                params.add("%" + orderKeyword + "%");
                params.add("%" + orderKeyword + "%");
            }

            // 添加商品名称搜索
            if (productKeyword != null && !productKeyword.isEmpty()) {
                sqlBuilder.append(" AND (description LIKE ? OR plugin_name LIKE ?)");
                params.add("%" + productKeyword + "%");
                params.add("%" + productKeyword + "%");
            }

            sqlBuilder.append(" ORDER BY create_time DESC LIMIT ?, ?");

            // 查询总数SQL
            StringBuilder countSqlBuilder = new StringBuilder();
            countSqlBuilder.append("SELECT COUNT(*) FROM aicg_user_transaction WHERE user_id = ?");
            List<Object> countParams = new ArrayList<>();
            countParams.add(userId);

            if (orderType != null && !orderType.isEmpty()) {
                countSqlBuilder.append(" AND order_type = ?");
                countParams.add(orderType);
            }

            if (status != null && !status.isEmpty()) {
                countSqlBuilder.append(" AND order_status = ?");
                countParams.add(Integer.parseInt(status));
            }

            // 添加时间范围查询到count查询
            if (startDate != null && !startDate.isEmpty()) {
                countSqlBuilder.append(" AND create_time >= ?");
                countParams.add(startDate + " 00:00:00");
            }
            if (endDate != null && !endDate.isEmpty()) {
                countSqlBuilder.append(" AND create_time <= ?");
                countParams.add(endDate + " 23:59:59");
            }

            // 添加订单号搜索到count查询
            if (orderKeyword != null && !orderKeyword.isEmpty()) {
                countSqlBuilder.append(" AND (related_order_id LIKE ? OR id LIKE ?)");
                countParams.add("%" + orderKeyword + "%");
                countParams.add("%" + orderKeyword + "%");
            }

            // 添加商品名称搜索到count查询
            if (productKeyword != null && !productKeyword.isEmpty()) {
                countSqlBuilder.append(" AND (description LIKE ? OR plugin_name LIKE ?)");
                countParams.add("%" + productKeyword + "%");
                countParams.add("%" + productKeyword + "%");
            }

            // 查询总数
            List<Map<String, Object>> countResult = jdbcTemplate.queryForList(countSqlBuilder.toString(), countParams.toArray());
            long total = countResult.isEmpty() ? 0 : Long.parseLong(countResult.get(0).get("COUNT(*)").toString());

            // 查询分页数据
            int offset = (pageNo - 1) * pageSize;
            params.add(offset);
            params.add(pageSize);
            List<Map<String, Object>> records = jdbcTemplate.queryForList(sqlBuilder.toString(), params.toArray());

            // 处理返回数据，解析product_info JSON
            for (Map<String, Object> record : records) {
                String productInfoStr = (String) record.get("product_info");
                if (productInfoStr != null && !productInfoStr.isEmpty()) {
                    try {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> productInfo = JSON.parseObject(productInfoStr, Map.class);
                        record.put("productInfo", productInfo);
                    } catch (Exception e) {
                        log.warn("解析product_info失败: {}", productInfoStr);
                        record.put("productInfo", new HashMap<>());
                    }
                }
                record.remove("product_info"); // 移除原始JSON字符串
            }

            Page<Map<String, Object>> page = new Page<>(pageNo, pageSize);
            page.setTotal(total);
            page.setRecords(records);

            log.info("🎯 getUserOrderList - 查询结果: 总数={}, 当前页数据量={}", total, records.size());
            return Result.OK(page);
        } catch (Exception e) {
            log.error("🎯 getUserOrderList - 获取订单记录失败", e);
            return Result.error("获取订单数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单统计数据
     */
    @AutoLog(value = "获取订单统计数据")
    @ApiOperation(value="获取订单统计数据", notes="获取订单统计数据")
    @GetMapping(value = "/orderStats")
    public Result<?> getOrderStats(HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            log.info("🎯 getOrderStats - 用户: {}", username);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            String userId = userProfile.getUserId();

            // 使用交易记录作为订单统计，支持新的订单状态
            String completedOrdersSql = "SELECT COUNT(*) FROM aicg_user_transaction WHERE user_id = ? AND order_status = 3"; // 已完成订单数
            String monthCompletedOrdersSql = "SELECT COUNT(*) FROM aicg_user_transaction WHERE user_id = ? AND order_status = 3 AND YEAR(transaction_time) = YEAR(CURDATE()) AND MONTH(transaction_time) = MONTH(CURDATE())"; // 本月完成订单数
            // 修复：只统计已完成状态的订单金额
            String totalAmountSql = "SELECT SUM(amount) FROM aicg_user_transaction WHERE user_id = ? AND amount > 0 AND order_status = 3"; // 总消费金额
            String monthAmountSql = "SELECT SUM(amount) FROM aicg_user_transaction WHERE user_id = ? AND amount > 0 AND order_status = 3 AND YEAR(transaction_time) = YEAR(CURDATE()) AND MONTH(transaction_time) = MONTH(CURDATE())"; // 本月消费金额

            // 新增：按状态统计订单
            String pendingOrdersSql = "SELECT COUNT(*) FROM aicg_user_transaction WHERE user_id = ? AND order_status = 1"; // 待支付
            String cancelledOrdersSql = "SELECT COUNT(*) FROM aicg_user_transaction WHERE user_id = ? AND order_status = 4"; // 取消订单数
            String monthCancelledOrdersSql = "SELECT COUNT(*) FROM aicg_user_transaction WHERE user_id = ? AND order_status = 4 AND YEAR(create_time) = YEAR(CURDATE()) AND MONTH(create_time) = MONTH(CURDATE())"; // 本月取消订单数

            // 新增：按类型统计订单
            String membershipOrdersSql = "SELECT COUNT(*) FROM aicg_user_transaction WHERE user_id = ? AND order_type = 'membership'";
            String pluginOrdersSql = "SELECT COUNT(*) FROM aicg_user_transaction WHERE user_id = ? AND order_type = 'plugin'";
            String rechargeOrdersSql = "SELECT COUNT(*) FROM aicg_user_transaction WHERE user_id = ? AND order_type = 'recharge'";

            List<Map<String, Object>> completedResult = jdbcTemplate.queryForList(completedOrdersSql, userId);
            List<Map<String, Object>> monthCompletedResult = jdbcTemplate.queryForList(monthCompletedOrdersSql, userId);
            List<Map<String, Object>> amountResult = jdbcTemplate.queryForList(totalAmountSql, userId);
            List<Map<String, Object>> monthAmountResult = jdbcTemplate.queryForList(monthAmountSql, userId);
            List<Map<String, Object>> pendingResult = jdbcTemplate.queryForList(pendingOrdersSql, userId);
            List<Map<String, Object>> cancelledResult = jdbcTemplate.queryForList(cancelledOrdersSql, userId);
            List<Map<String, Object>> monthCancelledResult = jdbcTemplate.queryForList(monthCancelledOrdersSql, userId);
            List<Map<String, Object>> membershipResult = jdbcTemplate.queryForList(membershipOrdersSql, userId);
            List<Map<String, Object>> pluginResult = jdbcTemplate.queryForList(pluginOrdersSql, userId);
            List<Map<String, Object>> rechargeResult = jdbcTemplate.queryForList(rechargeOrdersSql, userId);

            Map<String, Object> stats = new HashMap<>();
            // 总统计数据
            stats.put("totalCompletedOrders", completedResult.isEmpty() ? 0 : completedResult.get(0).get("COUNT(*)"));
            stats.put("totalAmount", amountResult.isEmpty() || amountResult.get(0).get("SUM(amount)") == null ? 0 : amountResult.get(0).get("SUM(amount)"));
            stats.put("pendingOrders", pendingResult.isEmpty() ? 0 : pendingResult.get(0).get("COUNT(*)"));
            stats.put("totalCancelledOrders", cancelledResult.isEmpty() ? 0 : cancelledResult.get(0).get("COUNT(*)"));

            // 本月统计数据
            stats.put("monthCompletedOrders", monthCompletedResult.isEmpty() ? 0 : monthCompletedResult.get(0).get("COUNT(*)"));
            stats.put("monthAmount", monthAmountResult.isEmpty() || monthAmountResult.get(0).get("SUM(amount)") == null ? 0 : monthAmountResult.get(0).get("SUM(amount)"));
            stats.put("monthCancelledOrders", monthCancelledResult.isEmpty() ? 0 : monthCancelledResult.get(0).get("COUNT(*)"));
            stats.put("membershipOrders", membershipResult.isEmpty() ? 0 : membershipResult.get(0).get("COUNT(*)"));
            stats.put("pluginOrders", pluginResult.isEmpty() ? 0 : pluginResult.get(0).get("COUNT(*)"));
            stats.put("rechargeOrders", rechargeResult.isEmpty() ? 0 : rechargeResult.get(0).get("COUNT(*)"));

            log.info("🎯 getOrderStats - 统计结果: {}", stats);
            return Result.OK(stats);
        } catch (Exception e) {
            log.error("🎯 getOrderStats - 获取订单统计失败", e);
            return Result.error("获取订单统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取会员信息和等级配置
     */
    @AutoLog(value = "获取会员信息")
    @ApiOperation(value="获取会员信息", notes="获取会员信息")
    @GetMapping(value = "/membership")
    public Result<?> getMembershipInfo(HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            
            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }
            
            Map<String, Object> result = new HashMap<>();
            
            // 当前会员信息
            AicgUserMembershipHistory currentMembership = membershipHistoryService.getCurrentMembership(userProfile.getUserId());
            result.put("currentMembership", currentMembership);
            
            // 会员历史
            List<AicgUserMembershipHistory> membershipHistory = membershipHistoryService.getByUserId(userProfile.getUserId());
            result.put("membershipHistory", membershipHistory);
            
            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取会员信息失败", e);
            return Result.error("获取会员信息失败");
        }
    }
    
    /**
     * 获取推荐统计信息
     */
    @AutoLog(value = "获取推荐统计信息")
    @ApiOperation(value="获取推荐统计信息", notes="获取推荐统计信息")
    @GetMapping(value = "/referralStats")
    public Result<?> getReferralStats(HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            
            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }
            
            // 实现推荐统计查询
            String userId = userProfile.getUserId();

            // 查询推荐统计
            String totalReferralsSql = "SELECT COUNT(*) FROM aicg_user_referral WHERE referrer_id = ?";
            String monthlyReferralsSql = "SELECT COUNT(*) FROM aicg_user_referral WHERE referrer_id = ? AND YEAR(register_time) = YEAR(CURDATE()) AND MONTH(register_time) = MONTH(CURDATE())";

            // 查询会员转化数：统计推荐用户中订阅过会员的人数
            String memberReferralsSql = "SELECT COUNT(DISTINCT r.referee_id) FROM aicg_user_referral r " +
                "INNER JOIN aicg_user_transaction t ON r.referee_id COLLATE utf8mb4_general_ci = t.user_id COLLATE utf8mb4_general_ci " +
                "WHERE r.referrer_id = ? AND t.transaction_type = 5 AND t.order_status = 2";

            // 查询总奖励金额（已发放的）
            String totalRewardsSql = "SELECT COALESCE(SUM(reward_amount), 0) FROM aicg_user_referral_reward WHERE referrer_id = ? AND status = 2";

            // 查询可提现金额（待发放的）
            String availableRewardsSql = "SELECT COALESCE(SUM(reward_amount), 0) FROM aicg_user_referral_reward WHERE referrer_id = ? AND status = 1";

            List<Map<String, Object>> totalReferralsResult = jdbcTemplate.queryForList(totalReferralsSql, userId);
            List<Map<String, Object>> monthlyReferralsResult = jdbcTemplate.queryForList(monthlyReferralsSql, userId);
            List<Map<String, Object>> memberReferralsResult = jdbcTemplate.queryForList(memberReferralsSql, userId);
            List<Map<String, Object>> totalRewardsResult = jdbcTemplate.queryForList(totalRewardsSql, userId);
            List<Map<String, Object>> availableRewardsResult = jdbcTemplate.queryForList(availableRewardsSql, userId);

            Map<String, Object> stats = new HashMap<>();
            stats.put("total_referrals", totalReferralsResult.isEmpty() ? 0 : totalReferralsResult.get(0).get("COUNT(*)"));
            stats.put("monthly_referrals", monthlyReferralsResult.isEmpty() ? 0 : monthlyReferralsResult.get(0).get("COUNT(*)"));
            stats.put("member_referrals", memberReferralsResult.isEmpty() ? 0 : memberReferralsResult.get(0).get("COUNT(DISTINCT r.referee_id)"));
            stats.put("total_reward_amount", totalRewardsResult.isEmpty() ? 0 : totalRewardsResult.get(0).get("COALESCE(SUM(reward_amount), 0)"));
            stats.put("available_rewards", availableRewardsResult.isEmpty() ? 0 : availableRewardsResult.get(0).get("COALESCE(SUM(reward_amount), 0)"));
            
            return Result.OK(stats);
        } catch (Exception e) {
            log.error("获取推荐统计失败", e);
            return Result.error("获取推荐统计失败");
        }
    }

    /**
     * 生成推荐链接
     */
    @AutoLog(value = "生成推荐链接")
    @ApiOperation(value="生成推荐链接", notes="生成推荐链接")
    @PostMapping(value = "/generateReferralLink")
    public Result<?> generateReferralLink(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            
            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }
            
            // 获取或生成用户的邀请码（使用新的统一服务）
            String inviteCode;
            try {
                // 使用新的统一邀请码生成服务（会自动检查、生成并保存邀请码）
                inviteCode = inviteCodeGeneratorService.generateOrGetInviteCode(userProfile.getUserId());
                log.info("为用户 {} 使用新服务获取/生成邀请码: {}", username, inviteCode);
            } catch (Exception e) {
                log.error("使用新服务生成邀请码失败，回退到旧逻辑，用户: {}, 错误: {}", username, e.getMessage());
                // 如果新服务失败，回退到旧的生成逻辑
                inviteCode = userProfile.getMyInviteCode();
                if (inviteCode == null || inviteCode.trim().isEmpty()) {
                    inviteCode = generateInviteCodeFallback(userProfile.getUserId());
                    userProfile.setMyInviteCode(inviteCode);
                    userProfileService.updateById(userProfile);
                }
                log.info("为用户 {} 使用备用方案获取邀请码: {}", username, inviteCode);
            }
            
            // 使用当前请求的域名生成基础URL，保证域名切换的灵活性
            String baseUrl = request.getScheme() + "://" + request.getServerName();
            if (request.getServerPort() != 80 && request.getServerPort() != 443) {
                baseUrl += ":" + request.getServerPort();
            }
            
            // 构建推荐链接 - 指向登录页面并携带推荐码
            String referralLink = baseUrl + "/login?ref=" + inviteCode;
            
            log.info("生成推荐链接成功 - 用户: {}, 链接: {}", username, referralLink);
            return Result.OK(referralLink);
        } catch (Exception e) {
            log.error("生成推荐链接失败", e);
            return Result.error("生成推荐链接失败");
        }
    }

    /**
     * 获取最近活动记录
     */
    @AutoLog(value = "获取最近活动记录")
    @ApiOperation(value="获取最近活动记录", notes="获取最近活动记录")
    @GetMapping(value = "/recentActivities")
    public Result<?> getRecentActivities(@RequestParam(name="limit", defaultValue="10") Integer limit,
                                        HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            // 获取最近交易记录作为活动记录
            QueryWrapper<AicgUserRecord> recordQuery = new QueryWrapper<>();
            recordQuery.eq("user_id", userProfile.getUserId())
                       .orderByDesc("transaction_time")
                       .last("LIMIT " + limit);
            List<AicgUserRecord> recentActivities = userRecordService.list(recordQuery);

            return Result.OK(recentActivities);
        } catch (Exception e) {
            log.error("获取最近活动记录失败", e);
            return Result.error("获取活动记录失败");
        }
    }

    /**
     * 获取交易记录列表
     */
    @AutoLog(value = "获取交易记录列表")
    @ApiOperation(value="获取交易记录列表", notes="获取交易记录列表")
    @GetMapping(value = "/transactionList")
    public Result<?> getTransactionList(@RequestParam(name="type", required=false) String type,
                                       @RequestParam(name="startDate", required=false) String startDate,
                                       @RequestParam(name="endDate", required=false) String endDate,
                                       @RequestParam(name="keyword", required=false) String keyword,
                                       @RequestParam(name="current", defaultValue="1") Integer pageNo,
                                       @RequestParam(name="size", defaultValue="10") Integer pageSize,
                                       HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            log.info("🔍 获取交易记录 - 用户: {}, 类型: {}, 开始日期: {}, 结束日期: {}, 关键词: {}",
                    username, type, startDate, endDate, keyword);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            // 🔥 简化：直接用 SQL 查询，和订单管理保持一致
            String userId = userProfile.getUserId();

            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT id, transaction_type, amount, description, related_order_id, transaction_time, ");
            sqlBuilder.append("order_status, order_type, product_info, create_time, ");
            sqlBuilder.append("plugin_id, plugin_key, plugin_name, balance_before, balance_after ");
            sqlBuilder.append("FROM aicg_user_transaction WHERE user_id = ? ");
            sqlBuilder.append("AND order_status = 3 "); // 只显示已完成的记录

            List<Object> params = new ArrayList<>();
            params.add(userId);

            // 添加筛选条件
            if (type != null && !type.isEmpty()) {
                sqlBuilder.append(" AND transaction_type = ?");
                params.add(type);
            }

            // 添加日期范围筛选
            if (startDate != null && !startDate.isEmpty()) {
                sqlBuilder.append(" AND DATE(CONVERT_TZ(transaction_time, '+00:00', '+08:00')) >= ?");
                params.add(startDate);
            }
            if (endDate != null && !endDate.isEmpty()) {
                sqlBuilder.append(" AND DATE(CONVERT_TZ(transaction_time, '+00:00', '+08:00')) <= ?");
                params.add(endDate);
            }

            // 添加关键词搜索
            if (keyword != null && !keyword.isEmpty()) {
                sqlBuilder.append(" AND description LIKE ?");
                params.add("%" + keyword + "%");
            }

            sqlBuilder.append(" ORDER BY transaction_time DESC");

            // 分页查询
            int offset = (pageNo - 1) * pageSize;
            String countSql = "SELECT COUNT(*) FROM (" + sqlBuilder.toString() + ") AS total_query";
            String dataSql = sqlBuilder.toString() + " LIMIT ? OFFSET ?";
            params.add(pageSize);
            params.add(offset);

            // 查询总数和数据
            Integer total = jdbcTemplate.queryForObject(countSql, Integer.class, params.subList(0, params.size() - 2).toArray());
            List<Map<String, Object>> recordMaps = jdbcTemplate.queryForList(dataSql, params.toArray());

            // 🔥 转换为 Page 对象格式，保持返回结构不变
            Page<AicgUserRecord> result = new Page<>(pageNo, pageSize);
            result.setTotal(total != null ? total : 0);

            // 将 Map 转换为 AicgUserRecord 对象
            List<AicgUserRecord> records = new ArrayList<>();
            for (Map<String, Object> map : recordMaps) {
                AicgUserRecord record = new AicgUserRecord();
                record.setId((String) map.get("id"));
                record.setUserId((String) map.get("user_id"));
                record.setTransactionType((Integer) map.get("transaction_type"));
                record.setAmount((BigDecimal) map.get("amount"));
                record.setBalanceBefore((BigDecimal) map.get("balance_before"));
                record.setBalanceAfter((BigDecimal) map.get("balance_after"));
                record.setDescription((String) map.get("description"));
                record.setRelatedOrderId((String) map.get("related_order_id"));
                record.setTransactionTime((Date) map.get("transaction_time"));
                record.setOrderStatus((Integer) map.get("order_status"));
                record.setOrderType((String) map.get("order_type"));
                record.setPluginId((String) map.get("plugin_id"));
                record.setPluginKey((String) map.get("plugin_key"));
                record.setPluginName((String) map.get("plugin_name"));
                record.setCreateBy((String) map.get("create_by"));
                record.setCreateTime((Date) map.get("create_time"));
                records.add(record);
            }
            result.setRecords(records);

            log.info("🔍 获取交易记录成功 - 用户: {}, 总数: {}, 当前页: {}", username, result.getTotal(), result.getCurrent());
            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取交易记录失败", e);
            return Result.error("获取交易记录失败");
        }
    }

    /**
     * 获取交易统计数据
     */
    @AutoLog(value = "获取交易统计数据")
    @ApiOperation(value="获取交易统计数据", notes="获取交易统计数据")
    @GetMapping(value = "/transactionStats")
    public Result<?> getTransactionStats(HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalRecharge", userProfile.getTotalRecharge());
            stats.put("totalConsumption", userProfile.getTotalConsumption());
            stats.put("accountBalance", userProfile.getAccountBalance());
            stats.put("transactionCount", userRecordService.count(new QueryWrapper<AicgUserRecord>().eq("user_id", userProfile.getUserId())));

            // ✅ 添加本月消费计算
            BigDecimal monthlyConsumption = calculateMonthlyConsumption(userProfile.getUserId());
            stats.put("monthlyConsumption", monthlyConsumption);

            return Result.OK(stats);
        } catch (Exception e) {
            log.error("获取交易统计失败", e);
            return Result.error("获取交易统计失败");
        }
    }

    /**
     * 获取充值选项配置
     */
    @AutoLog(value = "获取充值选项配置")
    @ApiOperation(value="获取充值选项配置", notes="获取充值选项配置")
    @GetMapping(value = "/rechargeOptions")
    public Result<?> getRechargeOptions() {
        try {
            log.info("💰 获取充值选项配置");

            // 定义充值选项配置
            List<Map<String, Object>> rechargeOptions = new ArrayList<>();

            // 体验套餐
            Map<String, Object> option1 = new HashMap<>();
            option1.put("amount", 50);
            option1.put("label", "体验套餐");
            option1.put("bonus", 0);
            option1.put("description", "适合初次体验用户");
            rechargeOptions.add(option1);

            // 基础套餐
            Map<String, Object> option2 = new HashMap<>();
            option2.put("amount", 100);
            option2.put("label", "基础套餐");
            option2.put("bonus", 5);
            option2.put("description", "送5元，性价比之选");
            rechargeOptions.add(option2);

            // 进阶套餐
            Map<String, Object> option3 = new HashMap<>();
            option3.put("amount", 300);
            option3.put("label", "进阶套餐");
            option3.put("bonus", 20);
            option3.put("description", "送20元，适合中度使用");
            rechargeOptions.add(option3);

            // 专业套餐
            Map<String, Object> option4 = new HashMap<>();
            option4.put("amount", 500);
            option4.put("label", "专业套餐");
            option4.put("bonus", 50);
            option4.put("description", "送50元，专业用户首选");
            rechargeOptions.add(option4);

            // 企业套餐
            Map<String, Object> option5 = new HashMap<>();
            option5.put("amount", 1000);
            option5.put("label", "企业套餐");
            option5.put("bonus", 150);
            option5.put("description", "送150元，企业级服务");
            rechargeOptions.add(option5);

            log.info("💰 充值选项配置返回成功，共{}个选项", rechargeOptions.size());
            return Result.OK(rechargeOptions);

        } catch (Exception e) {
            log.error("💰 获取充值选项配置失败", e);
            return Result.error("获取充值选项配置失败");
        }
    }

    /**
     * 创建充值订单
     */
    @AutoLog(value = "创建充值订单")
    @ApiOperation(value="创建充值订单", notes="创建充值订单")
    @PostMapping(value = "/createRechargeOrder")
    public Result<?> createRechargeOrder(@RequestBody Map<String, Object> requestData,
                                        HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);

            BigDecimal amount = new BigDecimal(requestData.get("amount").toString());
            String paymentMethod = (String) requestData.getOrDefault("paymentMethod", "alipay");

            log.info("💰 创建充值订单 - 用户: {}, 金额: {}, 支付方式: {}", username, amount, paymentMethod);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            // 生成订单号
            String orderId = "RECHARGE_" + System.currentTimeMillis() + "_" + userProfile.getUserId().substring(0, 8);

            // 创建交易记录（待支付状态）
            String insertSql = "INSERT INTO aicg_user_transaction (" +
                "id, user_id, transaction_type, amount, balance_before, balance_after, " +
                "description, related_order_id, order_status, create_time, create_by" +
                ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            String transactionId = UUID.randomUUID().toString().replace("-", "");
            BigDecimal currentBalance = userProfile.getAccountBalance();

            jdbcTemplate.update(insertSql,
                transactionId,
                userProfile.getUserId(),
                1, // 充值
                amount,
                currentBalance,
                currentBalance, // 待支付时余额不变
                "在线充值 - " + paymentMethod,
                orderId,
                1, // 待支付
                new Date(),
                username
            );

            // 根据支付方式处理
            Map<String, Object> result = new HashMap<>();
            result.put("orderId", orderId);
            result.put("amount", amount);
            result.put("paymentMethod", paymentMethod);

            if ("alipay".equals(paymentMethod)) {
                // 调用支付宝支付接口
                try {
                    // 这里需要注入AlipayService
                    // 暂时返回订单信息，前端可以调用支付宝支付接口
                    result.put("needRedirect", true);
                    result.put("paymentUrl", "/api/alipay/createOrder");
                    result.put("message", "请调用支付宝支付接口完成支付");
                } catch (Exception e) {
                    log.error("💰 调用支付宝支付失败", e);
                    result.put("needRedirect", false);
                    result.put("message", "支付宝支付暂时不可用，请稍后重试");
                }
            } else {
                result.put("needRedirect", false);
                result.put("message", "暂不支持该支付方式");
            }

            log.info("💰 充值订单创建成功 - 订单号: {}", orderId);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("💰 创建充值订单失败", e);
            return Result.error("创建充值订单失败: " + e.getMessage());
        }
    }



    /**
     * 获取用户佣金配置信息
     */
    @AutoLog(value = "获取用户佣金配置信息")
    @ApiOperation(value="获取用户佣金配置信息", notes="获取用户佣金配置信息")
    @GetMapping(value = "/commission-config")
    public Result<?> getCommissionConfig(HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            
            // 获取用户扩展信息
            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }
            
            // 获取用户当前会员类型
            String memberType = "NORMAL"; // 默认普通用户
            AicgUserMembershipHistory currentMembership = membershipHistoryService.getCurrentMembership(userProfile.getUserId());
            if (currentMembership != null) {
                Integer memberLevel = currentMembership.getMemberLevel();
                if (memberLevel != null) {
                    switch (memberLevel) {
                        case 2:
                            memberType = "VIP";
                            break;
                        case 3:
                            memberType = "SVIP";
                            break;
                        case 4:
                            memberType = "SUPREME";
                            break;
                        default:
                            memberType = "NORMAL";
                            break;
                    }
                }
            }
            
            // 构建佣金配置信息
            Map<String, Object> commissionConfig = new HashMap<>();
            commissionConfig.put("userType", memberType);
            commissionConfig.put("commissionLevel", userProfile.getCommissionLevel() != null ? userProfile.getCommissionLevel() : 1);
            commissionConfig.put("inviteCount", userProfile.getValidInviteCount() != null ? userProfile.getValidInviteCount() : 0);
            commissionConfig.put("totalCommission", userProfile.getTotalCommission() != null ? userProfile.getTotalCommission() : BigDecimal.ZERO);
            commissionConfig.put("availableCommission", userProfile.getAvailableCommission() != null ? userProfile.getAvailableCommission() : BigDecimal.ZERO);
            
            return Result.OK(commissionConfig);
        } catch (Exception e) {
            log.error("获取用户佣金配置失败", e);
            return Result.error("获取佣金配置失败");
        }
    }
    
    /**
     * 获取会员等级配置
     */
    @AutoLog(value = "获取会员等级配置")
    @ApiOperation(value="获取会员等级配置", notes="获取会员等级配置")
    @GetMapping(value = "/membershipLevels")
    public Result<?> getMembershipLevels() {
        try {
            // 返回会员等级配置
            List<Map<String, Object>> levels = new ArrayList<>();

            Map<String, Object> basic = new HashMap<>();
            basic.put("id", 1);
            basic.put("name", "普通用户");
            basic.put("price", 0);
            basic.put("features", new String[]{"基础AI服务", "每日10次调用"});
            levels.add(basic);

            Map<String, Object> vip = new HashMap<>();
            vip.put("id", 2);
            vip.put("name", "VIP会员");
            vip.put("price", 29);
            vip.put("features", new String[]{"高级AI服务", "每日100次调用", "优先客服"});
            levels.add(vip);

            Map<String, Object> svip = new HashMap<>();
            svip.put("id", 3);
            svip.put("name", "SVIP会员");
            svip.put("price", 99);
            svip.put("features", new String[]{"全部AI服务", "无限次调用", "专属客服", "定制功能"});
            levels.add(svip);

            Map<String, Object> supreme = new HashMap<>();
            supreme.put("id", 4);
            supreme.put("name", "至尊会员");
            supreme.put("price", 299);
            supreme.put("features", new String[]{"企业级服务", "API接入", "技术支持", "定制开发"});
            levels.add(supreme);

            return Result.OK(levels);
        } catch (Exception e) {
            log.error("获取会员等级配置失败", e);
            return Result.error("获取配置失败");
        }
    }

    /**
     * 获取会员历史记录
     */
    @AutoLog(value = "获取会员历史记录")
    @ApiOperation(value="获取会员历史记录", notes="获取会员历史记录")
    @GetMapping(value = "/membershipHistory")
    public Result<?> getMembershipHistory(@RequestParam(name="current", defaultValue="1") Integer current,
                                         @RequestParam(name="size", defaultValue="10") Integer size,
                                         HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            List<AicgUserMembershipHistory> membershipHistory = membershipHistoryService.getByUserId(userProfile.getUserId());

            // 简单分页处理
            int start = (current - 1) * size;
            int end = Math.min(start + size, membershipHistory.size());
            List<AicgUserMembershipHistory> pageData = membershipHistory.subList(start, end);

            Map<String, Object> result = new HashMap<>();
            result.put("records", pageData);
            result.put("total", membershipHistory.size());
            result.put("current", current);
            result.put("size", size);

            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取会员历史记录失败", e);
            return Result.error("获取历史记录失败");
        }
    }

    /**
     * 导出使用记录数据
     */
    @AutoLog(value = "导出使用记录数据")
    @ApiOperation(value="导出使用记录数据", notes="导出使用记录数据")
    @GetMapping(value = "/exportUsageRecords")
    public void exportUsageRecords(@RequestParam(name="category", required=false) String category,
                                  @RequestParam(name="pluginKeyword", required=false) String pluginKeyword,
                                  @RequestParam(name="startDate", required=false) String startDate,
                                  @RequestParam(name="endDate", required=false) String endDate,
                                  HttpServletRequest request,
                                  HttpServletResponse response) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            log.info("🎯 exportUsageRecords - 用户: {}, 导出条件: category={}, keyword={}", username, category, pluginKeyword);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }

            String userId = userProfile.getUserId();

            // 🔧 修复：使用与页面显示相同的数据源 - aicg_user_api_usage 表
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT ");
            sqlBuilder.append("plugin_key, ");
            sqlBuilder.append("plugin_name, ");
            sqlBuilder.append("api_endpoint, ");
            sqlBuilder.append("COUNT(*) as call_count, ");
            sqlBuilder.append("SUM(cost_amount) as total_cost, ");
            sqlBuilder.append("MAX(call_time) as last_used_time ");
            sqlBuilder.append("FROM aicg_user_api_usage ");
            sqlBuilder.append("WHERE user_id = ? AND response_status = 200 AND plugin_key IS NOT NULL "); // 只统计成功且有插件信息的记录

            List<Object> params = new ArrayList<>();
            params.add(userId);

            // 🔧 修复：使用正确的字段名和筛选逻辑
            if (pluginKeyword != null && !pluginKeyword.isEmpty()) {
                sqlBuilder.append("AND plugin_name LIKE ? ");
                params.add("%" + pluginKeyword + "%");
            }

            // 添加时间范围查询
            if (startDate != null && !startDate.isEmpty()) {
                sqlBuilder.append("AND call_time >= ? ");
                params.add(startDate + " 00:00:00");
            }
            if (endDate != null && !endDate.isEmpty()) {
                sqlBuilder.append("AND call_time <= ? ");
                params.add(endDate + " 23:59:59");
            }

            sqlBuilder.append("GROUP BY plugin_key, plugin_name, api_endpoint ");
            sqlBuilder.append("ORDER BY last_used_time DESC");

            // 查询数据
            List<Map<String, Object>> records = jdbcTemplate.queryForList(sqlBuilder.toString(), params.toArray());

            // 设置响应头为Excel格式
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = "使用记录_" + username + "_" + System.currentTimeMillis() + ".xlsx";
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("使用记录");

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 12);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {"插件名称", "插件分类", "调用次数", "消费金额", "最后使用时间"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 创建时间格式化器
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            // 🔧 修复：处理查询结果，添加插件分类信息
            int rowNum = 1;
            for (Map<String, Object> record : records) {
                Row row = sheet.createRow(rowNum++);

                // 插件名称
                String pluginName = (String) record.get("plugin_name");
                String apiEndpoint = (String) record.get("api_endpoint");
                String pluginKey = (String) record.get("plugin_key");

                // 如果插件名称为空，使用API端点作为显示名称
                if (pluginName == null || pluginName.isEmpty()) {
                    pluginName = apiEndpoint;
                }
                row.createCell(0).setCellValue(pluginName != null ? pluginName : "");

                // 插件分类 - 需要查询插件详细信息获取分类
                String categoryText = "未知分类";
                if (pluginKey != null && !pluginKey.isEmpty()) {
                    try {
                        AigcPlubShop plugin = plubShopService.getByPluginKey(pluginKey);
                        if (plugin != null) {
                            categoryText = getCategoryText(plugin.getPlubCategory());
                        } else {
                            categoryText = inferCategoryFromEndpoint(apiEndpoint);
                        }
                    } catch (Exception e) {
                        categoryText = inferCategoryFromEndpoint(apiEndpoint);
                    }
                } else {
                    categoryText = inferCategoryFromEndpoint(apiEndpoint);
                }
                row.createCell(1).setCellValue(categoryText);

                // 调用次数
                Object callCount = record.get("call_count");
                row.createCell(2).setCellValue(callCount != null ? callCount.toString() + "次" : "0次");

                // 消费金额
                Object totalCost = record.get("total_cost");
                row.createCell(3).setCellValue("¥" + (totalCost != null ? String.format("%.2f", Double.parseDouble(totalCost.toString())) : "0.00"));

                // 最后使用时间
                Object lastUsedTime = record.get("last_used_time");
                if (lastUsedTime != null) {
                    try {
                        if (lastUsedTime instanceof java.sql.Timestamp) {
                            row.createCell(4).setCellValue(dateFormat.format((java.sql.Timestamp) lastUsedTime));
                        } else if (lastUsedTime instanceof java.util.Date) {
                            row.createCell(4).setCellValue(dateFormat.format((java.util.Date) lastUsedTime));
                        } else {
                            row.createCell(4).setCellValue(lastUsedTime.toString());
                        }
                    } catch (Exception e) {
                        row.createCell(4).setCellValue(lastUsedTime.toString());
                    }
                } else {
                    row.createCell(4).setCellValue("");
                }
            }

            // 设置列宽（单位：字符宽度 * 256）
            sheet.setColumnWidth(0, 25 * 256);  // 插件名称 - 25个字符宽度
            sheet.setColumnWidth(1, 15 * 256);  // 插件分类 - 15个字符宽度
            sheet.setColumnWidth(2, 12 * 256);  // 调用次数 - 12个字符宽度
            sheet.setColumnWidth(3, 12 * 256);  // 消费金额 - 12个字符宽度
            sheet.setColumnWidth(4, 20 * 256);  // 最后使用时间 - 20个字符宽度

            // 写入响应
            workbook.write(response.getOutputStream());
            workbook.close();
            log.info("🎯 exportUsageRecords - 导出成功，共{}条记录", records.size());

        } catch (Exception e) {
            log.error("🎯 exportUsageRecords - 导出使用记录失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 导出订单数据
     */
    @AutoLog(value = "导出订单数据")
    @ApiOperation(value="导出订单数据", notes="导出订单数据")
    @GetMapping(value = "/exportOrders")
    public void exportOrders(@RequestParam(name="orderType", required=false) String orderType,
                            @RequestParam(name="status", required=false) String status,
                            @RequestParam(name="startDate", required=false) String startDate,
                            @RequestParam(name="endDate", required=false) String endDate,
                            @RequestParam(name="orderKeyword", required=false) String orderKeyword,
                            @RequestParam(name="productKeyword", required=false) String productKeyword,
                            HttpServletRequest request,
                            HttpServletResponse response) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            log.info("🎯 exportOrders - 用户: {}, 导出条件: orderType={}, status={}", username, orderType, status);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }

            String userId = userProfile.getUserId();

            // 构建查询SQL - 导出所有符合条件的数据，不分页，过滤重复记录
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT id, transaction_type, amount, description, related_order_id, transaction_time, ");
            sqlBuilder.append("order_status, order_type, product_info, create_time ");
            sqlBuilder.append("FROM aicg_user_transaction WHERE user_id = ? ");
            // 🔧 过滤掉无效的重复记录：排除所有 order_status=1 (未付款) 的记录
            sqlBuilder.append("AND order_status != 1");

            List<Object> params = new ArrayList<>();
            params.add(userId);

            // 添加筛选条件
            if (orderType != null && !orderType.isEmpty()) {
                sqlBuilder.append(" AND order_type = ?");
                params.add(orderType);
            }

            if (status != null && !status.isEmpty()) {
                sqlBuilder.append(" AND order_status = ?");
                params.add(Integer.parseInt(status));
            }

            // 添加时间范围查询
            if (startDate != null && !startDate.isEmpty()) {
                sqlBuilder.append(" AND create_time >= ?");
                params.add(startDate + " 00:00:00");
            }
            if (endDate != null && !endDate.isEmpty()) {
                sqlBuilder.append(" AND create_time <= ?");
                params.add(endDate + " 23:59:59");
            }

            // 添加订单号搜索
            if (orderKeyword != null && !orderKeyword.isEmpty()) {
                sqlBuilder.append(" AND related_order_id LIKE ?");
                params.add("%" + orderKeyword + "%");
            }

            // 添加商品名称搜索
            if (productKeyword != null && !productKeyword.isEmpty()) {
                sqlBuilder.append(" AND description LIKE ?");
                params.add("%" + productKeyword + "%");
            }

            sqlBuilder.append(" ORDER BY create_time DESC");

            // 查询数据
            List<Map<String, Object>> records = jdbcTemplate.queryForList(sqlBuilder.toString(), params.toArray());

            // 设置响应头为Excel格式
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = "订单记录_" + username + "_" + System.currentTimeMillis() + ".xlsx";
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("订单记录");

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 12);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {"订单号", "商品名称", "订单类型", "金额", "创建时间", "交易时间", "订单状态"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 创建时间格式化器
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            // 写入数据
            int rowNum = 1;
            for (Map<String, Object> record : records) {
                Row row = sheet.createRow(rowNum++);

                // 订单号
                row.createCell(0).setCellValue(record.get("related_order_id") != null ? record.get("related_order_id").toString() : "");

                // 商品名称
                String description = (String) record.get("description");
                row.createCell(1).setCellValue(description != null ? description : "");

                // 订单类型
                String orderTypeText = getOrderTypeText((String) record.get("order_type"));
                row.createCell(2).setCellValue(orderTypeText);

                // 金额
                Object amount = record.get("amount");
                row.createCell(3).setCellValue("¥" + (amount != null ? amount.toString() : "0.00"));

                // 创建时间 - 格式化时间
                Object createTime = record.get("create_time");
                if (createTime != null) {
                    try {
                        if (createTime instanceof java.sql.Timestamp) {
                            row.createCell(4).setCellValue(dateFormat.format((java.sql.Timestamp) createTime));
                        } else if (createTime instanceof java.util.Date) {
                            row.createCell(4).setCellValue(dateFormat.format((java.util.Date) createTime));
                        } else {
                            row.createCell(4).setCellValue(createTime.toString());
                        }
                    } catch (Exception e) {
                        row.createCell(4).setCellValue(createTime.toString());
                    }
                } else {
                    row.createCell(4).setCellValue("");
                }

                // 交易时间 - 格式化时间
                Object transactionTime = record.get("transaction_time");
                if (transactionTime != null) {
                    try {
                        if (transactionTime instanceof java.sql.Timestamp) {
                            row.createCell(5).setCellValue(dateFormat.format((java.sql.Timestamp) transactionTime));
                        } else if (transactionTime instanceof java.util.Date) {
                            row.createCell(5).setCellValue(dateFormat.format((java.util.Date) transactionTime));
                        } else {
                            row.createCell(5).setCellValue(transactionTime.toString());
                        }
                    } catch (Exception e) {
                        row.createCell(5).setCellValue(transactionTime.toString());
                    }
                } else {
                    row.createCell(5).setCellValue("");
                }

                // 订单状态
                String statusText = getOrderStatusText((Integer) record.get("order_status"));
                row.createCell(6).setCellValue(statusText);
            }

            // 设置列宽（单位：字符宽度 * 256）
            sheet.setColumnWidth(0, 25 * 256);  // 订单号 - 25个字符宽度
            sheet.setColumnWidth(1, 20 * 256);  // 商品名称 - 20个字符宽度
            sheet.setColumnWidth(2, 12 * 256);  // 订单类型 - 12个字符宽度
            sheet.setColumnWidth(3, 10 * 256);  // 金额 - 10个字符宽度
            sheet.setColumnWidth(4, 20 * 256);  // 创建时间 - 20个字符宽度
            sheet.setColumnWidth(5, 20 * 256);  // 交易时间 - 20个字符宽度
            sheet.setColumnWidth(6, 12 * 256);  // 订单状态 - 12个字符宽度

            // 写入响应
            workbook.write(response.getOutputStream());
            workbook.close();
            log.info("🎯 exportOrders - 导出成功，共{}条记录", records.size());

        } catch (Exception e) {
            log.error("🎯 exportOrders - 导出订单失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    // 辅助方法：获取订单类型文本
    private String getOrderTypeText(String orderType) {
        if (orderType == null) return "未知类型";
        switch (orderType) {
            case "plugin": return "插件购买";
            case "membership": return "会员订阅";
            case "recharge": return "账户充值";
            default: return "未知类型";
        }
    }

    // 辅助方法：获取订单状态文本
    private String getOrderStatusText(Integer status) {
        if (status == null) return "未知状态";
        switch (status) {
            case 1: return "未付款";
            case 3: return "已完成";
            case 4: return "已取消";
            case 5: return "已退款";
            default: return "未知状态";
        }
    }

    /**
     * 创建会员订阅订单
     */
    @AutoLog(value = "创建会员订阅订单")
    @ApiOperation(value="创建会员订阅订单", notes="创建会员订阅订单")
    @PostMapping(value = "/createMembershipOrder")
    public Result<?> createMembershipOrder(@RequestBody Map<String, Object> requestData,
                                          HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            log.info("🎯 createMembershipOrder - 用户: {}, 请求数据: {}", username, requestData);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            // 解析请求参数
            Integer membershipLevel = (Integer) requestData.get("membershipLevel");
            Integer duration = (Integer) requestData.get("duration");
            String planName = (String) requestData.get("planName");
            Double amount = Double.valueOf(requestData.get("amount").toString());

            if (membershipLevel == null || duration == null || planName == null || amount == null) {
                return Result.error("参数不完整");
            }

            // 生成订单号
            String orderId = "ORDER_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);

            // 构建产品信息JSON
            Map<String, Object> productInfo = new HashMap<>();
            productInfo.put("membershipLevel", membershipLevel);
            productInfo.put("duration", duration);
            productInfo.put("planName", planName);
            productInfo.put("originalPrice", amount);
            productInfo.put("discountPrice", amount);
            productInfo.put("features", requestData.get("features"));

            // 创建交易记录（订单）
            AicgUserRecord transaction = new AicgUserRecord();
            transaction.setUserId(userProfile.getUserId());
            transaction.setTransactionType(5); // 会员订阅
            transaction.setAmount(new BigDecimal(amount));
            transaction.setBalanceBefore(userProfile.getAccountBalance());
            transaction.setBalanceAfter(userProfile.getAccountBalance()); // 会员订阅不影响余额
            transaction.setDescription(planName + " - " + duration + "个月");
            transaction.setRelatedOrderId(orderId);
            transaction.setTransactionTime(null); // 创建订单时不设置交易时间，等支付成功再设置
            transaction.setCreateTime(new Date());
            transaction.setCreateBy(username);

            // 使用SQL直接插入，包含新增字段
            String insertSql = "INSERT INTO aicg_user_transaction (" +
                "id, user_id, transaction_type, amount, balance_before, balance_after, " +
                "description, order_status, order_type, product_info, related_order_id, " +
                "create_time, create_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            String transactionId = UUID.randomUUID().toString().replace("-", "");

            jdbcTemplate.update(insertSql,
                transactionId,
                userProfile.getUserId(),
                5, // 会员订阅
                amount,
                userProfile.getAccountBalance(),
                userProfile.getAccountBalance(),
                planName + " - " + duration + "个月",
                1, // 待支付
                "membership",
                JSON.toJSONString(productInfo),
                orderId,
                new Date(),
                username
            );

            // 返回订单信息
            Map<String, Object> orderResult = new HashMap<>();
            orderResult.put("orderId", orderId);
            orderResult.put("transactionId", transactionId);
            orderResult.put("amount", amount);
            orderResult.put("planName", planName);
            orderResult.put("status", "pending");
            orderResult.put("createTime", new Date());

            log.info("🎯 createMembershipOrder - 订单创建成功: {}", orderResult);
            return Result.OK(orderResult);

        } catch (Exception e) {
            log.error("🎯 createMembershipOrder - 创建会员订阅订单失败", e);
            return Result.error("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 判断是否为默认头像
     */
    private boolean isDefaultAvatar(String avatarUrl) {
        if (avatarUrl == null || avatarUrl.trim().isEmpty()) {
            return true;
        }

        // 判断是否为外部链接（默认头像通常是外部链接）
        if (avatarUrl.startsWith("http://") || avatarUrl.startsWith("https://")) {
            return true;
        }

        // 判断是否为系统默认头像文件
        String lowerUrl = avatarUrl.toLowerCase();
        return lowerUrl.contains("default") ||
               lowerUrl.contains("avatar2.jpg") ||
               lowerUrl.contains("default-avatar");
    }

    /**
     * 删除旧头像文件（支持TOS和本地文件）
     */
    private void deleteOldAvatarFile(String oldAvatarUrl) {
        try {
            // 判断是否为TOS文件
            if (oldAvatarUrl != null && oldAvatarUrl.startsWith("uploads/")) {
                // TOS文件，使用TOS删除逻辑
                log.info("🔍 deleteOldAvatarFile: 检测到TOS文件，使用TOS删除: {}", oldAvatarUrl);
                tosService.deleteOldAvatarIfExists(oldAvatarUrl);
            } else {
                // 本地文件，使用原有逻辑
                log.info("🔍 deleteOldAvatarFile: 检测到本地文件，使用本地删除: {}", oldAvatarUrl);

                // 获取上传根目录
                String uploadPath = SpringContextUtils.getApplicationContext()
                    .getEnvironment().getProperty("jeecg.path.upload");
                if (uploadPath == null || uploadPath.isEmpty()) {
                    uploadPath = "/opt/upFiles"; // 默认路径
                }

                // 构建完整的文件路径
                String fullPath = uploadPath + File.separator + oldAvatarUrl;
                File oldFile = new File(fullPath);

                if (oldFile.exists() && oldFile.isFile()) {
                    boolean deleted = oldFile.delete();
                    if (deleted) {
                        log.info("🔍 deleteOldAvatarFile: 成功删除旧头像文件: {}", fullPath);
                    } else {
                        log.warn("🔍 deleteOldAvatarFile: 删除旧头像文件失败: {}", fullPath);
                    }
                } else {
                    log.info("🔍 deleteOldAvatarFile: 旧头像文件不存在或不是文件: {}", fullPath);
                }
            }
        } catch (Exception e) {
            log.error("🔍 deleteOldAvatarFile: 删除旧头像文件时发生异常: {}", oldAvatarUrl, e);
        }
    }

    /**
     * 获取今日API调用次数
     */
    private int getApiCallsToday(String userId) {
        try {
            // 查询今日API调用记录 - 修复：使用正确的表和字段
            String sql = "SELECT COUNT(*) FROM aicg_user_api_usage WHERE user_id = ? AND DATE(call_time) = CURDATE()";

            // 直接执行SQL查询
            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, userId);
            if (result != null && !result.isEmpty()) {
                Object count = result.get(0).get("COUNT(*)");
                return count != null ? Integer.parseInt(count.toString()) : 0;
            }
            return 0;
        } catch (Exception e) {
            log.error("获取今日API调用次数失败", e);
            return 0;
        }
    }

    /**
     * 获取本月API调用次数
     */
    private int getApiCallsMonth(String userId) {
        try {
            // 查询本月API调用记录 - 修复：使用正确的表和字段
            String sql = "SELECT COUNT(*) FROM aicg_user_api_usage WHERE user_id = ? AND YEAR(call_time) = YEAR(CURDATE()) AND MONTH(call_time) = MONTH(CURDATE())";

            // 直接执行SQL查询
            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, userId);
            if (result != null && !result.isEmpty()) {
                Object count = result.get(0).get("COUNT(*)");
                return count != null ? Integer.parseInt(count.toString()) : 0;
            }
            return 0;
        } catch (Exception e) {
            log.error("获取本月API调用次数失败", e);
            return 0;
        }
    }

    /**
     * 计算会员剩余天数
     */
    private int calculateMemberDaysLeft(Date memberExpireTime) {
        if (memberExpireTime == null) {
            return 0;
        }

        Date now = new Date();
        if (memberExpireTime.before(now)) {
            return 0;
        }

        long diffInMillies = memberExpireTime.getTime() - now.getTime();
        return (int) (diffInMillies / (1000 * 60 * 60 * 24));
    }

    /**
     * 获取本周API调用次数
     */
    private int getApiCallsWeek(String userId) {
        try {
            String sql = "SELECT COUNT(*) FROM aicg_user_api_usage WHERE user_id = ? AND YEARWEEK(call_time) = YEARWEEK(CURDATE())";

            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, userId);
            if (result != null && !result.isEmpty()) {
                Object count = result.get(0).get("COUNT(*)");
                return count != null ? Integer.parseInt(count.toString()) : 0;
            }
            return 0;
        } catch (Exception e) {
            log.error("获取本周API调用次数失败", e);
            return 0;
        }
    }

    /**
     * 获取全部API调用次数
     */
    private int getApiCallsAll(String userId) {
        try {
            String sql = "SELECT COUNT(*) FROM aicg_user_api_usage WHERE user_id = ?";

            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, userId);
            if (result != null && !result.isEmpty()) {
                Object count = result.get(0).get("COUNT(*)");
                return count != null ? Integer.parseInt(count.toString()) : 0;
            }
            return 0;
        } catch (Exception e) {
            log.error("获取全部API调用次数失败", e);
            return 0;
        }
    }

    /**
     * 更新用户头像
     */
    @AutoLog(value = "更新用户头像")
    @ApiOperation(value="更新用户头像", notes="更新用户头像")
    @PostMapping(value = "/updateAvatar")
    public Result<?> updateAvatar(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.error("请上传图片文件");
            }

            // 验证文件大小（2MB）
            if (file.getSize() > 2 * 1024 * 1024) {
                return Result.error("图片大小不能超过2MB");
            }

            // 上传文件
            String uploadType = "local"; // 可以从配置中读取
            String bizPath = "avatar"; // 头像专用目录
            String avatarUrl = CommonUtils.upload(file, bizPath, uploadType);

            if (avatarUrl == null || avatarUrl.isEmpty()) {
                return Result.error("文件上传失败");
            }

            // 更新用户头像
            userProfile.setAvatar(avatarUrl);
            userProfile.setUpdateTime(new Date());
            userProfileService.updateById(userProfile);

            Map<String, Object> result = new HashMap<>();
            result.put("avatar", avatarUrl);

            return Result.OK(result);
        } catch (Exception e) {
            log.error("更新用户头像失败", e);
            return Result.error("更新头像失败");
        }
    }

    /**
     * 更新用户头像URL到数据库（同时更新sys_user和aicg_user_profile两个表，并删除旧头像）
     */
    @PostMapping("/updateAvatarUrl")
    public Result<?> updateAvatarUrl(@RequestBody Map<String, String> params, HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            if (username == null) {
                return Result.error("用户未登录");
            }

            String newAvatarUrl = params.get("avatar");
            if (newAvatarUrl == null || newAvatarUrl.trim().isEmpty()) {
                return Result.error("头像URL不能为空");
            }

            // 获取旧头像路径，用于后续删除
            String oldAvatarUrl = null;
            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile != null) {
                oldAvatarUrl = userProfile.getAvatar();
            }

            // 1. 更新sys_user表的头像（后台管理系统使用）
            SysUser sysUser = sysUserService.getUserByName(username);
            if (sysUser != null) {
                // 如果userProfile中没有旧头像，尝试从sys_user中获取
                if (oldAvatarUrl == null || oldAvatarUrl.trim().isEmpty()) {
                    oldAvatarUrl = sysUser.getAvatar();
                }
                sysUser.setAvatar(newAvatarUrl);
                sysUser.setUpdateTime(new Date());
                sysUserService.updateById(sysUser);
                log.info("🔍 updateAvatarUrl: 已更新sys_user表头像，用户: {}, 新头像: {}", username, newAvatarUrl);
            }

            // 2. 更新aicg_user_profile表的头像（官网个人中心使用）
            if (userProfile == null) {
                return Result.error("用户扩展信息不存在");
            }

            userProfile.setAvatar(newAvatarUrl);
            userProfile.setUpdateTime(new Date());
            userProfileService.updateById(userProfile);
            log.info("🔍 updateAvatarUrl: 已更新aicg_user_profile表头像，用户: {}, 新头像: {}", username, newAvatarUrl);

            // 3. 删除旧头像文件（如果存在且不是默认头像）
            if (oldAvatarUrl != null && !oldAvatarUrl.trim().isEmpty() &&
                !oldAvatarUrl.equals(newAvatarUrl) &&
                !isDefaultAvatar(oldAvatarUrl)) {
                deleteOldAvatarFile(oldAvatarUrl);
            }

            return Result.OK("头像更新成功");
        } catch (Exception e) {
            log.error("更新用户头像URL失败", e);
            return Result.error("更新头像URL失败");
        }
    }

    /**
     * 更新用户基本信息
     */
    @PostMapping("/updateUserInfo")
    public Result<?> updateUserInfo(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            if (username == null) {
                return Result.error("用户未登录");
            }

            // 获取要更新的邮箱
            String email = (String) params.get("email");
            if (email == null || email.trim().isEmpty()) {
                return Result.error("邮箱不能为空");
            }

            // 更新系统用户表的邮箱
            SysUser sysUser = sysUserService.getOne(
                new QueryWrapper<SysUser>().eq("username", username)
            );

            if (sysUser == null) {
                return Result.error("用户不存在");
            }

            // 🛡️ 关键修复：检查邮箱是否已被其他用户使用
            if (!email.equals(sysUser.getEmail())) {
                // 只有邮箱发生变化时才检查重复性
                SysUser existingUser = sysUserService.getOne(
                    new QueryWrapper<SysUser>()
                        .eq("email", email)
                        .ne("id", sysUser.getId()) // 排除当前用户
                );

                if (existingUser != null) {
                    return Result.error("该邮箱已被其他用户使用，请选择其他邮箱");
                }
            }

            sysUser.setEmail(email);
            sysUser.setUpdateTime(new Date());
            sysUserService.updateById(sysUser);

            // 🔄 同步更新用户扩展表的邮箱
            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile != null) {
                userProfile.setEmail(email);
                userProfile.setUpdateTime(new Date());
                userProfileService.updateById(userProfile);
            }

            return Result.OK("用户基本信息更新成功");
        } catch (Exception e) {
            log.error("更新用户基本信息失败", e);
            return Result.error("更新用户基本信息失败");
        }
    }

    /**
     * 更新用户扩展信息
     */
    @PostMapping("/updateUserProfile")
    public Result<?> updateUserProfile(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            if (username == null) {
                return Result.error("用户未登录");
            }

            // 获取要更新的字段
            String nickname = (String) params.get("nickname");
            String phone = (String) params.get("phone");
            String avatar = (String) params.get("avatar");

            // 🛡️ 安全检查：如果要更新昵称，先进行敏感词校验
            if (nickname != null && !nickname.trim().isEmpty()) {
                // 获取当前用户ID用于重复性检查
                AicgUserProfile currentProfile = userProfileService.getByUsername(username);
                String currentUserId = currentProfile != null ? currentProfile.getUserId() : username;

                // 执行完整的昵称校验（包括敏感词、重复性、格式等）
                ISensitiveWordService.NicknameValidationResult validationResult =
                    sensitiveWordService.validateNickname(nickname, currentUserId);

                if (!validationResult.isValid()) {
                    log.warn("🚨 用户尝试保存无效昵称 - 用户: {}, 昵称: {}, 原因: {}",
                        username, nickname, validationResult.getMessage());
                    return Result.error("昵称校验失败：" + validationResult.getMessage());
                }

                log.info("✅ 昵称校验通过 - 用户: {}, 昵称: {}", username, nickname);
            }

            // 更新系统用户表的手机号
            if (phone != null) {
                SysUser sysUser = sysUserService.getUserByName(username);
                if (sysUser != null) {
                    // 🛡️ 关键修复：检查手机号是否已被其他用户使用
                    if (!phone.equals(sysUser.getPhone())) {
                        // 只有手机号发生变化时才检查重复性
                        SysUser existingUser = sysUserService.getOne(
                            new QueryWrapper<SysUser>()
                                .eq("phone", phone)
                                .ne("id", sysUser.getId()) // 排除当前用户
                        );

                        if (existingUser != null) {
                            return Result.error("该手机号已被其他用户使用，请选择其他手机号");
                        }
                    }

                    sysUser.setPhone(phone);
                    sysUser.setUpdateTime(new Date());
                    sysUserService.updateById(sysUser);
                }
            }

            // 查询用户扩展信息
            AicgUserProfile userProfile = userProfileService.getByUsername(username);

            if (userProfile == null) {
                return Result.error("用户扩展信息不存在");
            }

            // 更新扩展信息字段
            if (nickname != null) {
                userProfile.setNickname(nickname);
            }
            if (phone != null) {
                userProfile.setPhone(phone); // 同步更新扩展表的手机号
            }
            if (avatar != null) {
                userProfile.setAvatar(avatar);
            }

            userProfile.setUpdateTime(new Date());
            userProfileService.updateById(userProfile);

            return Result.OK("用户扩展信息更新成功");
        } catch (Exception e) {
            log.error("更新用户扩展信息失败", e);
            return Result.error("更新用户扩展信息失败");
        }
    }

    /**
     * 修改密码
     */
    @AutoLog(value = "修改密码")
    @ApiOperation(value="修改密码", notes="修改密码")
    @PostMapping(value = "/changePassword")
    public Result<?> changePassword(@RequestBody Map<String, String> passwordData, HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            log.info("🔍 changePassword - 用户名: {}", username);

            String oldPassword = passwordData.get("oldPassword");
            String newPassword = passwordData.get("newPassword");
            String confirmPassword = passwordData.get("confirmPassword");

            // 获取用户扩展信息，检查是否首次修改密码
            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            boolean isFirstTimePassword = (userProfile == null || userProfile.getPasswordChanged() == null || userProfile.getPasswordChanged() == 0);

            // 基础验证
            if (oConvertUtils.isEmpty(newPassword) || oConvertUtils.isEmpty(confirmPassword)) {
                return Result.error("密码不能为空");
            }

            if (!newPassword.equals(confirmPassword)) {
                return Result.error("两次输入的新密码不一致");
            }

            // 🔑 增强的密码强度验证
            String passwordValidationResult = validatePasswordStrength(newPassword);
            if (passwordValidationResult != null) {
                return Result.error(passwordValidationResult);
            }

            // 获取用户信息
            SysUser user = sysUserService.getUserByName(username);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 🔑 关键逻辑：根据是否首次修改密码决定验证策略
            if (!isFirstTimePassword) {
                // 非首次修改，需要验证旧密码
                if (oConvertUtils.isEmpty(oldPassword)) {
                    return Result.error("请输入当前密码");
                }

                // 验证旧密码
                String oldPasswordEncrypted = PasswordUtil.encrypt(username, oldPassword, user.getSalt());
                if (!oldPasswordEncrypted.equals(user.getPassword())) {
                    return Result.error("当前密码错误");
                }

                // 🔑 检查新密码是否与旧密码相同
                if (newPassword.equals(oldPassword)) {
                    return Result.error("新密码不能与当前密码相同");
                }
            } else {
                // 首次修改密码，不需要验证旧密码
                log.info("🔍 changePassword - 用户 {} 首次设置密码", username);
            }

            // 生成新密码
            String newSalt = oConvertUtils.randomGen(8);
            String newPasswordEncrypted = PasswordUtil.encrypt(username, newPassword, newSalt);

            // 更新用户密码
            user.setSalt(newSalt);
            user.setPassword(newPasswordEncrypted);
            user.setUpdateTime(new Date());
            sysUserService.updateById(user);

            // 🔑 关键：更新密码修改状态
            if (userProfile != null) {
                userProfile.setPasswordChanged(1); // 标记为已修改过密码
                userProfile.setUpdateTime(new Date());
                userProfileService.updateById(userProfile);
                log.info("🔍 changePassword - 已更新用户密码状态为已修改: {}", username);
            }

            log.info("🔍 changePassword - 密码修改成功: {}", username);
            return Result.OK("密码修改成功");
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return Result.error("修改密码失败");
        }
    }

    /**
     * 支付订单
     */
    @AutoLog(value = "支付订单")
    @ApiOperation(value="支付订单", notes="支付订单")
    @PostMapping(value = "/payOrder")
    public Result<?> payOrder(@RequestBody Map<String, Object> requestData,
                             HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);
            String orderId = (String) requestData.get("orderId");

            log.info("💰 payOrder - 用户: {}, 订单ID: {}", username, orderId);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            // 查询订单信息
            String querySql = "SELECT * FROM aicg_user_transaction WHERE related_order_id = ? AND user_id = ?";
            List<Map<String, Object>> orderResult = jdbcTemplate.queryForList(querySql, orderId, userProfile.getUserId());

            if (orderResult.isEmpty()) {
                return Result.error("订单不存在");
            }

            Map<String, Object> order = orderResult.get(0);
            Integer orderStatus = (Integer) order.get("order_status");

            if (orderStatus != 1) {
                return Result.error("订单状态不正确，无法支付");
            }

            BigDecimal orderAmount = (BigDecimal) order.get("amount");
            BigDecimal currentBalance = userProfile.getAccountBalance();

            // 检查余额是否足够
            if (currentBalance.compareTo(orderAmount) < 0) {
                return Result.error("账户余额不足，当前余额：¥" + currentBalance + "，需要：¥" + orderAmount);
            }

            // 计算支付后余额
            BigDecimal newBalance = currentBalance.subtract(orderAmount);

            // 更新订单状态和支付信息
            String updateSql = "UPDATE aicg_user_transaction SET " +
                "order_status = 3, " +  // 直接到已完成，跳过已支付环节
                "balance_before = ?, " +
                "balance_after = ?, " +
                "transaction_time = ?, " +
                "update_time = ?, " +
                "update_by = ? " +
                "WHERE related_order_id = ? AND user_id = ?";

            Date paymentTime = new Date();
            jdbcTemplate.update(updateSql,
                currentBalance,
                newBalance,
                paymentTime,
                paymentTime,
                username,
                orderId,
                userProfile.getUserId()
            );

            // 更新用户余额
            userProfile.setAccountBalance(newBalance);
            userProfile.setTotalConsumption(userProfile.getTotalConsumption().add(orderAmount));
            userProfile.setUpdateTime(paymentTime);
            userProfileService.updateById(userProfile);

            Map<String, Object> result = new HashMap<>();
            result.put("orderId", orderId);
            result.put("paymentTime", paymentTime);
            result.put("amount", orderAmount);
            result.put("newBalance", newBalance);
            result.put("status", "success");

            log.info("💰 payOrder - 支付成功: {}", result);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("💰 payOrder - 支付失败", e);
            return Result.error("支付失败: " + e.getMessage());
        }
    }

    /**
     * 获取API成功率
     */
    private double getApiSuccessRate(String userId) {
        try {
            // 查询总调用次数
            String totalSql = "SELECT COUNT(*) FROM aicg_user_api_usage WHERE user_id = ?";
            List<Map<String, Object>> totalResult = jdbcTemplate.queryForList(totalSql, userId);
            long totalCalls = totalResult.isEmpty() ? 0 : Long.parseLong(totalResult.get(0).get("COUNT(*)").toString());

            if (totalCalls == 0) {
                return 0.0;
            }

            // 查询成功调用次数（响应状态码200）
            String successSql = "SELECT COUNT(*) FROM aicg_user_api_usage WHERE user_id = ? AND response_status = 200";
            List<Map<String, Object>> successResult = jdbcTemplate.queryForList(successSql, userId);
            long successCalls = successResult.isEmpty() ? 0 : Long.parseLong(successResult.get(0).get("COUNT(*)").toString());

            // 计算成功率
            double successRate = (double) successCalls / totalCalls * 100;
            return Math.round(successRate * 100.0) / 100.0; // 保留两位小数
        } catch (Exception e) {
            log.error("获取API成功率失败", e);
            return 0.0;
        }
    }

    /**
     * 获取插件分类文本 - 使用字典服务
     */
    private String getCategoryText(String category) {
        if (category == null || category.isEmpty()) {
            return "未知分类";
        }

        try {
            // 🔧 使用字典服务获取插件分类的中文显示
            String dictText = sysDictService.queryDictTextByKey("plugin_category", category);
            if (dictText != null && !dictText.isEmpty()) {
                log.debug("🎯 从字典获取分类文本 - 代码: {}, 文本: {}", category, dictText);
                return dictText;
            } else {
                log.warn("🎯 字典中未找到分类 - 代码: {}, 使用兜底处理", category);
                return getFallbackCategoryText(category);
            }
        } catch (Exception e) {
            log.error("🎯 获取字典分类文本失败 - 代码: {}, 错误: {}", category, e.getMessage());
            return getFallbackCategoryText(category);
        }
    }

    /**
     * 兜底的分类文本获取方法
     */
    private String getFallbackCategoryText(String category) {
        if (category == null || category.isEmpty()) {
            return "未知分类";
        }

        // 处理常见的英文分类模式作为兜底
        if (category.contains("share")) {
            return "分享工具";
        } else if (category.contains("social")) {
            return "社交工具";
        } else if (category.contains("ai")) {
            return "AI工具";
        } else if (category.contains("content")) {
            return "内容工具";
        } else if (category.contains("image")) {
            return "图像工具";
        } else if (category.contains("video")) {
            return "视频工具";
        } else if (category.contains("audio")) {
            return "音频工具";
        } else if (category.contains("text") || category.contains("chat")) {
            return "文本工具";
        } else if (category.contains("util") || category.contains("tool")) {
            return "实用工具";
        } else if (category.contains("data")) {
            return "数据工具";
        } else if (category.contains("web") || category.contains("api")) {
            return "网络工具";
        } else {
            // 如果无法识别，返回首字母大写的原始值
            return category.substring(0, 1).toUpperCase() + category.substring(1);
        }
    }

    /**
     * 从请求参数中提取插件Key
     */
    private String extractPluginKey(String requestParams) {
        if (requestParams == null || requestParams.isEmpty()) {
            return null;
        }

        // 查找 "pluginKey: " 模式（确保前面是开头或逗号分隔符）
        String pattern = "pluginKey: ";
        int index = requestParams.indexOf(pattern);
        if (index == -1) {
            return null;
        }

        // 确保这是一个完整的参数，而不是其他参数值的一部分
        if (index > 0) {
            char prevChar = requestParams.charAt(index - 1);
            if (prevChar != ',' && prevChar != ' ' && prevChar != '\n') {
                return null;
            }
        }

        // 提取插件Key
        String remaining = requestParams.substring(index + pattern.length());
        int commaIndex = remaining.indexOf(",");
        String pluginKey;
        if (commaIndex != -1) {
            pluginKey = remaining.substring(0, commaIndex).trim();
        } else {
            pluginKey = remaining.trim();
        }

        // 验证插件Key格式（只包含字母、数字、下划线、连字符）
        if (pluginKey.matches("^[a-zA-Z0-9_-]+$")) {
            return pluginKey;
        }

        return null;
    }

    /**
     * 根据API端点推断分类（返回中文分类名称）
     */
    private String inferCategoryFromEndpoint(String apiEndpoint) {
        if (apiEndpoint == null) {
            return "未知分类";
        }

        // 先推断可能的分类代码
        String categoryCode = null;
        if (apiEndpoint.contains("share") || apiEndpoint.contains("social")) {
            categoryCode = "social-share";
        } else if (apiEndpoint.contains("xiaohongshu") || apiEndpoint.contains("xhs")) {
            categoryCode = "social-share"; // 小红书分享也属于社交分享
        } else if (apiEndpoint.contains("text") || apiEndpoint.contains("chat")) {
            categoryCode = "ai-chat";
        } else if (apiEndpoint.contains("image") || apiEndpoint.contains("picture")) {
            categoryCode = "ai-image";
        } else if (apiEndpoint.contains("video")) {
            categoryCode = "ai-video";
        } else if (apiEndpoint.contains("audio") || apiEndpoint.contains("voice")) {
            categoryCode = "ai-audio";
        } else if (apiEndpoint.contains("content")) {
            categoryCode = "content-creation";
        } else if (apiEndpoint.contains("util") || apiEndpoint.contains("tool")) {
            categoryCode = "utility";
        } else if (apiEndpoint.contains("data")) {
            categoryCode = "data-processing";
        } else if (apiEndpoint.contains("web") || apiEndpoint.contains("api")) {
            categoryCode = "web-service";
        }

        // 如果推断出了分类代码，使用字典服务转换为中文
        if (categoryCode != null) {
            return getCategoryText(categoryCode);
        } else {
            // 如果无法推断，使用兜底处理
            return getFallbackCategoryText(apiEndpoint);
        }
    }

    /**
     * 判断记录是否应该包含在结果中（应用筛选条件）
     */
    private boolean shouldIncludeRecord(Map<String, Object> record, String category, String pluginKeyword) {
        // 分类筛选
        if (category != null && !category.isEmpty()) {
            String recordCategory = (String) record.get("category");
            if (!category.equals(recordCategory)) {
                return false;
            }
        }

        // 关键词筛选
        if (pluginKeyword != null && !pluginKeyword.isEmpty()) {
            String pluginName = (String) record.get("plugin_name");
            if (pluginName == null || !pluginName.toLowerCase().contains(pluginKeyword.toLowerCase())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 导出交易记录
     */
    @AutoLog(value = "导出交易记录")
    @ApiOperation(value="导出交易记录", notes="导出交易记录")
    @GetMapping(value = "/exportTransactions")
    public void exportTransactions(@RequestParam(name="transactionType", required=false) String transactionType,
                                  @RequestParam(name="status", required=false) String status,
                                  @RequestParam(name="startDate", required=false) String startDate,
                                  @RequestParam(name="endDate", required=false) String endDate,
                                  @RequestParam(name="keyword", required=false) String keyword,
                                  HttpServletRequest request,
                                  HttpServletResponse response) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }

            String userId = userProfile.getUserId();
            log.info("🎯 导出交易记录 - 用户: {}, 类型: {}, 状态: {}", username, transactionType, status);

            // 构建查询SQL - 只导出已完成的交易记录
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT transaction_time as '交易时间', ");
            sqlBuilder.append("CASE transaction_type ");
            sqlBuilder.append("  WHEN 1 THEN '消费' ");
            sqlBuilder.append("  WHEN 2 THEN '充值' ");
            sqlBuilder.append("  WHEN 3 THEN '退款' ");
            sqlBuilder.append("  WHEN 4 THEN '奖励' ");
            sqlBuilder.append("  WHEN 5 THEN '会员订阅' ");
            sqlBuilder.append("  ELSE '未知' END as '交易类型', ");
            sqlBuilder.append("description as '交易描述', ");
            sqlBuilder.append("amount as '交易金额', ");
            sqlBuilder.append("balance_after as '交易后余额' ");
            sqlBuilder.append("FROM aicg_user_transaction WHERE user_id = ? ");

            // 🔧 关键修复：只导出已完成的交易记录，与页面显示保持一致
            sqlBuilder.append("AND order_status = 3 "); // 只导出已完成状态

            List<Object> params = new ArrayList<>();
            params.add(userId);

            // 添加筛选条件
            if (transactionType != null && !transactionType.isEmpty()) {
                sqlBuilder.append("AND transaction_type = ? ");
                params.add(Integer.parseInt(transactionType));
            }

            if (status != null && !status.isEmpty()) {
                sqlBuilder.append("AND order_status = ? ");
                params.add(Integer.parseInt(status));
            }

            if (startDate != null && !startDate.isEmpty()) {
                sqlBuilder.append("AND DATE(transaction_time) >= ? ");
                params.add(startDate);
            }

            if (endDate != null && !endDate.isEmpty()) {
                sqlBuilder.append("AND DATE(transaction_time) <= ? ");
                params.add(endDate);
            }

            if (keyword != null && !keyword.isEmpty()) {
                sqlBuilder.append("AND description LIKE ? ");
                params.add("%" + keyword + "%");
            }

            sqlBuilder.append("ORDER BY transaction_time DESC");

            // 查询数据
            List<Map<String, Object>> dataList = jdbcTemplate.queryForList(sqlBuilder.toString(), params.toArray());

            // 创建Excel工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("交易记录");

            // 创建表头 - 移除状态列，因为都是已完成状态
            Row headerRow = sheet.createRow(0);
            String[] headers = {"交易时间", "交易类型", "交易描述", "交易金额", "交易后余额"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // 填充数据 - 移除状态列
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                Map<String, Object> data = dataList.get(i);

                row.createCell(0).setCellValue(data.get("交易时间") != null ? data.get("交易时间").toString() : "");
                row.createCell(1).setCellValue(data.get("交易类型") != null ? data.get("交易类型").toString() : "");
                row.createCell(2).setCellValue(data.get("交易描述") != null ? data.get("交易描述").toString() : "");
                row.createCell(3).setCellValue(data.get("交易金额") != null ? data.get("交易金额").toString() : "");
                row.createCell(4).setCellValue(data.get("交易后余额") != null ? data.get("交易后余额").toString() : "");
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=transactions.xlsx");

            // 写入响应
            workbook.write(response.getOutputStream());
            workbook.close();

            log.info("🎯 导出交易记录成功 - 用户: {}, 记录数: {}", username, dataList.size());

        } catch (Exception e) {
            log.error("🎯 导出交易记录失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 计算用户本月消费金额
     * @param userId 用户ID
     * @return 本月消费金额
     */
    private BigDecimal calculateMonthlyConsumption(String userId) {
        try {
            // 使用SQL直接计算当月消费，性能更好
            String sql = "SELECT COALESCE(SUM(amount), 0) FROM aicg_user_transaction " +
                        "WHERE user_id = ? " +
                        "AND transaction_type = 1 " +  // 消费类型
                        "AND order_status = 3 " +      // 已完成状态
                        "AND DATE_FORMAT(transaction_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')"; // 当月

            BigDecimal result = jdbcTemplate.queryForObject(sql, BigDecimal.class, userId);
            log.info("💰 计算用户本月消费 - 用户ID: {}, 本月消费: {}", userId, result);
            return result != null ? result : BigDecimal.ZERO;

        } catch (Exception e) {
            log.error("💰 计算用户本月消费失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 密码强度验证
     * @param password 待验证的密码
     * @return 验证失败的错误信息，验证通过返回null
     */
    private String validatePasswordStrength(String password) {
        if (password == null || password.isEmpty()) {
            return "密码不能为空";
        }

        // 长度验证
        if (password.length() < 8) {
            return "密码长度至少8位";
        }
        if (password.length() > 32) {
            return "密码长度不能超过32位";
        }

        // 字符类型验证 - 至少包含3种
        int charTypeCount = 0;
        if (password.matches(".*[A-Z].*")) charTypeCount++;      // 大写字母
        if (password.matches(".*[a-z].*")) charTypeCount++;      // 小写字母
        if (password.matches(".*\\d.*")) charTypeCount++;        // 数字
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) charTypeCount++; // 特殊字符

        if (charTypeCount < 3) {
            return "密码至少包含3种字符类型（大写字母、小写字母、数字、特殊字符）";
        }

        // 弱密码模式检查
        if (password.matches("(.)\\1{3,}")) {
            return "密码不能包含连续4个相同字符";
        }

        if (password.matches("^123456.*")) {
            return "密码不能以123456开头";
        }

        if (password.toLowerCase().equals("password")) {
            return "密码不能是password";
        }

        // 常见弱密码检查
        String[] commonPasswords = {
            "12345678", "password", "password1", "admin123", "user1234",
            "qwerty123", "abc123456", "password123", "admin1234", "123456789"
        };

        for (String commonPassword : commonPasswords) {
            if (password.toLowerCase().equals(commonPassword.toLowerCase())) {
                return "这是一个常见的弱密码，请选择更安全的密码";
            }
        }

        return null; // 验证通过
    }

    /**
     * 生成邀请码（备用方案）
     * 当新的统一服务失败时使用此方法
     * @param userId 用户ID
     * @return 邀请码
     */
    private String generateInviteCodeFallback(String userId) {
        String inviteCode;
        int maxAttempts = 10;
        int attempts = 0;

        do {
            inviteCode = generateRandomInviteCode();
            attempts++;
        } while (userProfileService.getByInviteCode(inviteCode) != null && attempts < maxAttempts);

        if (attempts >= maxAttempts) {
            // 如果随机生成失败，使用用户ID生成
            inviteCode = "INV" + userId.substring(0, Math.min(5, userId.length())).toUpperCase();
        }

        return inviteCode;
    }

    /**
     * 生成随机邀请码
     */
    private String generateRandomInviteCode() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder code = new StringBuilder();

        for (int i = 0; i < registerConfig.getInviteCode().getLength(); i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }

        return code.toString();
    }

    /**
     * 获取推荐用户列表
     */
    @AutoLog(value = "获取推荐用户列表")
    @ApiOperation(value="获取推荐用户列表", notes="获取推荐用户列表")
    @GetMapping(value = "/referralList")
    public Result<?> getReferralList(@RequestParam(name="current", defaultValue="1") Integer current,
                                    @RequestParam(name="size", defaultValue="10") Integer size,
                                    HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            String userId = userProfile.getUserId();

            // 查询推荐用户列表，关联用户信息和奖励记录
            String sql = "SELECT r.id, r.referee_id, r.register_time, r.status, " +
                        "u.realname as referee_nickname, u.avatar as referee_avatar, " +
                        "CASE WHEN m.id IS NOT NULL THEN 1 ELSE 0 END as has_membership, " +
                        "COALESCE(SUM(rw.reward_amount), 0) as total_reward " +
                        "FROM aicg_user_referral r " +
                        "LEFT JOIN sys_user u ON r.referee_id COLLATE utf8mb4_general_ci = u.id COLLATE utf8mb4_general_ci " +
                        "LEFT JOIN aicg_user_membership_history m ON r.referee_id COLLATE utf8mb4_general_ci = m.user_id COLLATE utf8mb4_general_ci " +
                        "LEFT JOIN aicg_user_referral_reward rw ON r.id COLLATE utf8mb4_general_ci = rw.referral_id COLLATE utf8mb4_general_ci " +
                        "WHERE r.referrer_id = ? " +
                        "GROUP BY r.id, r.referee_id, r.register_time, r.status, u.realname, u.avatar " +
                        "ORDER BY r.register_time DESC " +
                        "LIMIT ?, ?";

            int offset = (current - 1) * size;
            List<Map<String, Object>> records = jdbcTemplate.queryForList(sql, userId, offset, size);

            // 查询总数
            String countSql = "SELECT COUNT(*) FROM aicg_user_referral WHERE referrer_id = ?";
            Integer total = jdbcTemplate.queryForObject(countSql, Integer.class, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("records", records);
            result.put("total", total);
            result.put("current", current);
            result.put("size", size);

            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取推荐用户列表失败", e);
            return Result.error("获取推荐用户列表失败");
        }
    }

    /**
     * 获取提现记录
     */
    @AutoLog(value = "获取提现记录")
    @ApiOperation(value="获取提现记录", notes="获取提现记录")
    @GetMapping(value = "/withdrawalHistory")
    public Result<?> getWithdrawalHistory(@RequestParam(name="current", defaultValue="1") Integer current,
                                         @RequestParam(name="size", defaultValue="10") Integer size,
                                         HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            String username = JwtUtil.getUsername(token);

            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return Result.error("用户信息不存在");
            }

            // 暂时返回空数据，因为还没有提现表
            // TODO: 创建提现记录表后实现真实查询
            Map<String, Object> result = new HashMap<>();
            result.put("records", new ArrayList<>());
            result.put("total", 0);
            result.put("current", current);
            result.put("size", size);

            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取提现记录失败", e);
            return Result.error("获取提现记录失败");
        }
    }

    /**
     * 生成推广二维码
     */
    @AutoLog(value = "生成推广二维码")
    @ApiOperation(value="生成推广二维码", notes="生成推广二维码")
    @PostMapping(value = "/generateReferralQRCode")
    public Result<?> generateReferralQRCode(@RequestParam("url") String url, HttpServletRequest req) {
        try {
            log.info("开始生成推广二维码，URL: {}", url);

            // 从URL中提取域名
            java.net.URL urlObj = new java.net.URL(url);
            String domain = urlObj.getHost();
            int port = urlObj.getPort();
            String domainKey = domain.replace(".", "_");
            if (port != -1 && port != 80 && port != 443) {
                domainKey += "_" + port;
            }

            // 生成基于URL的唯一标识符（避免重复生成）
            String urlHash = String.valueOf(url.hashCode()).replace("-", "");
            String fileName = "qr_" + urlHash + ".png";

            // 生成TOS存储路径：qrcode/domain/qr_hash.png
            String objectKey = "qrcode/" + domainKey + "/" + fileName;
            String cdnUrl = "https://cdn.aigcview.com/" + objectKey;

            log.info("域名: {}, 存储路径: {}", domain, objectKey);

            // 检查TOS中是否已存在该二维码（通过HTTP HEAD请求）
            try {
                java.net.HttpURLConnection connection = (java.net.HttpURLConnection)
                    new java.net.URL(cdnUrl).openConnection();
                connection.setRequestMethod("HEAD");
                connection.setConnectTimeout(3000);
                connection.setReadTimeout(3000);

                int responseCode = connection.getResponseCode();
                if (responseCode == 200) {
                    log.info("二维码已存在，直接返回CDN地址: {}", cdnUrl);
                    return Result.OK("二维码已存在", cdnUrl);
                }
            } catch (Exception e) {
                // 对象不存在或网络错误，继续生成新的二维码
                log.info("二维码不存在或检查失败，开始生成新的二维码: {}", e.getMessage());
            }

            // 生成二维码到临时文件
            String tempFileName = "temp_qr_" + System.currentTimeMillis() + ".png";
            String tempFilePath = System.getProperty("java.io.tmpdir") + File.separator + tempFileName;

            // 使用QRCodeUtil生成二维码
            QRCodeUtil.generateQRCode(url, 300, 300, tempFilePath);

            // 美化二维码
            String beautifiedFilePath = beautifyQRCode(tempFilePath);

            // 读取美化后的二维码文件
            File qrFile = new File(beautifiedFilePath);
            byte[] qrData = java.nio.file.Files.readAllBytes(qrFile.toPath());

            // 删除临时文件
            new File(tempFilePath).delete(); // 删除原始二维码

            // 上传到TOS通用桶
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(qrData)) {
                // 使用TOS客户端直接上传
                com.volcengine.tos.model.object.PutObjectInput putObjectInput =
                    new com.volcengine.tos.model.object.PutObjectInput()
                        .setBucket("aigcview-tos")
                        .setKey(objectKey)
                        .setContent(inputStream)
                        .setContentLength((long) qrData.length);

                // 通过反射获取TOS客户端
                java.lang.reflect.Field tosClientField = tosService.getClass().getDeclaredField("tosClient");
                tosClientField.setAccessible(true);
                com.volcengine.tos.TOSV2 tosClient = (com.volcengine.tos.TOSV2) tosClientField.get(tosService);

                tosClient.putObject(putObjectInput);

                // 删除临时文件
                qrFile.delete(); // 删除美化后的文件

                log.info("推广二维码生成成功，CDN地址: {}", cdnUrl);

                return Result.OK("二维码生成成功", cdnUrl);
            }

        } catch (Exception e) {
            log.error("生成推广二维码失败", e);
            return Result.error("生成二维码失败：" + e.getMessage());
        }
    }

    /**
     * 美化二维码 - 添加边框、文字和样式
     */
    private String beautifyQRCode(String originalFilePath) throws Exception {
        // 读取原始二维码
        java.awt.image.BufferedImage originalImage = javax.imageio.ImageIO.read(new File(originalFilePath));

        // 创建更大的画布 (400x450) 为文字留空间
        int canvasWidth = 400;
        int canvasHeight = 450;
        java.awt.image.BufferedImage canvas = new java.awt.image.BufferedImage(
            canvasWidth, canvasHeight, java.awt.image.BufferedImage.TYPE_INT_RGB);

        java.awt.Graphics2D g2d = canvas.createGraphics();

        // 设置抗锯齿
        g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING,
                            java.awt.RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(java.awt.RenderingHints.KEY_TEXT_ANTIALIASING,
                            java.awt.RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 设置白色背景
        g2d.setColor(java.awt.Color.WHITE);
        g2d.fillRect(0, 0, canvasWidth, canvasHeight);

        // 绘制圆角边框
        g2d.setColor(new java.awt.Color(24, 144, 255)); // 蓝色边框
        g2d.setStroke(new java.awt.BasicStroke(3));
        g2d.drawRoundRect(10, 10, canvasWidth - 20, canvasHeight - 20, 20, 20);

        // 绘制二维码 (居中，留出边距)
        int qrSize = 300;
        int qrX = (canvasWidth - qrSize) / 2;
        int qrY = 30;
        g2d.drawImage(originalImage, qrX, qrY, qrSize, qrSize, null);

        // 绘制标题文字
        g2d.setColor(new java.awt.Color(24, 144, 255));
        g2d.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 18));
        String title = "智界AIGC - 推广二维码";
        java.awt.FontMetrics fm = g2d.getFontMetrics();
        int titleX = (canvasWidth - fm.stringWidth(title)) / 2;
        g2d.drawString(title, titleX, qrY + qrSize + 30);

        // 绘制说明文字
        g2d.setColor(new java.awt.Color(102, 102, 102));
        g2d.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        String desc = "扫码注册并订阅会员，推广者可获得佣金奖励";
        fm = g2d.getFontMetrics();
        int descX = (canvasWidth - fm.stringWidth(desc)) / 2;
        g2d.drawString(desc, descX, qrY + qrSize + 55);

        g2d.dispose();

        // 保存美化后的二维码
        String beautifiedFilePath = originalFilePath.replace(".png", "_beautified.png");
        javax.imageio.ImageIO.write(canvas, "PNG", new File(beautifiedFilePath));

        return beautifiedFilePath;
    }

    /**
     * 下载推广二维码
     */
    @AutoLog(value = "下载推广二维码")
    @ApiOperation(value="下载推广二维码", notes="下载推广二维码")
    @GetMapping(value = "/downloadReferralQRCode")
    public void downloadReferralQRCode(@RequestParam("url") String qrCodeUrl,
                                      HttpServletRequest request,
                                      HttpServletResponse response) {
        try {
            log.info("开始下载推广二维码，URL: {}", qrCodeUrl);

            // 通过HTTP请求获取二维码图片
            java.net.URL url = new java.net.URL(qrCodeUrl);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);

            if (connection.getResponseCode() == 200) {
                // 设置响应头，强制下载
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename=\"智界AIGC推广二维码.png\"");
                response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
                response.setHeader("Pragma", "no-cache");
                response.setHeader("Expires", "0");

                // 读取图片数据并写入响应
                try (java.io.InputStream inputStream = connection.getInputStream();
                     java.io.OutputStream outputStream = response.getOutputStream()) {

                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    outputStream.flush();
                }

                log.info("推广二维码下载成功");
            } else {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("二维码文件不存在");
            }

        } catch (Exception e) {
            log.error("下载推广二维码失败", e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("下载失败：" + e.getMessage());
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

}
