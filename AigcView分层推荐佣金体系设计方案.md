# AigcView分层推荐佣金体系设计方案

## 📋 项目概述

基于AigcView项目现有的分销推广系统，设计一个完善的分层推荐佣金体系，包含多级佣金规则、防刷机制和完整的技术实现方案。

---

## 💰 佣金体系设计

### 🎯 佣金等级体系

#### 1. **佣金等级定义**
```
Level 1 - 新手推广员
├── 普通用户：30% 基础佣金
├── VIP会员：35% 基础佣金
└── SVIP会员：50% 直接封顶佣金

Level 2 - 高级推广员 (邀请10人成功)
├── 普通用户：40% 佣金
├── VIP会员：45% 佣金
└── SVIP会员：50% 佣金

Level 3 - 顶级推广员 (邀请30人成功)
├── 普通用户：50% 佣金 (封顶)
├── VIP会员：50% 佣金 (封顶)
└── SVIP会员：50% 佣金 (封顶)
```

#### 2. **邀请成功标准**
```
邀请成功 = 被邀请人完成以下所有步骤：
1. 通过推荐链接注册账号
2. 订阅会员 (任意等级)
```

#### 3. **佣金计算规则**
```javascript
// 佣金计算公式
function calculateCommission(userLevel, memberType, inviteCount, orderAmount) {
    let baseRate = getBaseCommissionRate(memberType);
    let levelBonus = getLevelBonus(userLevel, memberType, inviteCount);
    let finalRate = Math.min(baseRate + levelBonus, 0.5); // 50%封顶
    
    return orderAmount * finalRate;
}

// 基础佣金率
function getBaseCommissionRate(memberType) {
    switch(memberType) {
        case 'NORMAL': return 0.30;    // 普通用户30%
        case 'VIP': return 0.35;       // VIP用户35%
        case 'SVIP': return 0.50;      // SVIP用户50%
        default: return 0.30;
    }
}

// 等级奖励
function getLevelBonus(userLevel, memberType, inviteCount) {
    if (memberType === 'SVIP') return 0; // SVIP已达封顶
    
    if (inviteCount >= 30) return 0.20;  // 顶级推广员
    if (inviteCount >= 10) return 0.10;  // 高级推广员
    return 0; // 新手推广员
}
```

### 📊 佣金结算机制

#### 1. **结算周期**
- **实时计算**：订单完成后立即计算佣金
- **每日结算**：每日凌晨2点批量处理前一日佣金
- **每周发放**：每周一发放上周确认的佣金
- **月度对账**：每月1号进行佣金对账和调整

#### 2. **佣金状态流转**
```
待计算 → 已计算 → 待审核 → 已确认 → 待发放 → 已发放 → 已提现
```

#### 3. **提现规则**
- **最低提现额度**：100元
- **提现手续费**：2% (最低2元，最高20元)
- **提现周期**：每周二、周五可申请，3个工作日内到账
- **风控审核**：单次提现≥1000元需要人工审核

---

## 🛡️ 防刷机制设计

### 🔍 技术层面防护

#### 1. **设备指纹识别**
```javascript
// 设备指纹生成
function generateDeviceFingerprint() {
    return {
        userAgent: navigator.userAgent,
        screenResolution: `${screen.width}x${screen.height}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        language: navigator.language,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        canvasFingerprint: generateCanvasFingerprint(),
        webglFingerprint: generateWebGLFingerprint()
    };
}
```

#### 2. **IP地址限制**
- **同IP注册限制**：24小时内同一IP最多注册3个账号
- **同IP邀请限制**：同一IP下的账号互相邀请无效
- **地理位置检测**：检测IP地址的地理位置一致性

#### 3. **实名认证关联**
- **身份证号唯一性**：一个身份证号只能认证一个账号
- **银行卡绑定**：提现账户与实名信息必须一致
- **人脸识别**：高额提现需要人脸识别验证

### 🕵️ 业务层面防护

#### 1. **邀请关系审核**
```sql
-- 异常邀请关系检测
SELECT 
    referrer_id,
    COUNT(*) as invite_count,
    COUNT(DISTINCT ip_address) as unique_ips,
    COUNT(DISTINCT device_fingerprint) as unique_devices,
    AVG(TIMESTAMPDIFF(MINUTE, register_time, first_recharge_time)) as avg_recharge_time
FROM aicg_user_referral r
JOIN aicg_user_profile p ON r.referee_id = p.user_id
WHERE r.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY referrer_id
HAVING invite_count > 5 
   AND (unique_ips < invite_count * 0.8 
        OR unique_devices < invite_count * 0.8
        OR avg_recharge_time < 10)
```

#### 2. **异常行为检测**
- **注册时间模式**：检测批量注册的时间规律
- **充值行为模式**：检测异常的充值金额和时间
- **活跃度检测**：检测账号的真实使用情况
- **消费行为分析**：分析用户的真实消费需求

#### 3. **风控规则引擎**
```javascript
// 风控规则配置
const riskRules = [
    {
        name: "同设备多账号",
        condition: "device_fingerprint_count > 3",
        action: "FREEZE_COMMISSION",
        severity: "HIGH"
    },
    {
        name: "快速注册充值",
        condition: "register_to_recharge_time < 300", // 5分钟内
        action: "MANUAL_REVIEW",
        severity: "MEDIUM"
    },
    {
        name: "异常邀请频率",
        condition: "daily_invite_count > 10",
        action: "LIMIT_INVITE",
        severity: "MEDIUM"
    }
];
```

### 📊 数据层面防护

#### 1. **新增防刷字段**
需要在现有表中添加以下字段来支持防刷功能：

```sql
-- aicg_user_profile 表新增字段
ALTER TABLE aicg_user_profile ADD COLUMN device_fingerprint VARCHAR(255) COMMENT '设备指纹';
ALTER TABLE aicg_user_profile ADD COLUMN ip_address VARCHAR(50) COMMENT '注册IP地址';
ALTER TABLE aicg_user_profile ADD COLUMN geo_location VARCHAR(100) COMMENT '地理位置';
ALTER TABLE aicg_user_profile ADD COLUMN real_name_verified TINYINT(1) DEFAULT 0 COMMENT '实名认证状态';
ALTER TABLE aicg_user_profile ADD COLUMN id_card_number VARCHAR(18) COMMENT '身份证号码';
ALTER TABLE aicg_user_profile ADD COLUMN risk_score INT DEFAULT 0 COMMENT '风险评分';
ALTER TABLE aicg_user_profile ADD COLUMN account_status TINYINT(1) DEFAULT 1 COMMENT '账号状态：1-正常，2-风控，3-冻结';

-- aicg_user_referral 表新增字段
ALTER TABLE aicg_user_referral ADD COLUMN ip_address VARCHAR(50) COMMENT '邀请时IP地址';
ALTER TABLE aicg_user_referral ADD COLUMN device_fingerprint VARCHAR(255) COMMENT '设备指纹';
ALTER TABLE aicg_user_referral ADD COLUMN risk_level TINYINT(1) DEFAULT 1 COMMENT '风险等级：1-低，2-中，3-高';
ALTER TABLE aicg_user_referral ADD COLUMN review_status TINYINT(1) DEFAULT 1 COMMENT '审核状态：1-待审核，2-通过，3-拒绝';
ALTER TABLE aicg_user_referral ADD COLUMN review_time DATETIME COMMENT '审核时间';
ALTER TABLE aicg_user_referral ADD COLUMN review_reason VARCHAR(200) COMMENT '审核备注';

-- aicg_user_referral_reward 表新增字段
ALTER TABLE aicg_user_referral_reward ADD COLUMN commission_rate DECIMAL(5,4) COMMENT '佣金比例';
ALTER TABLE aicg_user_referral_reward ADD COLUMN commission_level TINYINT(1) COMMENT '佣金等级：1-新手，2-高级，3-顶级';
ALTER TABLE aicg_user_referral_reward ADD COLUMN settlement_cycle VARCHAR(20) COMMENT '结算周期';
ALTER TABLE aicg_user_referral_reward ADD COLUMN risk_check_status TINYINT(1) DEFAULT 1 COMMENT '风控检查状态';
```

#### 2. **新增防刷专用表**
```sql
-- 设备指纹记录表
CREATE TABLE aicg_device_fingerprint (
    id VARCHAR(32) PRIMARY KEY,
    fingerprint_hash VARCHAR(255) UNIQUE NOT NULL,
    user_count INT DEFAULT 1,
    first_seen_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_seen_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    risk_score INT DEFAULT 0,
    status TINYINT(1) DEFAULT 1 COMMENT '状态：1-正常，2-可疑，3-黑名单',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- IP地址记录表
CREATE TABLE aicg_ip_address_log (
    id VARCHAR(32) PRIMARY KEY,
    ip_address VARCHAR(50) NOT NULL,
    user_count INT DEFAULT 1,
    register_count_today INT DEFAULT 0,
    last_register_time DATETIME,
    geo_location VARCHAR(100),
    isp_info VARCHAR(100),
    risk_level TINYINT(1) DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ip_address (ip_address),
    INDEX idx_last_register_time (last_register_time)
);

-- 风控日志表
CREATE TABLE aicg_risk_control_log (
    id VARCHAR(32) PRIMARY KEY,
    user_id VARCHAR(32) NOT NULL,
    rule_name VARCHAR(100) NOT NULL,
    risk_type VARCHAR(50) NOT NULL,
    risk_level TINYINT(1) NOT NULL,
    trigger_data JSON,
    action_taken VARCHAR(100),
    status TINYINT(1) DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_risk_type (risk_type),
    INDEX idx_create_time (create_time)
);
```

---

## 🔄 业务流程设计

### 📈 佣金等级升级流程
```mermaid
graph TD
    A[用户邀请成功] --> B[检查邀请总数]
    B --> C{达到升级条件?}
    C -->|是| D[风控检查]
    C -->|否| E[保持当前等级]
    D --> F{风控通过?}
    F -->|是| G[升级佣金等级]
    F -->|否| H[标记异常待审核]
    G --> I[发送升级通知]
    H --> J[人工审核]
    J --> K{审核通过?}
    K -->|是| G
    K -->|否| L[维持原等级]
```

### 🛡️ 防刷检测流程
```mermaid
graph TD
    A[用户注册] --> B[记录设备指纹]
    B --> C[记录IP地址]
    C --> D[实时风控检测]
    D --> E{风险评分}
    E -->|低风险| F[正常流程]
    E -->|中风险| G[标记待观察]
    E -->|高风险| H[冻结账号]
    F --> I[邀请关系生效]
    G --> J[人工审核]
    H --> K[风控处理]
```

---

## 🎯 技术实现要点

### 1. **佣金计算服务**
```java
@Service
public class CommissionCalculationService {
    
    public CommissionResult calculateCommission(String referrerId, String orderId, BigDecimal orderAmount) {
        // 1. 获取推荐人信息和等级
        UserProfile referrer = getUserProfile(referrerId);
        CommissionLevel level = getCommissionLevel(referrer);
        
        // 2. 计算佣金比例
        BigDecimal rate = calculateCommissionRate(level, referrer.getMemberType(), referrer.getInviteCount());
        
        // 3. 风控检查
        RiskCheckResult riskResult = riskControlService.checkCommissionRisk(referrerId, orderId);
        if (!riskResult.isPassed()) {
            return CommissionResult.rejected(riskResult.getReason());
        }
        
        // 4. 计算最终佣金
        BigDecimal commission = orderAmount.multiply(rate);
        
        return CommissionResult.success(commission, rate, level);
    }
}
```

### 2. **防刷检测服务**
```java
@Service
public class AntiCheatService {
    
    public RiskAssessment assessRegistrationRisk(RegistrationRequest request) {
        RiskScore score = new RiskScore();
        
        // 设备指纹检查
        score.add(checkDeviceFingerprint(request.getDeviceFingerprint()));
        
        // IP地址检查
        score.add(checkIPAddress(request.getIpAddress()));
        
        // 邀请关系检查
        if (request.getInviteCode() != null) {
            score.add(checkInviteRelation(request.getInviteCode(), request.getIpAddress()));
        }
        
        return new RiskAssessment(score.getTotal(), score.getLevel(), score.getReasons());
    }
}
```

### 3. **等级升级服务**
```java
@Service
public class LevelUpgradeService {
    
    @Transactional
    public void checkAndUpgradeLevel(String userId) {
        UserProfile user = getUserProfile(userId);
        int validInviteCount = getValidInviteCount(userId);
        
        CommissionLevel newLevel = determineCommissionLevel(user.getMemberType(), validInviteCount);
        
        if (newLevel.getLevel() > user.getCommissionLevel()) {
            // 风控检查
            if (antiCheatService.checkUpgradeEligibility(userId)) {
                upgradeUserLevel(userId, newLevel);
                sendUpgradeNotification(userId, newLevel);
            } else {
                markForManualReview(userId, "等级升级风控检查未通过");
            }
        }
    }
}
```

---

## 📱 前端展示设计

### 1. **佣金等级展示**
```vue
<template>
  <div class="commission-level-card">
    <div class="level-header">
      <div class="level-badge" :class="levelClass">
        {{ levelName }}
      </div>
      <div class="commission-rate">
        {{ commissionRate }}% 佣金
      </div>
    </div>
    
    <div class="progress-section">
      <div class="progress-info">
        <span>邀请进度：{{ currentInvites }}/{{ nextLevelRequirement }}</span>
        <span class="next-level">下一等级：{{ nextLevelRate }}%</span>
      </div>
      <a-progress 
        :percent="progressPercent" 
        :stroke-color="progressColor"
        :show-info="false"
      />
    </div>
    
    <div class="benefits-list">
      <h4>当前权益</h4>
      <ul>
        <li v-for="benefit in currentBenefits" :key="benefit">
          <a-icon type="check-circle" style="color: #52c41a" />
          {{ benefit }}
        </li>
      </ul>
    </div>
  </div>
</template>
```

### 2. **收益明细展示**
```vue
<template>
  <div class="earnings-detail">
    <a-table 
      :columns="columns" 
      :data-source="earningsData"
      :pagination="pagination"
    >
      <template slot="status" slot-scope="text, record">
        <a-tag :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>
      </template>
      
      <template slot="commission_rate" slot-scope="text">
        {{ (text * 100).toFixed(2) }}%
      </template>
      
      <template slot="amount" slot-scope="text">
        ¥{{ text.toFixed(2) }}
      </template>
    </a-table>
  </div>
</template>
```

---

## 🗄️ 数据库表结构调整SQL脚本

### 1. **现有表字段扩展**
```sql
-- 扩展用户扩展信息表
ALTER TABLE aicg_user_profile
ADD COLUMN device_fingerprint VARCHAR(255) COMMENT '设备指纹',
ADD COLUMN ip_address VARCHAR(50) COMMENT '注册IP地址',
ADD COLUMN geo_location VARCHAR(100) COMMENT '地理位置',
ADD COLUMN real_name_verified TINYINT(1) DEFAULT 0 COMMENT '实名认证状态',
ADD COLUMN id_card_number VARCHAR(18) COMMENT '身份证号码',
ADD COLUMN risk_score INT DEFAULT 0 COMMENT '风险评分',
ADD COLUMN account_status TINYINT(1) DEFAULT 1 COMMENT '账号状态：1-正常，2-风控，3-冻结',
ADD COLUMN commission_level TINYINT(1) DEFAULT 1 COMMENT '佣金等级：1-新手，2-高级，3-顶级',
ADD COLUMN valid_invite_count INT DEFAULT 0 COMMENT '有效邀请数量',
ADD COLUMN total_commission DECIMAL(10,2) DEFAULT 0.00 COMMENT '累计佣金',
ADD COLUMN available_commission DECIMAL(10,2) DEFAULT 0.00 COMMENT '可提现佣金';

-- 为新字段添加索引
ALTER TABLE aicg_user_profile ADD INDEX idx_device_fingerprint (device_fingerprint);
ALTER TABLE aicg_user_profile ADD INDEX idx_ip_address (ip_address);
ALTER TABLE aicg_user_profile ADD INDEX idx_id_card_number (id_card_number);
ALTER TABLE aicg_user_profile ADD INDEX idx_commission_level (commission_level);

-- 扩展推荐关系表
ALTER TABLE aicg_user_referral
ADD COLUMN ip_address VARCHAR(50) COMMENT '邀请时IP地址',
ADD COLUMN device_fingerprint VARCHAR(255) COMMENT '设备指纹',
ADD COLUMN risk_level TINYINT(1) DEFAULT 1 COMMENT '风险等级：1-低，2-中，3-高',
ADD COLUMN review_status TINYINT(1) DEFAULT 1 COMMENT '审核状态：1-待审核，2-通过，3-拒绝',
ADD COLUMN review_time DATETIME COMMENT '审核时间',
ADD COLUMN review_reason VARCHAR(200) COMMENT '审核备注',
ADD COLUMN is_valid TINYINT(1) DEFAULT 0 COMMENT '是否有效邀请',
ADD COLUMN valid_time DATETIME COMMENT '生效时间';

-- 扩展奖励记录表
ALTER TABLE aicg_user_referral_reward
ADD COLUMN commission_rate DECIMAL(5,4) COMMENT '佣金比例',
ADD COLUMN commission_level TINYINT(1) COMMENT '佣金等级：1-新手，2-高级，3-顶级',
ADD COLUMN settlement_cycle VARCHAR(20) COMMENT '结算周期',
ADD COLUMN risk_check_status TINYINT(1) DEFAULT 1 COMMENT '风控检查状态',
ADD COLUMN order_id VARCHAR(32) COMMENT '关联订单ID',
ADD COLUMN order_amount DECIMAL(10,2) COMMENT '订单金额',
ADD COLUMN settlement_time DATETIME COMMENT '结算时间',
ADD COLUMN withdraw_time DATETIME COMMENT '提现时间';
```

### 2. **新增防刷专用表**
```sql
-- 设备指纹记录表
CREATE TABLE aicg_device_fingerprint (
    id VARCHAR(32) PRIMARY KEY,
    fingerprint_hash VARCHAR(255) UNIQUE NOT NULL COMMENT '设备指纹哈希',
    user_count INT DEFAULT 1 COMMENT '关联用户数',
    first_seen_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
    last_seen_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后出现时间',
    risk_score INT DEFAULT 0 COMMENT '风险评分',
    status TINYINT(1) DEFAULT 1 COMMENT '状态：1-正常，2-可疑，3-黑名单',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_fingerprint_hash (fingerprint_hash),
    INDEX idx_status (status)
) COMMENT='设备指纹记录表';

-- IP地址记录表
CREATE TABLE aicg_ip_address_log (
    id VARCHAR(32) PRIMARY KEY,
    ip_address VARCHAR(50) NOT NULL COMMENT 'IP地址',
    user_count INT DEFAULT 1 COMMENT '关联用户数',
    register_count_today INT DEFAULT 0 COMMENT '今日注册数',
    last_register_time DATETIME COMMENT '最后注册时间',
    geo_location VARCHAR(100) COMMENT '地理位置',
    isp_info VARCHAR(100) COMMENT 'ISP信息',
    risk_level TINYINT(1) DEFAULT 1 COMMENT '风险等级：1-低，2-中，3-高',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ip_address (ip_address),
    INDEX idx_last_register_time (last_register_time),
    INDEX idx_risk_level (risk_level)
) COMMENT='IP地址记录表';

-- 风控日志表
CREATE TABLE aicg_risk_control_log (
    id VARCHAR(32) PRIMARY KEY,
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    risk_type VARCHAR(50) NOT NULL COMMENT '风险类型',
    risk_level TINYINT(1) NOT NULL COMMENT '风险等级：1-低，2-中，3-高',
    trigger_data JSON COMMENT '触发数据',
    action_taken VARCHAR(100) COMMENT '采取的行动',
    status TINYINT(1) DEFAULT 1 COMMENT '状态：1-待处理，2-已处理，3-已忽略',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_risk_type (risk_type),
    INDEX idx_create_time (create_time),
    INDEX idx_status (status)
) COMMENT='风控日志表';

-- 佣金等级配置表
CREATE TABLE aicg_commission_level_config (
    id VARCHAR(32) PRIMARY KEY,
    level TINYINT(1) NOT NULL COMMENT '等级：1-新手，2-高级，3-顶级',
    member_type VARCHAR(20) NOT NULL COMMENT '会员类型：NORMAL,VIP,SVIP',
    base_rate DECIMAL(5,4) NOT NULL COMMENT '基础佣金比例',
    invite_requirement INT NOT NULL COMMENT '邀请人数要求',
    level_name VARCHAR(50) NOT NULL COMMENT '等级名称',
    level_description TEXT COMMENT '等级描述',
    benefits JSON COMMENT '等级权益',
    status TINYINT(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_level_member (level, member_type),
    INDEX idx_level (level),
    INDEX idx_member_type (member_type)
) COMMENT='佣金等级配置表';

-- 插入默认佣金等级配置
INSERT INTO aicg_commission_level_config (id, level, member_type, base_rate, invite_requirement, level_name, level_description, benefits) VALUES
('1', 1, 'NORMAL', 0.3000, 0, '新手推广员', '普通用户基础等级', '["30%基础佣金", "推广素材下载", "基础数据统计"]'),
('2', 2, 'NORMAL', 0.4000, 10, '高级推广员', '邀请10人成功后升级', '["40%佣金比例", "专属推广海报", "详细数据分析", "优先客服支持"]'),
('3', 3, 'NORMAL', 0.5000, 30, '顶级推广员', '邀请30人成功后升级', '["50%最高佣金", "定制推广素材", "实时数据看板", "专属客户经理"]'),
('4', 1, 'VIP', 0.3500, 0, 'VIP推广员', 'VIP会员基础等级', '["35%基础佣金", "VIP专属素材", "高级数据统计"]'),
('5', 2, 'VIP', 0.4500, 10, 'VIP高级推广员', 'VIP会员邀请10人后升级', '["45%佣金比例", "VIP专属海报", "深度数据分析", "VIP客服通道"]'),
('6', 3, 'VIP', 0.5000, 30, 'VIP顶级推广员', 'VIP会员邀请30人后升级', '["50%最高佣金", "VIP定制素材", "专业数据看板", "VIP客户经理"]'),
('7', 1, 'SVIP', 0.5000, 0, 'SVIP推广员', 'SVIP会员直接享受最高佣金', '["50%最高佣金", "SVIP专属素材", "全功能数据看板", "SVIP专属服务"]'),
('8', 2, 'SVIP', 0.5000, 10, 'SVIP高级推广员', 'SVIP会员等级', '["50%最高佣金", "SVIP高级素材", "专业数据分析", "SVIP优先服务"]'),
('9', 3, 'SVIP', 0.5000, 30, 'SVIP顶级推广员', 'SVIP会员最高等级', '["50%最高佣金", "SVIP定制服务", "企业级数据看板", "专属客户经理"]');
```

---

## 🔌 API接口设计

### 1. **佣金管理接口**
```java
@RestController
@RequestMapping("/api/commission")
public class CommissionController {

    /**
     * 获取用户佣金等级信息
     */
    @GetMapping("/level/{userId}")
    public Result<CommissionLevelVO> getCommissionLevel(@PathVariable String userId) {
        CommissionLevelVO levelInfo = commissionService.getCommissionLevel(userId);
        return Result.ok(levelInfo);
    }

    /**
     * 计算订单佣金
     */
    @PostMapping("/calculate")
    public Result<CommissionCalculationVO> calculateCommission(@RequestBody CommissionCalculationDTO dto) {
        CommissionCalculationVO result = commissionService.calculateCommission(dto);
        return Result.ok(result);
    }

    /**
     * 获取佣金收益明细
     */
    @GetMapping("/earnings/{userId}")
    public Result<IPage<CommissionEarningsVO>> getCommissionEarnings(
            @PathVariable String userId,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        IPage<CommissionEarningsVO> earnings = commissionService.getCommissionEarnings(userId, current, size);
        return Result.ok(earnings);
    }

    /**
     * 申请佣金提现
     */
    @PostMapping("/withdraw")
    public Result<String> applyWithdraw(@RequestBody WithdrawApplicationDTO dto) {
        String applicationId = commissionService.applyWithdraw(dto);
        return Result.ok(applicationId);
    }

    /**
     * 获取佣金统计数据
     */
    @GetMapping("/statistics/{userId}")
    public Result<CommissionStatisticsVO> getCommissionStatistics(@PathVariable String userId) {
        CommissionStatisticsVO statistics = commissionService.getCommissionStatistics(userId);
        return Result.ok(statistics);
    }
}
```

### 2. **防刷检测接口**
```java
@RestController
@RequestMapping("/api/anti-cheat")
public class AntiCheatController {

    /**
     * 注册风险评估
     */
    @PostMapping("/assess-registration")
    public Result<RiskAssessmentVO> assessRegistrationRisk(@RequestBody RegistrationRiskDTO dto) {
        RiskAssessmentVO assessment = antiCheatService.assessRegistrationRisk(dto);
        return Result.ok(assessment);
    }

    /**
     * 邀请关系风险检查
     */
    @PostMapping("/check-referral")
    public Result<ReferralRiskCheckVO> checkReferralRisk(@RequestBody ReferralRiskCheckDTO dto) {
        ReferralRiskCheckVO result = antiCheatService.checkReferralRisk(dto);
        return Result.ok(result);
    }

    /**
     * 获取用户风险评分
     */
    @GetMapping("/risk-score/{userId}")
    public Result<UserRiskScoreVO> getUserRiskScore(@PathVariable String userId) {
        UserRiskScoreVO riskScore = antiCheatService.getUserRiskScore(userId);
        return Result.ok(riskScore);
    }

    /**
     * 风控日志查询
     */
    @GetMapping("/risk-logs/{userId}")
    public Result<IPage<RiskControlLogVO>> getRiskControlLogs(
            @PathVariable String userId,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        IPage<RiskControlLogVO> logs = antiCheatService.getRiskControlLogs(userId, current, size);
        return Result.ok(logs);
    }
}
```

### 3. **推荐管理接口扩展**
```java
@RestController
@RequestMapping("/api/referral")
public class ReferralController {

    /**
     * 获取推荐统计信息（包含等级进度）
     */
    @GetMapping("/statistics/{userId}")
    public Result<ReferralStatisticsVO> getReferralStatistics(@PathVariable String userId) {
        ReferralStatisticsVO statistics = referralService.getReferralStatistics(userId);
        return Result.ok(statistics);
    }

    /**
     * 获取推荐关系列表（包含风险状态）
     */
    @GetMapping("/list/{userId}")
    public Result<IPage<ReferralRelationVO>> getReferralList(
            @PathVariable String userId,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer riskLevel) {
        IPage<ReferralRelationVO> referrals = referralService.getReferralList(userId, current, size, riskLevel);
        return Result.ok(referrals);
    }

    /**
     * 检查等级升级资格
     */
    @PostMapping("/check-upgrade/{userId}")
    public Result<LevelUpgradeCheckVO> checkLevelUpgrade(@PathVariable String userId) {
        LevelUpgradeCheckVO result = referralService.checkLevelUpgrade(userId);
        return Result.ok(result);
    }

    /**
     * 手动触发等级升级检查
     */
    @PostMapping("/trigger-upgrade/{userId}")
    public Result<String> triggerLevelUpgrade(@PathVariable String userId) {
        referralService.triggerLevelUpgrade(userId);
        return Result.ok("等级升级检查已触发");
    }
}
```

---

## 📊 数据模型定义

### 1. **VO对象定义**
```java
// 佣金等级信息VO
@Data
public class CommissionLevelVO {
    private Integer currentLevel;           // 当前等级
    private String levelName;              // 等级名称
    private BigDecimal commissionRate;     // 佣金比例
    private Integer currentInvites;        // 当前邀请数
    private Integer nextLevelRequirement;  // 下一等级要求
    private BigDecimal nextLevelRate;      // 下一等级佣金比例
    private Integer progressPercent;       // 进度百分比
    private List<String> currentBenefits; // 当前权益
    private List<String> nextBenefits;    // 下一等级权益
    private Boolean canUpgrade;            // 是否可升级
    private String upgradeMessage;         // 升级提示信息
}

// 佣金收益明细VO
@Data
public class CommissionEarningsVO {
    private String id;                     // 记录ID
    private String orderId;               // 订单ID
    private String refereeNickname;       // 被推荐人昵称
    private BigDecimal orderAmount;       // 订单金额
    private BigDecimal commissionRate;    // 佣金比例
    private BigDecimal commissionAmount;  // 佣金金额
    private Integer rewardType;           // 奖励类型
    private String rewardTypeName;        // 奖励类型名称
    private Integer status;               // 状态
    private String statusName;            // 状态名称
    private LocalDateTime createTime;     // 创建时间
    private LocalDateTime settlementTime; // 结算时间
    private Integer riskCheckStatus;      // 风控状态
}

// 风险评估结果VO
@Data
public class RiskAssessmentVO {
    private Integer riskScore;            // 风险评分
    private String riskLevel;            // 风险等级
    private List<String> riskReasons;    // 风险原因
    private Boolean passed;              // 是否通过
    private String action;               // 建议行动
    private Map<String, Object> details; // 详细信息
}
```

这个完整的方案涵盖了分层佣金体系的所有方面，您觉得还需要补充或调整哪些部分？
