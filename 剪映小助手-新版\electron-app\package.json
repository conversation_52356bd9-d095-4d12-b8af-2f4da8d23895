{"name": "<PERSON><PERSON><PERSON>-assistant", "version": "1.0.3", "description": "剪映小助手 - 智界AigcView出品", "main": "src/main/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "start-win": "chcp 65001 && electron .", "dev-win": "chcp 65001 && electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "version:update": "node scripts/update-version.js", "version:check": "node scripts/check-version-consistency.js"}, "keywords": ["剪映", "视频编辑", "草稿导入", "智界AigcView"], "author": "智界AigcView", "license": "MIT", "devDependencies": {"electron": "^22.0.0", "electron-builder": "^24.0.0"}, "dependencies": {"@volcengine/tos-sdk": "^2.7.4", "axios": "^1.6.0", "electron-store": "^8.1.0", "node-fetch": "^3.3.0", "yauzl": "^2.10.0"}, "build": {"appId": "com.aigcview.jianying-assistant", "productName": "剪映小助手-智界", "copyright": "Copyright © 2025 智界AigcView", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.png", "publisherName": "智界AigcView"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "剪映小助手-智界"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}