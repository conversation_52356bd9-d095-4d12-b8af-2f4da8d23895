<template>
  <WebsitePage>
    <div class="affiliate-container">
      <!-- 简洁页面标题 -->
      <div class="simple-header">
        <h1 class="simple-title">分销推广</h1>
        <p class="simple-subtitle">加入分销计划，推广智界AIGC获得丰厚佣金</p>
        <div class="commission-badge">
          <span class="badge-text">当前佣金率：{{ currentCommissionRate }}%</span>
          <span class="badge-level">{{ commissionLevelText }}</span>
        </div>
      </div>

      <!-- 分销内容区域 -->
      <section class="affiliate-section">
        <div class="container">
          <!-- 推广链接区域 - 最显眼位置 -->
          <div class="promotion-link-section">
            <h2 class="section-title">您的专属推广链接</h2>
            <div class="link-main-container">
              <div class="link-input-large">
                <a-input
                  :value="affiliateLink || '正在生成推广链接...'"
                  readonly
                  :loading="loading"
                  size="large"
                  placeholder="推广链接生成中..."
                />
              </div>
              <div class="link-actions">
                <a-button
                  type="primary"
                  size="large"
                  :disabled="!affiliateLink || loading"
                  @click="copyLink"
                  class="copy-btn"
                >
                  <a-icon type="copy" />
                  复制链接
                </a-button>
                <a-button
                  size="large"
                  :loading="qrLoading"
                  @click="generateQRCode"
                  class="qr-btn"
                >
                  <a-icon type="qrcode" />
                  生成二维码
                </a-button>
                <a-button
                  size="large"
                  @click="shareLink"
                  class="share-btn"
                >
                  <a-icon type="share-alt" />
                  分享链接
                </a-button>
              </div>
            </div>
            <div class="link-tips">
              <a-icon type="info-circle" />
              分享此链接，好友注册并订阅会员后，您将获得 <strong>{{ currentCommissionRate }}%</strong> 的佣金奖励
            </div>
          </div>

          <!-- 收益展示 -->
          <div class="earnings-dashboard">
            <h2 class="section-title">收益概览</h2>
            <div class="earnings-grid">
              <div class="earning-card primary">
                <div class="card-icon">
                  <a-icon type="dollar" />
                </div>
                <div class="card-content">
                  <a-spin :spinning="loading" size="small">
                    <div class="earning-number">¥{{ formatNumber(totalEarnings) }}</div>
                    <div class="earning-label">累计收益</div>
                  </a-spin>
                </div>
              </div>

              <div class="earning-card success">
                <div class="card-icon">
                  <a-icon type="wallet" />
                </div>
                <div class="card-content">
                  <a-spin :spinning="loading" size="small">
                    <div class="earning-number">¥{{ formatNumber(availableEarnings) }}</div>
                    <div class="earning-label">可提现金额</div>
                  </a-spin>
                  <div class="card-action">
                    <a-button
                      type="primary"
                      size="small"
                      :disabled="availableEarnings <= 0 || loading"
                      @click="openWithdrawModal"
                    >
                      立即提现
                    </a-button>
                  </div>
                </div>
              </div>

              <div class="earning-card info">
                <div class="card-icon">
                  <a-icon type="team" />
                </div>
                <div class="card-content">
                  <a-spin :spinning="loading" size="small">
                    <div class="earning-number">{{ formatNumber(totalReferrals) }}</div>
                    <div class="earning-label">推荐注册</div>
                  </a-spin>
                </div>
              </div>

              <div class="earning-card warning">
                <div class="card-icon">
                  <a-icon type="crown" />
                </div>
                <div class="card-content">
                  <a-spin :spinning="loading" size="small">
                    <div class="earning-number">{{ formatNumber(memberReferrals) }}</div>
                    <div class="earning-label">转化人数</div>
                  </a-spin>
                </div>
              </div>

              <div class="earning-card warning">
                <div class="card-icon">
                  <a-icon type="percentage" />
                </div>
                <div class="card-content">
                  <a-spin :spinning="loading" size="small">
                    <div class="earning-number">{{ conversionRate }}%</div>
                    <div class="earning-label">转化率</div>
                  </a-spin>
                </div>
              </div>
            </div>
          </div>

          <!-- 佣金等级进度 -->
          <div class="commission-progress">
            <h2 class="section-title">佣金等级进度</h2>
            <div class="progress-card">
              <div class="current-level">
                <div class="level-info">
                  <span class="level-name">{{ commissionLevelText }}</span>
                  <span class="level-rate">{{ currentCommissionRate }}%佣金</span>
                </div>
                <div class="level-progress">
                  <a-progress
                    :percent="levelProgress"
                    :stroke-color="progressColor"
                    :show-info="false"
                  />
                  <div class="progress-text">
                    {{ memberReferrals }}/{{ nextLevelRequirement }} 转化用户
                  </div>
                </div>
              </div>
              <div class="next-level" v-if="nextLevelRequirement > 0">
                <div class="next-info">
                  <span class="next-text">下一等级：{{ nextLevelText }}</span>
                  <span class="next-rate">{{ nextLevelRate }}%佣金</span>
                </div>
                <div class="remaining">
                  还需 {{ nextLevelRequirement - memberReferrals }} 个转化用户
                </div>
              </div>
            </div>
          </div>

          <!-- 分成规则说明 -->
          <div class="commission-rules">
            <h2 class="section-title">分成规则</h2>
            <div class="rules-table">
              <div class="rule-row header">
                <div class="rule-cell">用户等级</div>
                <div class="rule-cell">推广人数要求</div>
                <div class="rule-cell">佣金比例</div>
                <div class="rule-cell">说明</div>
              </div>
              <div class="rule-row">
                <div class="rule-cell">普通用户</div>
                <div class="rule-cell">0-9人</div>
                <div class="rule-cell highlight">30%</div>
                <div class="rule-cell">新手推广员</div>
              </div>
              <div class="rule-row">
                <div class="rule-cell">普通用户</div>
                <div class="rule-cell">10-29人</div>
                <div class="rule-cell highlight">40%</div>
                <div class="rule-cell">高级推广员</div>
              </div>
              <div class="rule-row">
                <div class="rule-cell">普通用户</div>
                <div class="rule-cell">30人以上</div>
                <div class="rule-cell highlight">50%</div>
                <div class="rule-cell">顶级推广员</div>
              </div>
              <div class="rule-row vip">
                <div class="rule-cell">VIP用户</div>
                <div class="rule-cell">基础+5%</div>
                <div class="rule-cell highlight">35%-50%</div>
                <div class="rule-cell">VIP推广员</div>
              </div>
              <div class="rule-row svip">
                <div class="rule-cell">SVIP用户</div>
                <div class="rule-cell">无要求</div>
                <div class="rule-cell highlight">50%</div>
                <div class="rule-cell">SVIP推广员</div>
              </div>
            </div>
          </div>

          <!-- 推广用户列表 -->
          <div class="referral-users">
            <h2 class="section-title">我的推广用户</h2>
            <div class="users-table-container">
              <a-table
                :columns="userColumns"
                :data-source="referralUsers"
                :loading="usersLoading"
                :pagination="{ pageSize: 10, showSizeChanger: false }"
                size="middle"
              >
                <template slot="avatar" slot-scope="text, record">
                  <a-avatar :src="record.avatar" :style="{ backgroundColor: '#87d068' }">
                    {{ record.nickname ? record.nickname.charAt(0) : 'U' }}
                  </a-avatar>
                </template>
                <template slot="status" slot-scope="text">
                  <a-tag :color="text === '已转化' ? 'green' : 'blue'">
                    {{ text }}
                  </a-tag>
                </template>
                <template slot="reward" slot-scope="text">
                  <span class="reward-amount">¥{{ text || '0.00' }}</span>
                </template>
              </a-table>
            </div>
          </div>

          <!-- 提现记录 -->
          <div class="withdraw-records">
            <h2 class="section-title">提现记录</h2>
            <div class="records-table-container">
              <a-table
                :columns="withdrawColumns"
                :data-source="withdrawRecords"
                :loading="recordsLoading"
                :pagination="{ pageSize: 10, showSizeChanger: false }"
                size="middle"
              >
                <template slot="status" slot-scope="text">
                  <a-tag :color="getStatusColor(text)">
                    {{ text }}
                  </a-tag>
                </template>
                <template slot="amount" slot-scope="text">
                  <span class="withdraw-amount">¥{{ text }}</span>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </section>

      <!-- 二维码弹窗 -->
      <a-modal
        v-model="showQRModal"
        title="推广二维码"
        :footer="null"
        width="400px"
        centered
      >
        <div class="qr-modal-content">
          <div class="qr-code-container" v-if="qrCodeUrl">
            <img :src="qrCodeUrl" alt="推广二维码" class="qr-code-image" />
          </div>
          <div class="qr-actions">
            <a-button type="primary" block @click="downloadQRCode" v-if="qrCodeUrl">
              <a-icon type="download" />
              下载二维码
            </a-button>
          </div>
        </div>
      </a-modal>

      <!-- 提现弹窗 -->
      <a-modal
        v-model="showWithdrawModal"
        title="申请提现"
        :footer="null"
        width="500px"
        centered
      >
        <div class="withdraw-modal-content">
          <div class="withdraw-info">
            <div class="info-item">
              <span class="info-label">可提现金额：</span>
              <span class="info-value">¥{{ formatNumber(availableEarnings) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">最低提现金额：</span>
              <span class="info-value">¥100.00</span>
            </div>
          </div>

          <a-form :form="withdrawForm" @submit="handleWithdraw">
            <a-form-item label="提现金额">
              <a-input-number
                v-decorator="['amount', {
                  rules: [
                    { required: true, message: '请输入提现金额' },
                    { type: 'number', min: 100, message: '最低提现金额为100元' },
                    { type: 'number', max: availableEarnings, message: '提现金额不能超过可提现金额' }
                  ]
                }]"
                :min="100"
                :max="availableEarnings"
                :precision="2"
                style="width: 100%"
                placeholder="请输入提现金额"
              >
                <template slot="addonAfter">元</template>
              </a-input-number>
            </a-form-item>

            <a-form-item label="提现方式">
              <a-select
                v-decorator="['method', {
                  rules: [{ required: true, message: '请选择提现方式' }],
                  initialValue: 'alipay'
                }]"
                placeholder="请选择提现方式"
              >
                <a-select-option value="alipay">支付宝</a-select-option>
                <a-select-option value="wechat">微信</a-select-option>
                <a-select-option value="bank">银行卡</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="收款账号">
              <a-input
                v-decorator="['account', {
                  rules: [{ required: true, message: '请输入收款账号' }]
                }]"
                placeholder="请输入收款账号"
              />
            </a-form-item>

            <a-form-item label="收款人姓名">
              <a-input
                v-decorator="['name', {
                  rules: [{ required: true, message: '请输入收款人姓名' }]
                }]"
                placeholder="请输入收款人姓名"
              />
            </a-form-item>
          </a-form>

          <div class="withdraw-actions">
            <a-button @click="showWithdrawModal = false" style="margin-right: 8px">
              取消
            </a-button>
            <a-button
              type="primary"
              :loading="withdrawLoading"
              @click="handleWithdraw"
            >
              申请提现
            </a-button>
          </div>
        </div>
      </a-modal>
    </div>
  </WebsitePage>
</template>

<script>
import WebsitePage from '@/components/website/WebsitePage.vue'
import { getReferralStats, generateReferralLink } from '@/api/usercenter'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import Vue from 'vue'

export default {
  name: 'Affiliate',
  components: {
    WebsitePage
  },
  data() {
    return {
      loading: true,
      qrLoading: false,

      // 收益数据
      totalEarnings: 0,
      availableEarnings: 0,
      totalReferrals: 0,
      memberReferrals: 0,
      conversionRate: 0,

      // 推广链接
      affiliateLink: '',

      // 佣金等级
      userRole: 'NORMAL', // NORMAL, VIP, SVIP
      currentCommissionRate: 30,
      commissionLevelText: '新手推广员',
      levelProgress: 0,
      nextLevelRequirement: 10,
      nextLevelText: '高级推广员',
      nextLevelRate: 40,
      progressColor: '#1890ff',

      // 二维码
      showQRModal: false,
      qrCodeUrl: '',

      // 提现
      showWithdrawModal: false,
      withdrawLoading: false,
      withdrawForm: this.$form.createForm(this),

      // 推广用户列表
      referralUsers: [],
      usersLoading: false,
      userColumns: [
        {
          title: '头像',
          dataIndex: 'avatar',
          key: 'avatar',
          scopedSlots: { customRender: 'avatar' },
          width: 80
        },
        {
          title: '用户昵称',
          dataIndex: 'nickname',
          key: 'nickname'
        },
        {
          title: '注册时间',
          dataIndex: 'registerTime',
          key: 'registerTime'
        },
        {
          title: '转化状态',
          dataIndex: 'status',
          key: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '获得奖励',
          dataIndex: 'reward',
          key: 'reward',
          scopedSlots: { customRender: 'reward' }
        }
      ],

      // 提现记录
      withdrawRecords: [],
      recordsLoading: false,
      withdrawColumns: [
        {
          title: '提现金额',
          dataIndex: 'amount',
          key: 'amount',
          scopedSlots: { customRender: 'amount' }
        },
        {
          title: '提现方式',
          dataIndex: 'method',
          key: 'method'
        },
        {
          title: '申请时间',
          dataIndex: 'applyTime',
          key: 'applyTime'
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '完成时间',
          dataIndex: 'completeTime',
          key: 'completeTime'
        }
      ],

      // 用户信息
      userInfo: null
    }
  },
  async mounted() {
    await this.checkLoginAndLoadData()
  },
  methods: {
    // 检查登录状态并加载数据
    async checkLoginAndLoadData() {
      const token = Vue.ls.get(ACCESS_TOKEN)
      if (!token) {
        this.$router.push({ path: '/login', query: { redirect: this.$route.fullPath } })
        return
      }

      try {
        await Promise.all([
          this.loadReferralData(),
          this.loadReferralLink(),
          this.loadUserRole(),
          this.loadReferralUsers(),
          this.loadWithdrawRecords()
        ])

        // 计算佣金等级
        this.calculateCommissionLevel()
      } catch (error) {
        console.error('加载分销数据失败:', error)
        this.$notification.error({
          message: '加载失败',
          description: '获取分销数据失败，请稍后重试',
          placement: 'topRight'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载推荐统计数据
    async loadReferralData() {
      try {
        const response = await getReferralStats()
        if (response.success) {
          const data = response.result
          this.totalEarnings = data.total_reward_amount || 0
          this.availableEarnings = data.available_rewards || 0
          this.totalReferrals = data.total_referrals || 0
          this.memberReferrals = data.member_referrals || 0

          // 计算转化率：转化数 / 总注册数 * 100
          this.conversionRate = this.totalReferrals > 0 ?
            ((this.memberReferrals) / this.totalReferrals * 100).toFixed(1) : 0
        }
      } catch (error) {
        console.error('获取推荐统计失败:', error)
        throw error
      }
    },

    // 加载推荐链接
    async loadReferralLink() {
      try {
        const response = await generateReferralLink({})
        if (response.success) {
          this.affiliateLink = response.result || ''
        }
      } catch (error) {
        console.error('获取推荐链接失败:', error)
        // 如果获取失败，使用默认链接格式
        this.affiliateLink = `${window.location.origin}?ref=loading...`
      }
    },

    // 加载用户角色信息
    async loadUserRole() {
      try {
        // TODO: 从用户信息API获取用户角色
        // 暂时使用默认值
        this.userRole = 'NORMAL'
      } catch (error) {
        console.error('获取用户角色失败:', error)
        this.userRole = 'NORMAL'
      }
    },

    // 计算佣金等级和进度
    calculateCommissionLevel() {
      const memberCount = this.memberReferrals

      if (this.userRole === 'SVIP') {
        this.currentCommissionRate = 50
        this.commissionLevelText = 'SVIP推广员'
        this.levelProgress = 100
        this.nextLevelRequirement = 0
        this.nextLevelText = '已达最高等级'
        this.nextLevelRate = 50
        this.progressColor = '#722ed1'
      } else if (this.userRole === 'VIP') {
        if (memberCount >= 30) {
          this.currentCommissionRate = 50
          this.commissionLevelText = 'VIP顶级推广员'
          this.levelProgress = 100
          this.nextLevelRequirement = 0
          this.nextLevelText = '已达最高等级'
          this.nextLevelRate = 50
          this.progressColor = '#722ed1'
        } else if (memberCount >= 10) {
          this.currentCommissionRate = 45
          this.commissionLevelText = 'VIP高级推广员'
          this.levelProgress = (memberCount / 30) * 100
          this.nextLevelRequirement = 30
          this.nextLevelText = 'VIP顶级推广员'
          this.nextLevelRate = 50
          this.progressColor = '#13c2c2'
        } else {
          this.currentCommissionRate = 35
          this.commissionLevelText = 'VIP推广员'
          this.levelProgress = (memberCount / 10) * 100
          this.nextLevelRequirement = 10
          this.nextLevelText = 'VIP高级推广员'
          this.nextLevelRate = 45
          this.progressColor = '#1890ff'
        }
      } else {
        // NORMAL用户
        if (memberCount >= 30) {
          this.currentCommissionRate = 50
          this.commissionLevelText = '顶级推广员'
          this.levelProgress = 100
          this.nextLevelRequirement = 0
          this.nextLevelText = '已达最高等级'
          this.nextLevelRate = 50
          this.progressColor = '#722ed1'
        } else if (memberCount >= 10) {
          this.currentCommissionRate = 40
          this.commissionLevelText = '高级推广员'
          this.levelProgress = (memberCount / 30) * 100
          this.nextLevelRequirement = 30
          this.nextLevelText = '顶级推广员'
          this.nextLevelRate = 50
          this.progressColor = '#13c2c2'
        } else {
          this.currentCommissionRate = 30
          this.commissionLevelText = '新手推广员'
          this.levelProgress = (memberCount / 10) * 100
          this.nextLevelRequirement = 10
          this.nextLevelText = '高级推广员'
          this.nextLevelRate = 40
          this.progressColor = '#1890ff'
        }
      }
    },

     // 复制推广链接
     copyLink() {
      if (!this.affiliateLink) {
        this.$notification.warning({
          message: '推广链接未生成',
          description: '推广链接正在生成中，请稍后再试',
          placement: 'topRight'
        })
        return
      }
      
      navigator.clipboard.writeText(this.affiliateLink).then(() => {
        this.$notification.success({
          message: '推广链接已复制',
          description: '推广链接已成功复制到剪贴板，快去分享给好友吧！',
          placement: 'topRight',
          duration: 3,
          style: {
            width: '380px',
            marginTop: '101px',
            borderRadius: '8px',
            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'
          }
        })
      }).catch(() => {
        this.$notification.error({
          message: '复制失败',
          description: '复制推广链接失败，请手动复制',
          placement: 'topRight'
        })
      })
    },

    // 生成推广二维码
    async generateQRCode() {
      if (!this.affiliateLink) {
        this.$notification.warning({
          message: '推广链接未生成',
          description: '请等待推广链接生成完成后再生成二维码',
          placement: 'topRight'
        })
        return
      }

      try {
        this.qrLoading = true

        // 调用后端API生成二维码并上传到TOS
        const response = await this.$http.post('/jeecg-boot/api/usercenter/generateReferralQRCode', {
          url: this.affiliateLink
        }, {
          params: {
            url: this.affiliateLink
          }
        })

        if (response.data.success) {
          // 使用CDN地址
          this.qrCodeUrl = response.data.result
          this.showQRModal = true

          this.$notification.success({
            message: '二维码生成成功',
            description: '推广二维码已生成并存储到CDN，可以下载保存',
            placement: 'topRight'
          })
        } else {
          throw new Error(response.data.message || '生成失败')
        }
      } catch (error) {
        console.error('生成二维码失败:', error)
        this.$notification.error({
          message: '生成失败',
          description: error.message || '二维码生成失败，请稍后重试',
          placement: 'topRight'
        })
      } finally {
        this.qrLoading = false
      }
    },

    // 下载二维码
    downloadQRCode() {
      if (!this.qrCodeUrl) return

      const link = document.createElement('a')
      link.href = this.qrCodeUrl
      link.download = '智界AIGC推广二维码.png'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$notification.success({
        message: '下载成功',
        description: '二维码已保存到本地',
        placement: 'topRight'
      })
    },

    // 显示提现弹窗
    openWithdrawModal() {
      if (this.availableEarnings < 100) {
        this.$notification.warning({
          message: '提现金额不足',
          description: '最低提现金额为100元，请继续推广获得更多收益',
          placement: 'topRight'
        })
        return
      }
      this.showWithdrawModal = true
    },

    // 处理提现申请
    handleWithdraw() {
      this.withdrawForm.validateFields((err, values) => {
        if (err) return

        this.withdrawLoading = true

        // TODO: 调用提现API
        setTimeout(() => {
          this.withdrawLoading = false
          this.showWithdrawModal = false
          this.withdrawForm.resetFields()

          this.$notification.success({
            message: '提现申请成功',
            description: '您的提现申请已提交，预计1-3个工作日到账',
            placement: 'topRight'
          })
        }, 2000)
      })
    },

    // 分享链接
    shareLink() {
      if (!this.affiliateLink) {
        this.$notification.warning({
          message: '推广链接未生成',
          description: '推广链接正在生成中，请稍后再试',
          placement: 'topRight'
        })
        return
      }

      // 使用Web Share API或复制到剪贴板
      if (navigator.share) {
        navigator.share({
          title: '智界AIGC - AI创作平台',
          text: '发现一个超棒的AI创作平台，快来体验吧！',
          url: this.affiliateLink
        })
      } else {
        this.copyLink()
      }
    },

    // 加载推广用户列表
    async loadReferralUsers() {
      try {
        this.usersLoading = true
        // TODO: 调用API获取推广用户列表
        // 模拟数据
        this.referralUsers = [
          {
            key: '1',
            nickname: '用户***1',
            avatar: '',
            registerTime: '2024-01-15 10:30:00',
            status: '已转化',
            reward: '29.90'
          },
          {
            key: '2',
            nickname: '用户***2',
            avatar: '',
            registerTime: '2024-01-14 15:20:00',
            status: '已注册',
            reward: '0.00'
          }
        ]
      } catch (error) {
        console.error('获取推广用户列表失败:', error)
      } finally {
        this.usersLoading = false
      }
    },

    // 加载提现记录
    async loadWithdrawRecords() {
      try {
        this.recordsLoading = true
        // TODO: 调用API获取提现记录
        // 模拟数据
        this.withdrawRecords = [
          {
            key: '1',
            amount: '100.00',
            method: '支付宝',
            applyTime: '2024-01-10 14:30:00',
            status: '已完成',
            completeTime: '2024-01-12 09:15:00'
          },
          {
            key: '2',
            amount: '200.00',
            method: '微信',
            applyTime: '2024-01-08 16:45:00',
            status: '处理中',
            completeTime: '-'
          }
        ]
      } catch (error) {
        console.error('获取提现记录失败:', error)
      } finally {
        this.recordsLoading = false
      }
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        '已完成': 'green',
        '处理中': 'blue',
        '已拒绝': 'red',
        '待审核': 'orange'
      }
      return colorMap[status] || 'default'
    },

     // 格式化数字显示
     formatNumber(num) {
       if (num === null || num === undefined) return '0'
       const number = parseFloat(num)
       if (isNaN(number)) return '0'
       
       // 如果是金额，保留两位小数
       if (num === this.totalEarnings) {
         return number.toLocaleString('zh-CN', {
           minimumFractionDigits: 2,
           maximumFractionDigits: 2
         })
       }
       
       // 其他数字不保留小数
       return number.toLocaleString('zh-CN')
     }
   }
 }
</script>

<style scoped>
.affiliate-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  min-height: 100vh;
  padding: 2rem 0;
}

/* 简洁页面标题 */
.simple-header {
  text-align: center;
  padding: 2rem 0 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.simple-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.simple-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0 0 1.5rem 0;
}

.commission-badge {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: white;
  padding: 12px 24px;
  border-radius: 50px;
  border: 2px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.badge-text {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.badge-level {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* 分销内容区域 */
.affiliate-section {
  padding: 4rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

/* 收益仪表板 */
.earnings-dashboard {
  background: white;
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.earnings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
}

.earning-card {
  display: flex;
  align-items: center;
  padding: 24px;
  border-radius: 16px;
  background: white;
  border: 2px solid #f1f5f9;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.earning-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--card-color);
}

.earning-card.primary {
  --card-color: #3b82f6;
}

.earning-card.success {
  --card-color: #10b981;
}

.earning-card.warning {
  --card-color: #f59e0b;
}

.earning-card.info {
  --card-color: #8b5cf6;
}

.earning-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: var(--card-color);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
  background: var(--card-color);
}

.card-content {
  flex: 1;
}

.earning-number {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
  line-height: 1;
}

.earning-label {
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
}

/* 佣金等级进度 */
.commission-progress {
  background: white;
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.progress-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  padding: 32px;
  border: 2px solid #e2e8f0;
}

.current-level {
  margin-bottom: 24px;
}

.level-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.level-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f2937;
}

.level-rate {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.level-progress {
  margin-bottom: 8px;
}

.progress-text {
  text-align: center;
  font-size: 0.9rem;
  color: #6b7280;
  margin-top: 8px;
}

.next-level {
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.next-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.next-text {
  font-size: 1rem;
  color: #374151;
  font-weight: 500;
}

.next-rate {
  background: #f3f4f6;
  color: #6b7280;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
}

.remaining {
  font-size: 0.9rem;
  color: #9ca3af;
  text-align: center;
}

/* 推广工具 */
.tools-section {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
}

.tool-card {
  background: #fafbfc;
  border: 2px solid #f1f5f9;
  border-radius: 16px;
  padding: 32px;
  transition: all 0.3s ease;
}

.tool-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.15);
}

.tool-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
}

.tool-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.tool-info h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1f2937;
}

.tool-info p {
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

.tool-content {
  margin-top: 16px;
}

.link-input {
  width: 100%;
}

/* 二维码弹窗 */
.qr-modal-content {
  text-align: center;
}

.qr-code-container {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #d1d5db;
}

.qr-code-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.qr-actions {
  margin-top: 16px;
}

/* 提现弹窗 */
.withdraw-modal-content {
  padding: 8px 0;
}

.withdraw-info {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #64748b;
  font-size: 0.9rem;
}

.info-value {
  color: #1e293b;
  font-weight: 600;
  font-size: 1rem;
}

.withdraw-actions {
  text-align: right;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f1f5f9;
}

.card-action {
  margin-top: 8px;
}

/* 推广链接区域 */
.promotion-link-section {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 2px solid #e2e8f0;
}

.link-main-container {
  margin-bottom: 1rem;
}

.link-input-large {
  margin-bottom: 1rem;
}

.link-input-large .ant-input {
  font-size: 1rem;
  padding: 12px 16px;
  border-radius: 8px;
  border: 2px solid #e2e8f0;
}

.link-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.copy-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  font-weight: 600;
}

.qr-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  color: white;
  font-weight: 600;
}

.share-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  color: white;
  font-weight: 600;
}

.link-tips {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 12px 16px;
  color: #0369a1;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.link-tips strong {
  color: #1e40af;
  font-weight: 700;
}

/* 分成规则表格 */
.commission-rules {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.rules-table {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
}

.rule-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  border-bottom: 1px solid #e2e8f0;
}

.rule-row:last-child {
  border-bottom: none;
}

.rule-row.header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  font-weight: 700;
  color: #1e293b;
}

.rule-row.vip {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.rule-row.svip {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
}

.rule-cell {
  padding: 16px;
  text-align: center;
  border-right: 1px solid #e2e8f0;
}

.rule-cell:last-child {
  border-right: none;
}

.rule-cell.highlight {
  font-weight: 700;
  color: #dc2626;
  font-size: 1.1rem;
}

/* 表格容器 */
.users-table-container,
.records-table-container {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.referral-users,
.withdraw-records {
  margin-bottom: 3rem;
}

.reward-amount,
.withdraw-amount {
  font-weight: 600;
  color: #059669;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }

  .commission-badge {
    flex-direction: column;
    gap: 8px;
  }

  .earnings-grid {
    grid-template-columns: 1fr;
  }

  .tools-grid {
    grid-template-columns: 1fr;
  }

  .earnings-dashboard,
  .commission-progress,
  .tools-section {
    padding: 24px;
  }

  .tool-card {
    padding: 24px;
  }

  .progress-card {
    padding: 24px;
  }

  .level-info,
  .next-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .tool-header {
    flex-direction: column;
    text-align: center;
  }

  .tool-icon {
    margin: 0 auto 16px;
  }
}

@media (max-width: 480px) {
  .affiliate-section {
    padding: 0 16px 60px;
  }

  .page-header {
    padding: 40px 16px 24px;
  }

  .earning-card {
    flex-direction: column;
    text-align: center;
  }

  .card-icon {
    margin: 0 auto 12px;
  }

  .link-actions {
    flex-direction: column;
  }

  .rule-row {
    grid-template-columns: 1fr;
    text-align: left;
  }

  .rule-cell {
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
    text-align: left;
  }

  .rule-cell:last-child {
    border-bottom: none;
  }

  .promotion-link-section,
  .commission-rules,
  .users-table-container,
  .records-table-container {
    padding: 1.5rem;
  }
}
</style>
