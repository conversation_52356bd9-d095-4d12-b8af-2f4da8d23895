package org.jeecg.modules.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 支付宝配置类
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "alipay")
public class AlipayConfig {

    /**
     * 支付宝应用ID
     */
    private String appId;

    /**
     * 应用私钥（用于签名）
     */
    private String privateKey;

    /**
     * 支付宝公钥（用于验签）
     */
    private String alipayPublicKey;

    /**
     * 签名类型
     */
    private String signType = "RSA2";

    /**
     * 字符编码
     */
    private String charset = "UTF-8";

    /**
     * 支付宝网关地址
     * 正式环境：https://openapi.alipay.com/gateway.do
     * 沙箱环境：https://openapi.alipaydev.com/gateway.do
     */
    private String gatewayUrl = "https://openapi.alipay.com/gateway.do";

    /**
     * 数据格式
     */
    private String format = "json";

    /**
     * 异步通知地址
     */
    private String notifyUrl;

    /**
     * 同步返回地址
     */
    private String returnUrl;

    /**
     * 是否沙箱环境
     */
    private boolean sandbox = false;

    /**
     * 获取完整的异步通知地址
     */
    public String getFullNotifyUrl() {
        return notifyUrl;
    }

    /**
     * 获取完整的同步返回地址
     */
    public String getFullReturnUrl() {
        return returnUrl;
    }
}
