package org.jeecg.modules.system.service.impl;

import org.jeecg.modules.system.service.IUserRegisterService;
import org.jeecg.modules.system.service.IAicgVerifyCodeService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.jeecg.modules.system.service.IInviteCodeGeneratorService;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.demo.referral.service.IAicgUserReferralService;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.api.dto.message.MessageDTO;
import org.jeecg.modules.system.dto.RegisterDTO;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.demo.referral.entity.AicgUserReferral;
import org.jeecg.modules.system.config.RegisterConfig;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.regex.Pattern;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * @Description: 用户注册服务实现类
 * @Author: jeecg-boot
 * @Date: 2025-06-19
 * @Version: V1.0
 */
@Service
@Slf4j
public class UserRegisterServiceImpl implements IUserRegisterService {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysUserRoleService sysUserRoleService;

    @Autowired
    private IAicgUserProfileService userProfileService;

    @Autowired
    private IAicgUserReferralService userReferralService;

    @Autowired
    private IInviteCodeGeneratorService inviteCodeGeneratorService;

    @Autowired
    private IAicgVerifyCodeService verifyCodeService;

    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    private RegisterConfig registerConfig;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> register(RegisterDTO registerDTO, String ipAddress) {
        try {
            // 1. 基础验证
            Result<?> validationResult = validateRegisterData(registerDTO, ipAddress);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }

            // 2. 验证验证码（微信注册除外）
            if (!"wechat".equals(registerDTO.getType())) {
                boolean codeValid = false;
                if ("phone".equals(registerDTO.getType())) {
                    codeValid = verifyCodeService.verifyCode(registerDTO.getPhone(), 
                        registerDTO.getVerifyCode(), "sms", "register");
                } else if ("email".equals(registerDTO.getType())) {
                    codeValid = verifyCodeService.verifyCode(registerDTO.getEmail(), 
                        registerDTO.getVerifyCode(), "email", "register");
                }

                if (!codeValid) {
                    return Result.error("验证码错误或已过期");
                }
            }

            // 3. 创建用户
            SysUser newUser = createSysUser(registerDTO);
            sysUserService.save(newUser);

            // 4. 分配默认角色
            assignDefaultRole(newUser.getId());

            // 5. 创建用户扩展信息
            AicgUserProfile userProfile = createUserProfile(newUser, registerDTO);
            userProfileService.save(userProfile);

            // 6. 处理邀请关系
            if (oConvertUtils.isNotEmpty(registerDTO.getInviteCode())) {
                handleInviteRelation(newUser.getId(), registerDTO.getInviteCode());
            }

            // 7. 发送密码修改提醒通知
            sendPasswordChangeNotification(newUser.getId(), registerDTO.getType());

            log.info("用户注册成功，用户ID：{}，注册方式：{}", newUser.getId(), registerDTO.getType());
            return Result.OK("注册成功", newUser.getId());

        } catch (Exception e) {
            log.error("用户注册失败：{}", e.getMessage(), e);
            return Result.error("注册失败，请稍后重试");
        }
    }

    @Override
    public Result<?> checkUsername(String username, String type) {
        try {
            SysUser existUser = null;
            
            if ("phone".equals(type)) {
                existUser = sysUserService.getUserByPhone(username);
                if (existUser != null) {
                    return Result.error("手机号已被注册");
                }
            } else if ("email".equals(type)) {
                existUser = sysUserService.getUserByEmail(username);
                if (existUser != null) {
                    return Result.error("邮箱已被注册");
                }
            }

            return Result.OK("用户名可用");
        } catch (Exception e) {
            log.error("检查用户名失败：{}", e.getMessage(), e);
            return Result.error("检查失败，请稍后重试");
        }
    }

    @Override
    public Result<?> validateInviteCode(String inviteCode) {
        try {
            if (oConvertUtils.isEmpty(inviteCode)) {
                return Result.error("邀请码不能为空");
            }

            // 查询邀请码是否存在
            AicgUserProfile inviter = userProfileService.getByInviteCode(inviteCode);
            if (inviter == null) {
                return Result.error("邀请码无效");
            }

            return Result.OK("邀请码有效", inviter.getNickname());
        } catch (Exception e) {
            log.error("验证邀请码失败：{}", e.getMessage(), e);
            return Result.error("验证失败，请稍后重试");
        }
    }

    @Override
    public String generateInviteCode(String userId) {
        try {
            // 使用新的统一邀请码生成服务
            // 这个服务会自动检查用户是否已有邀请码，如果有则返回现有的，如果没有则生成新的ZJ格式邀请码
            String inviteCode = inviteCodeGeneratorService.generateOrGetInviteCode(userId);
            log.info("为用户 {} 生成/获取邀请码: {}", userId, inviteCode);
            return inviteCode;
        } catch (Exception e) {
            log.error("使用新服务生成邀请码失败，回退到旧逻辑，用户: {}, 错误: {}", userId, e.getMessage());
            // 如果新服务失败，回退到旧的生成逻辑确保系统稳定性
            return generateInviteCodeFallback(userId);
        }
    }

    /**
     * 备用邀请码生成方法（保持原有逻辑作为回退方案）
     */
    private String generateInviteCodeFallback(String userId) {
        String inviteCode;
        int maxAttempts = 10;
        int attempts = 0;

        do {
            inviteCode = generateRandomInviteCode();
            attempts++;
        } while (userProfileService.getByInviteCode(inviteCode) != null && attempts < maxAttempts);

        if (attempts >= maxAttempts) {
            // 如果随机生成失败，使用用户ID生成
            inviteCode = "INV" + userId.substring(0, Math.min(5, userId.length())).toUpperCase();
        }

        log.warn("使用备用邀请码生成逻辑，用户: {}, 邀请码: {}", userId, inviteCode);
        return inviteCode;
    }

    @Override
    public boolean validatePassword(String password) {
        if (oConvertUtils.isEmpty(password)) {
            return false;
        }

        // 检查长度
        if (password.length() < registerConfig.getPassword().getMinLength()) {
            return false;
        }

        // 检查是否包含字母
        if (registerConfig.getPassword().getRequireLetter()) {
            if (!Pattern.compile("[a-zA-Z]").matcher(password).find()) {
                return false;
            }
        }

        // 检查是否包含数字
        if (registerConfig.getPassword().getRequireNumber()) {
            if (!Pattern.compile("[0-9]").matcher(password).find()) {
                return false;
            }
        }

        // 检查是否包含特殊字符
        if (registerConfig.getPassword().getRequireSpecial()) {
            if (!Pattern.compile("[^a-zA-Z0-9]").matcher(password).find()) {
                return false;
            }
        }

        return true;
    }

    @Override
    public boolean canRegister(String ipAddress, String phone, String email) {
        // TODO: 实现注册频率限制检查
        // 这里可以查询数据库，检查同一IP、手机号、邮箱的注册频率
        return true;
    }

    /**
     * 验证注册数据
     */
    private Result<?> validateRegisterData(RegisterDTO registerDTO, String ipAddress) {
        // 检查注册类型
        if (!"phone".equals(registerDTO.getType()) && 
            !"email".equals(registerDTO.getType()) && 
            !"wechat".equals(registerDTO.getType())) {
            return Result.error("不支持的注册类型");
        }

        // 检查用户名重复
        Result<?> usernameCheck = null;
        if ("phone".equals(registerDTO.getType())) {
            usernameCheck = checkUsername(registerDTO.getPhone(), "phone");
        } else if ("email".equals(registerDTO.getType())) {
            usernameCheck = checkUsername(registerDTO.getEmail(), "email");
        }

        if (usernameCheck != null && !usernameCheck.isSuccess()) {
            return usernameCheck;
        }

        // 检查密码强度（微信注册除外）
        if (!"wechat".equals(registerDTO.getType())) {
            if (!validatePassword(registerDTO.getPassword())) {
                return Result.error("密码强度不够，至少8位且包含字母和数字");
            }

            if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
                return Result.error("两次输入的密码不一致");
            }
        }

        // 检查邀请码
        if (oConvertUtils.isNotEmpty(registerDTO.getInviteCode())) {
            Result<?> inviteCheck = validateInviteCode(registerDTO.getInviteCode());
            if (!inviteCheck.isSuccess()) {
                return inviteCheck;
            }
        }

        // 检查注册频率限制
        if (!canRegister(ipAddress, registerDTO.getPhone(), registerDTO.getEmail())) {
            return Result.error("注册过于频繁，请稍后重试");
        }

        return Result.OK();
    }

    /**
     * 创建系统用户
     */
    private SysUser createSysUser(RegisterDTO registerDTO) {
        SysUser user = new SysUser();
        
        // 设置用户名
        if ("phone".equals(registerDTO.getType())) {
            user.setUsername(registerDTO.getPhone());
            user.setPhone(registerDTO.getPhone());
            user.setPhoneVerified(1);
        } else if ("email".equals(registerDTO.getType())) {
            user.setUsername(registerDTO.getEmail());
            user.setEmail(registerDTO.getEmail());
            user.setEmailVerified(1);
        } else if ("wechat".equals(registerDTO.getType())) {
            user.setUsername("wx_" + System.currentTimeMillis());
            if (registerDTO.getWechatInfo() != null) {
                user.setWechatOpenid(registerDTO.getWechatInfo().getOpenid());
                user.setWechatUnionid(registerDTO.getWechatInfo().getUnionid());
                user.setRealname(registerDTO.getWechatInfo().getNickname());
                user.setAvatar(registerDTO.getWechatInfo().getAvatar());
            }
        }

        // 设置密码
        if (oConvertUtils.isNotEmpty(registerDTO.getPassword())) {
            String salt = oConvertUtils.randomGen(8);
            user.setSalt(salt);
            String passwordEncode = PasswordUtil.encrypt(user.getUsername(), registerDTO.getPassword(), salt);
            user.setPassword(passwordEncode);
        }

        // 设置其他信息
        String defaultNickname = generateUniqueNickname(registerDTO.getNickname());
        user.setRealname(defaultNickname);
        user.setStatus(1);
        user.setDelFlag(0);
        user.setCreateTime(new Date());
        user.setRegisterSource(registerDTO.getType());
        user.setIsAuthor(0); // 默认不是作者

        return user;
    }

    /**
     * 分配默认角色
     */
    private void assignDefaultRole(String userId) {
        // 分配普通用户角色
        sysUserRoleService.addUserRole(userId, "user");
    }

    /**
     * 创建用户扩展信息
     */
    private AicgUserProfile createUserProfile(SysUser user, RegisterDTO registerDTO) {
        AicgUserProfile profile = new AicgUserProfile();
        profile.setUserId(user.getId());
        profile.setUsername(user.getUsername());
        profile.setNickname(user.getRealname());
        profile.setPhone(user.getPhone());
        profile.setEmail(user.getEmail());
        profile.setAvatar(user.getAvatar());
        profile.setAccountBalance(BigDecimal.ZERO);
        profile.setTotalConsumption(BigDecimal.ZERO);
        profile.setTotalRecharge(BigDecimal.ZERO);
        profile.setInviteCount(0);
        profile.setRegisterSource(registerDTO.getType());
        profile.setCreateTime(new Date());

        // 生成邀请码
        String myInviteCode = generateInviteCode(user.getId());
        profile.setMyInviteCode(myInviteCode);

        // 自动生成API Key
        String apiKey = generateApiKey();
        profile.setApiKey(apiKey);

        // 设置密码修改状态
        if ("wechat".equals(registerDTO.getType())) {
            // 微信注册：如果没有设置密码，标记为未修改
            profile.setPasswordChanged(oConvertUtils.isEmpty(registerDTO.getPassword()) ? 0 : 1);
        } else {
            // 手机号/邮箱注册：使用系统生成的随机密码，标记为未修改
            profile.setPasswordChanged(0);
        }

        // 设置使用的邀请码
        if (oConvertUtils.isNotEmpty(registerDTO.getInviteCode())) {
            profile.setUsedInviteCode(registerDTO.getInviteCode());
        }

        return profile;
    }

    /**
     * 生成唯一昵称
     * 如果用户没有提供昵称，则生成"智界用户" + 6位随机字符
     * 如果昵称重复，则自动添加后缀
     */
    private String generateUniqueNickname(String inputNickname) {
        String baseNickname;

        // 如果用户提供了昵称，使用用户提供的；否则使用默认的"智界用户"
        if (oConvertUtils.isNotEmpty(inputNickname)) {
            baseNickname = inputNickname.trim();
        } else {
            // 生成6位随机字符（数字+字母）
            String randomSuffix = generateRandomString(6);
            baseNickname = "智界用户" + randomSuffix;
        }

        // 检查昵称是否重复，如果重复则添加后缀
        String finalNickname = baseNickname;
        int suffix = 1;

        while (isNicknameExists(finalNickname)) {
            if (oConvertUtils.isEmpty(inputNickname)) {
                // 如果是系统生成的昵称重复，重新生成随机后缀
                String newRandomSuffix = generateRandomString(6);
                finalNickname = "智界用户" + newRandomSuffix;
            } else {
                // 如果是用户提供的昵称重复，添加数字后缀
                finalNickname = baseNickname + suffix;
                suffix++;
            }

            // 防止无限循环，最多尝试100次
            if (suffix > 100) {
                String randomSuffix = generateRandomString(6);
                finalNickname = "智界用户" + randomSuffix;
                break;
            }
        }

        return finalNickname;
    }

    /**
     * 生成指定长度的随机字符串（数字+字母）
     */
    private String generateRandomString(int length) {
        String chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        StringBuilder sb = new StringBuilder();
        java.util.Random random = new java.util.Random();

        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }

        return sb.toString();
    }

    /**
     * 检查昵称是否已存在（检查用户扩展表）
     */
    private boolean isNicknameExists(String nickname) {
        try {
            List<AicgUserProfile> existingProfiles = userProfileService.list(
                new LambdaQueryWrapper<AicgUserProfile>()
                    .eq(AicgUserProfile::getNickname, nickname)
            );
            return existingProfiles != null && !existingProfiles.isEmpty();
        } catch (Exception e) {
            log.error("检查昵称重复性失败：{}", e.getMessage(), e);
            // 如果检查失败，为了安全起见，认为昵称已存在
            return true;
        }
    }

    /**
     * 处理邀请关系
     */
    private void handleInviteRelation(String newUserId, String inviteCode) {
        try {
            // 查找邀请人
            AicgUserProfile inviter = userProfileService.getByInviteCode(inviteCode);
            if (inviter != null) {
                // 设置邀请人ID
                AicgUserProfile newUserProfile = userProfileService.getByUserId(newUserId);
                if (newUserProfile != null) {
                    newUserProfile.setInviterUserId(inviter.getUserId());
                    userProfileService.updateById(newUserProfile);
                }

                // 创建推荐关系记录
                AicgUserReferral referral = new AicgUserReferral();
                referral.setReferrerId(inviter.getUserId());
                referral.setRefereeId(newUserId);
                referral.setReferralCode(inviteCode);
                referral.setRegisterTime(new Date());
                referral.setStatus(1);
                referral.setCreateTime(new Date());
                userReferralService.save(referral);

                // 更新邀请人的邀请统计
                inviter.setInviteCount(inviter.getInviteCount() + 1);
                userProfileService.updateById(inviter);

                log.info("邀请关系建立成功，邀请人：{}，被邀请人：{}", inviter.getUserId(), newUserId);
            }
        } catch (Exception e) {
            log.error("处理邀请关系失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 生成随机邀请码
     */
    private String generateRandomInviteCode() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder code = new StringBuilder();

        for (int i = 0; i < registerConfig.getInviteCode().getLength(); i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }

        return code.toString();
    }

    /**
     * 生成API密钥
     */
    private String generateApiKey() {
        return "ak_" + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 发送密码修改提醒通知
     */
    private void sendPasswordChangeNotification(String userId, String registerType) {
        try {
            String title = "安全提醒：建议修改登录密码";
            String content = buildPasswordChangeNotificationContent(registerType);

            // 🛡️ 关键修复：获取用户名而不是直接使用用户ID
            SysUser user = sysUserService.getById(userId);
            if (user == null) {
                log.error("发送密码修改提醒通知失败，用户不存在，用户ID：{}", userId);
                return;
            }

            String username = user.getUsername();
            log.info("🔔 准备发送密码修改提醒通知，用户ID：{}，用户名：{}，注册方式：{}", userId, username, registerType);

            // 使用系统通告发送通知（传入用户名）
            MessageDTO message = new MessageDTO("system", username, title, content);
            sysBaseAPI.sendSysAnnouncement(message);

            log.info("✅ 已发送密码修改提醒通知，用户ID：{}，用户名：{}，注册方式：{}", userId, username, registerType);
        } catch (Exception e) {
            log.error("❌ 发送密码修改提醒通知失败，用户ID：{}，错误：{}", userId, e.getMessage(), e);
        }
    }

    /**
     * 构建密码修改通知内容
     */
    private String buildPasswordChangeNotificationContent(String registerType) {
        StringBuilder content = new StringBuilder();

        content.append("为了您的账户安全，建议您尽快修改登录密码。\n\n");

        if ("phone".equals(registerType)) {
            content.append("您通过手机号注册，系统已为您生成随机密码。");
        } else if ("email".equals(registerType)) {
            content.append("您通过邮箱注册，系统已为您生成随机密码。");
        } else {
            content.append("您通过第三方登录注册，系统已为您设置默认密码。");
        }

        content.append("为了方便您使用密码登录，请及时修改为您熟悉的密码。\n\n");
        content.append("修改路径：个人中心 → 账户设置 → 安全设置 → 登录密码\n\n");
        content.append("密码要求：至少8位，包含字母和数字\n\n");
        content.append("修改密码后，您可以使用新密码进行登录，提升使用便利性。");

        return content.toString();
    }
}
