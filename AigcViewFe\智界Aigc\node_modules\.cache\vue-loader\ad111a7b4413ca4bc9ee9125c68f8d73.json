{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue?vue&type=template&id=42c31eb0&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue", "mtime": 1753512620053}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"website-login\" },\n    [\n      _vm._m(0),\n      _c(\"WebsiteHeader\"),\n      _c(\"div\", { staticClass: \"login-main\" }, [\n        _c(\"div\", { ref: \"loginInfo\", staticClass: \"login-info\" }, [\n          _c(\"div\", { staticClass: \"info-content\" }, [\n            _c(\"div\", { staticClass: \"brand-showcase\" }, [\n              _c(\n                \"div\",\n                { staticClass: \"brand-logo-large\" },\n                [\n                  _c(\"LogoImage\", {\n                    attrs: {\n                      size: \"large\",\n                      hover: false,\n                      \"container-class\": \"login-logo-container\",\n                      \"image-class\": \"login-logo-image\",\n                      \"fallback-class\": \"login-logo-fallback\"\n                    }\n                  }),\n                  _c(\"h1\", { staticClass: \"brand-title\" }, [_vm._v(\"智界AIGC\")])\n                ],\n                1\n              ),\n              _c(\"p\", { staticClass: \"brand-slogan\" }, [\n                _vm._v(\"AI驱动的内容生成平台\")\n              ])\n            ]),\n            _c(\n              \"div\",\n              { staticClass: \"feature-highlights\" },\n              _vm._l(_vm.features, function(feature, index) {\n                return _c(\"div\", { key: index, staticClass: \"feature-item\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"feature-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: feature.icon } })],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"feature-text\" }, [\n                    _c(\"h3\", [_vm._v(_vm._s(feature.title))]),\n                    _c(\"p\", [_vm._v(_vm._s(feature.description))])\n                  ])\n                ])\n              }),\n              0\n            )\n          ])\n        ]),\n        _c(\"div\", { ref: \"loginContainer\", staticClass: \"login-container\" }, [\n          _c(\"div\", { staticClass: \"login-card\" }, [\n            _c(\"div\", { staticClass: \"login-header\" }, [\n              _c(\"h2\", { staticClass: \"login-title\" }, [\n                _vm._v(\"欢迎使用智界AIGC\")\n              ]),\n              _c(\"p\", { staticClass: \"login-subtitle\" }, [\n                _vm._v(\n                  _vm._s(\n                    _vm.inviteCodeFromUrl\n                      ? \"您正在通过邀请链接登录\"\n                      : \"选择您的登录方式，开启AI创作之旅\"\n                  )\n                )\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"auth-tabs\" }, [\n              _c(\"div\", { staticClass: \"tab-buttons\" }, [\n                _c(\n                  \"button\",\n                  {\n                    class: [\"tab-btn\", { active: _vm.loginType === \"phone\" }],\n                    on: {\n                      click: function($event) {\n                        return _vm.switchLoginType(\"phone\")\n                      }\n                    }\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"mobile\" } }),\n                    _c(\"span\", { staticClass: \"tab-text\" }, [_vm._v(\"手机号\")])\n                  ],\n                  1\n                ),\n                _c(\n                  \"button\",\n                  {\n                    class: [\"tab-btn\", { active: _vm.loginType === \"email\" }],\n                    on: {\n                      click: function($event) {\n                        return _vm.switchLoginType(\"email\")\n                      }\n                    }\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"mail\" } }),\n                    _c(\"span\", { staticClass: \"tab-text\" }, [_vm._v(\"邮箱\")])\n                  ],\n                  1\n                ),\n                _c(\n                  \"button\",\n                  {\n                    class: [\n                      \"tab-btn\",\n                      { active: _vm.loginType === \"password\" }\n                    ],\n                    on: {\n                      click: function($event) {\n                        return _vm.switchLoginType(\"password\")\n                      }\n                    }\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"lock\" } }),\n                    _c(\"span\", { staticClass: \"tab-text\" }, [\n                      _vm._v(\"密码登录\")\n                    ])\n                  ],\n                  1\n                )\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"login-form\" }, [\n              _vm.loginType === \"password\"\n                ? _c(\n                    \"div\",\n                    { staticClass: \"login-content\" },\n                    [\n                      _c(\n                        \"a-form\",\n                        {\n                          staticClass: \"account-login-form\",\n                          attrs: { form: _vm.form },\n                          on: { submit: _vm.handleSubmit }\n                        },\n                        [\n                          _c(\n                            \"div\",\n                            { staticClass: \"input-group\" },\n                            [\n                              _c(\n                                \"a-form-item\",\n                                [\n                                  _c(\n                                    \"a-input\",\n                                    {\n                                      directives: [\n                                        {\n                                          name: \"decorator\",\n                                          rawName: \"v-decorator\",\n                                          value: [\n                                            \"username\",\n                                            {\n                                              rules: [\n                                                {\n                                                  required: true,\n                                                  message: \"请输入用户名或邮箱\"\n                                                }\n                                              ]\n                                            }\n                                          ],\n                                          expression:\n                                            \"['username', { rules: [{ required: true, message: '请输入用户名或邮箱' }] }]\"\n                                        }\n                                      ],\n                                      staticClass: \"clean-input\",\n                                      attrs: {\n                                        size: \"large\",\n                                        placeholder: \"用户名或邮箱\"\n                                      }\n                                    },\n                                    [\n                                      _c(\"a-icon\", {\n                                        attrs: { slot: \"prefix\", type: \"user\" },\n                                        slot: \"prefix\"\n                                      })\n                                    ],\n                                    1\n                                  )\n                                ],\n                                1\n                              )\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"div\",\n                            { staticClass: \"input-group\" },\n                            [\n                              _c(\n                                \"a-form-item\",\n                                [\n                                  _c(\n                                    \"a-input-password\",\n                                    {\n                                      directives: [\n                                        {\n                                          name: \"decorator\",\n                                          rawName: \"v-decorator\",\n                                          value: [\n                                            \"password\",\n                                            {\n                                              rules: [\n                                                {\n                                                  required: true,\n                                                  message: \"请输入密码\"\n                                                }\n                                              ]\n                                            }\n                                          ],\n                                          expression:\n                                            \"['password', { rules: [{ required: true, message: '请输入密码' }] }]\"\n                                        }\n                                      ],\n                                      staticClass: \"clean-input\",\n                                      attrs: {\n                                        size: \"large\",\n                                        placeholder: \"密码\"\n                                      }\n                                    },\n                                    [\n                                      _c(\"a-icon\", {\n                                        attrs: { slot: \"prefix\", type: \"lock\" },\n                                        slot: \"prefix\"\n                                      })\n                                    ],\n                                    1\n                                  )\n                                ],\n                                1\n                              )\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"div\",\n                            { staticClass: \"input-group\" },\n                            [\n                              _c(\"a-form-item\", [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"captcha-row\" },\n                                  [\n                                    _c(\n                                      \"a-input\",\n                                      {\n                                        directives: [\n                                          {\n                                            name: \"decorator\",\n                                            rawName: \"v-decorator\",\n                                            value: [\n                                              \"inputCode\",\n                                              {\n                                                rules: [\n                                                  {\n                                                    required: true,\n                                                    message: \"请输入验证码\"\n                                                  }\n                                                ]\n                                              }\n                                            ],\n                                            expression:\n                                              \"['inputCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"\n                                          }\n                                        ],\n                                        staticClass:\n                                          \"clean-input captcha-input\",\n                                        attrs: {\n                                          size: \"large\",\n                                          placeholder: \"验证码\"\n                                        }\n                                      },\n                                      [\n                                        _c(\"a-icon\", {\n                                          attrs: {\n                                            slot: \"prefix\",\n                                            type: \"safety-certificate\"\n                                          },\n                                          slot: \"prefix\"\n                                        })\n                                      ],\n                                      1\n                                    ),\n                                    _c(\n                                      \"div\",\n                                      {\n                                        staticClass: \"captcha-image-container\",\n                                        on: { click: _vm.handleChangeCheckCode }\n                                      },\n                                      [\n                                        _c(\"img\", {\n                                          staticClass: \"captcha-image\",\n                                          attrs: {\n                                            src: _vm.randCodeImage,\n                                            alt: \"验证码\"\n                                          }\n                                        }),\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass:\n                                              \"captcha-refresh-overlay\"\n                                          },\n                                          [\n                                            _c(\"a-icon\", {\n                                              attrs: { type: \"reload\" }\n                                            })\n                                          ],\n                                          1\n                                        )\n                                      ]\n                                    )\n                                  ],\n                                  1\n                                )\n                              ])\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"div\",\n                            { staticClass: \"login-options\" },\n                            [\n                              _c(\n                                \"a-checkbox\",\n                                {\n                                  staticClass: \"remember-me\",\n                                  model: {\n                                    value: _vm.rememberMe,\n                                    callback: function($$v) {\n                                      _vm.rememberMe = $$v\n                                    },\n                                    expression: \"rememberMe\"\n                                  }\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n                记住我\\n              \"\n                                  )\n                                ]\n                              ),\n                              _c(\n                                \"a\",\n                                {\n                                  staticClass: \"forgot-link\",\n                                  on: { click: _vm.handleForgotPassword }\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n                忘记密码？\\n              \"\n                                  )\n                                ]\n                              )\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"a-form-item\",\n                            { staticClass: \"login-button-item\" },\n                            [\n                              _c(\n                                \"a-button\",\n                                {\n                                  staticClass: \"login-submit-button\",\n                                  attrs: {\n                                    type: \"primary\",\n                                    \"html-type\": \"submit\",\n                                    size: \"large\",\n                                    loading: _vm.loginLoading,\n                                    block: \"\"\n                                  }\n                                },\n                                [\n                                  !_vm.loginLoading\n                                    ? _c(\"span\", [_vm._v(\"登录\")])\n                                    : _c(\"span\", [_vm._v(\"登录中...\")])\n                                ]\n                              )\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm.loginType === \"phone\"\n                ? _c(\n                    \"div\",\n                    { staticClass: \"login-content\" },\n                    [\n                      _c(\n                        \"a-form\",\n                        {\n                          staticClass: \"phone-login-form\",\n                          attrs: { form: _vm.phoneLoginForm },\n                          on: { submit: _vm.handlePhoneLogin }\n                        },\n                        [\n                          _c(\n                            \"div\",\n                            { staticClass: \"input-group\" },\n                            [\n                              _c(\n                                \"a-form-item\",\n                                [\n                                  _c(\n                                    \"a-input\",\n                                    {\n                                      directives: [\n                                        {\n                                          name: \"decorator\",\n                                          rawName: \"v-decorator\",\n                                          value: [\n                                            \"phone\",\n                                            {\n                                              rules: [\n                                                {\n                                                  required: true,\n                                                  message: \"请输入手机号\"\n                                                },\n                                                {\n                                                  pattern: /^1[3-9]\\d{9}$/,\n                                                  message: \"手机号格式不正确\"\n                                                }\n                                              ]\n                                            }\n                                          ],\n                                          expression:\n                                            \"['phone', { rules: [\\n                      { required: true, message: '请输入手机号' },\\n                      { pattern: /^1[3-9]\\\\d{9}$/, message: '手机号格式不正确' }\\n                    ] }]\"\n                                        }\n                                      ],\n                                      staticClass: \"clean-input\",\n                                      attrs: {\n                                        size: \"large\",\n                                        placeholder: \"请输入手机号\"\n                                      }\n                                    },\n                                    [\n                                      _c(\"a-icon\", {\n                                        attrs: {\n                                          slot: \"prefix\",\n                                          type: \"mobile\"\n                                        },\n                                        slot: \"prefix\"\n                                      })\n                                    ],\n                                    1\n                                  )\n                                ],\n                                1\n                              )\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"div\",\n                            { staticClass: \"input-group\" },\n                            [\n                              _c(\"a-form-item\", [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"verify-code-row\" },\n                                  [\n                                    _c(\n                                      \"a-input\",\n                                      {\n                                        directives: [\n                                          {\n                                            name: \"decorator\",\n                                            rawName: \"v-decorator\",\n                                            value: [\n                                              \"smsCode\",\n                                              {\n                                                rules: [\n                                                  {\n                                                    required: true,\n                                                    message: \"请输入验证码\"\n                                                  }\n                                                ]\n                                              }\n                                            ],\n                                            expression:\n                                              \"['smsCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"\n                                          }\n                                        ],\n                                        staticClass:\n                                          \"clean-input verify-code-input\",\n                                        attrs: {\n                                          size: \"large\",\n                                          placeholder: \"请输入短信验证码\"\n                                        }\n                                      },\n                                      [\n                                        _c(\"a-icon\", {\n                                          attrs: {\n                                            slot: \"prefix\",\n                                            type: \"safety-certificate\"\n                                          },\n                                          slot: \"prefix\"\n                                        })\n                                      ],\n                                      1\n                                    ),\n                                    _c(\n                                      \"a-button\",\n                                      {\n                                        staticClass: \"send-code-btn\",\n                                        attrs: {\n                                          disabled:\n                                            _vm.smsCodeSending ||\n                                            _vm.smsCountdown > 0,\n                                          size: \"large\"\n                                        },\n                                        on: { click: _vm.sendLoginSmsCode }\n                                      },\n                                      [\n                                        _vm._v(\n                                          \"\\n                      \" +\n                                            _vm._s(\n                                              _vm.smsCountdown > 0\n                                                ? _vm.smsCountdown + \"s后重发\"\n                                                : \"发送验证码\"\n                                            ) +\n                                            \"\\n                    \"\n                                        )\n                                      ]\n                                    )\n                                  ],\n                                  1\n                                )\n                              ])\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"a-form-item\",\n                            { staticClass: \"login-button-item\" },\n                            [\n                              _c(\n                                \"a-button\",\n                                {\n                                  staticClass: \"login-submit-button\",\n                                  attrs: {\n                                    type: \"primary\",\n                                    \"html-type\": \"submit\",\n                                    size: \"large\",\n                                    loading: _vm.phoneLoginLoading,\n                                    block: \"\"\n                                  }\n                                },\n                                [\n                                  !_vm.phoneLoginLoading\n                                    ? _c(\"span\", [_vm._v(\"登录\")])\n                                    : _c(\"span\", [_vm._v(\"登录中...\")])\n                                ]\n                              )\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"div\",\n                            { staticClass: \"phone-login-tip\" },\n                            [\n                              _c(\"a-alert\", {\n                                attrs: {\n                                  message: \"手机号登录说明\",\n                                  description:\n                                    \"首次使用手机号登录将自动为您创建账户，无需设置密码\",\n                                  type: \"info\",\n                                  \"show-icon\": \"\"\n                                }\n                              })\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm.loginType === \"email\"\n                ? _c(\n                    \"div\",\n                    { staticClass: \"login-content\" },\n                    [\n                      _c(\n                        \"a-form\",\n                        {\n                          staticClass: \"email-login-form\",\n                          attrs: { form: _vm.emailLoginForm },\n                          on: { submit: _vm.handleEmailLogin }\n                        },\n                        [\n                          _c(\n                            \"div\",\n                            { staticClass: \"input-group\" },\n                            [\n                              _c(\n                                \"a-form-item\",\n                                [\n                                  _c(\n                                    \"a-input\",\n                                    {\n                                      directives: [\n                                        {\n                                          name: \"decorator\",\n                                          rawName: \"v-decorator\",\n                                          value: [\n                                            \"email\",\n                                            {\n                                              rules: [\n                                                {\n                                                  required: true,\n                                                  message: \"请输入邮箱\"\n                                                },\n                                                {\n                                                  type: \"email\",\n                                                  message: \"邮箱格式不正确\"\n                                                }\n                                              ]\n                                            }\n                                          ],\n                                          expression:\n                                            \"['email', { rules: [\\n                      { required: true, message: '请输入邮箱' },\\n                      { type: 'email', message: '邮箱格式不正确' }\\n                    ] }]\"\n                                        }\n                                      ],\n                                      staticClass: \"clean-input\",\n                                      attrs: {\n                                        size: \"large\",\n                                        placeholder: \"请输入邮箱\"\n                                      }\n                                    },\n                                    [\n                                      _c(\"a-icon\", {\n                                        attrs: { slot: \"prefix\", type: \"mail\" },\n                                        slot: \"prefix\"\n                                      })\n                                    ],\n                                    1\n                                  )\n                                ],\n                                1\n                              )\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"div\",\n                            { staticClass: \"input-group\" },\n                            [\n                              _c(\"a-form-item\", [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"verify-code-row\" },\n                                  [\n                                    _c(\n                                      \"a-input\",\n                                      {\n                                        directives: [\n                                          {\n                                            name: \"decorator\",\n                                            rawName: \"v-decorator\",\n                                            value: [\n                                              \"emailCode\",\n                                              {\n                                                rules: [\n                                                  {\n                                                    required: true,\n                                                    message: \"请输入验证码\"\n                                                  }\n                                                ]\n                                              }\n                                            ],\n                                            expression:\n                                              \"['emailCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"\n                                          }\n                                        ],\n                                        staticClass:\n                                          \"clean-input verify-code-input\",\n                                        attrs: {\n                                          size: \"large\",\n                                          placeholder: \"请输入邮箱验证码\"\n                                        }\n                                      },\n                                      [\n                                        _c(\"a-icon\", {\n                                          attrs: {\n                                            slot: \"prefix\",\n                                            type: \"safety-certificate\"\n                                          },\n                                          slot: \"prefix\"\n                                        })\n                                      ],\n                                      1\n                                    ),\n                                    _c(\n                                      \"a-button\",\n                                      {\n                                        staticClass: \"send-code-btn\",\n                                        attrs: {\n                                          disabled:\n                                            _vm.emailCodeSending ||\n                                            _vm.emailCountdown > 0,\n                                          size: \"large\"\n                                        },\n                                        on: { click: _vm.sendLoginEmailCode }\n                                      },\n                                      [\n                                        _vm._v(\n                                          \"\\n                      \" +\n                                            _vm._s(\n                                              _vm.emailCountdown > 0\n                                                ? _vm.emailCountdown + \"s后重发\"\n                                                : \"发送验证码\"\n                                            ) +\n                                            \"\\n                    \"\n                                        )\n                                      ]\n                                    )\n                                  ],\n                                  1\n                                )\n                              ])\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"a-form-item\",\n                            { staticClass: \"login-button-item\" },\n                            [\n                              _c(\n                                \"a-button\",\n                                {\n                                  staticClass: \"login-submit-button\",\n                                  attrs: {\n                                    type: \"primary\",\n                                    \"html-type\": \"submit\",\n                                    size: \"large\",\n                                    loading: _vm.emailLoginLoading,\n                                    block: \"\"\n                                  }\n                                },\n                                [\n                                  !_vm.emailLoginLoading\n                                    ? _c(\"span\", [_vm._v(\"登录\")])\n                                    : _c(\"span\", [_vm._v(\"登录中...\")])\n                                ]\n                              )\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"div\",\n                            { staticClass: \"email-login-tip\" },\n                            [\n                              _c(\"a-alert\", {\n                                attrs: {\n                                  message: \"邮箱登录说明\",\n                                  description:\n                                    \"首次使用邮箱登录将自动为您创建账户，无需设置密码\",\n                                  type: \"info\",\n                                  \"show-icon\": \"\"\n                                }\n                              })\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm.loginType === \"wechat\"\n                ? _c(\"div\", { staticClass: \"login-content\" }, [\n                    _c(\"div\", { staticClass: \"wechat-login-container\" }, [\n                      _c(\"div\", { staticClass: \"wechat-qr-section\" }, [\n                        _c(\"div\", { staticClass: \"qr-code-container\" }, [\n                          _vm.wechatLoginQrCode\n                            ? _c(\"img\", {\n                                staticClass: \"qr-code-image\",\n                                attrs: {\n                                  src: _vm.wechatLoginQrCode,\n                                  alt: \"微信登录二维码\"\n                                }\n                              })\n                            : _c(\n                                \"div\",\n                                { staticClass: \"qr-loading\" },\n                                [\n                                  _c(\"a-spin\", { attrs: { size: \"large\" } }),\n                                  _c(\"p\", [_vm._v(\"正在生成二维码...\")])\n                                ],\n                                1\n                              )\n                        ]),\n                        _c(\"div\", { staticClass: \"qr-instructions\" }, [\n                          _c(\"h4\", [_vm._v(\"使用微信扫码登录\")]),\n                          _c(\"p\", [_vm._v(\"1. 打开微信扫一扫\")]),\n                          _c(\"p\", [_vm._v(\"2. 扫描上方二维码\")]),\n                          _c(\"p\", [_vm._v(\"3. 确认登录\")]),\n                          _vm.inviteCodeFromUrl\n                            ? _c(\"p\", { staticClass: \"invite-tip\" }, [\n                                _vm._v(\"* 您正在通过邀请链接登录\")\n                              ])\n                            : _vm._e()\n                        ])\n                      ])\n                    ])\n                  ])\n                : _vm._e()\n            ])\n          ])\n        ])\n      ])\n    ],\n    1\n  )\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"login-background\" }, [\n      _c(\"div\", { staticClass: \"bg-animated-grid\" }),\n      _c(\"div\", { staticClass: \"bg-floating-elements\" }),\n      _c(\"div\", { staticClass: \"bg-gradient-overlay\" })\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}